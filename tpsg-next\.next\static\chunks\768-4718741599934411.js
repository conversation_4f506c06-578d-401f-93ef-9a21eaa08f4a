"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[768],{6768:function(n,e,t){t.d(e,{Bv:function(){return ArticleLayout},Q4:function(){return PodcastLayout},KB:function(){return WebinarLayout}});var i=t(2729),o=t(5893),r=t(2962),a=t(3071),l=t(1664),c=t.n(l),d=t(6368),s=t(279),p=t(211),u=t(9588),x=t(9521),m=t(7294),h=t(1304),g=t(5158);function _templateObject(){let n=(0,i._)(["\n  position: relative;\n\n  box-shadow: ",";\n  \n  width: 100%;\n  height: 100%;\n  \n  iframe {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 100%;\n    width: 100%;\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  position: absolute;\n  color: #f6f6f6;\n  margin-top: 0;\n  top: 22px;\n  left: 22px;\n  font-size: 22px;\n  font-weight: 500;\n"]);return _templateObject1=function(){return n},n}let YoutubeIframe=n=>{let{video:e}=n,t=(0,g.gc)(e);return(0,o.jsx)("iframe",{src:"https://www.youtube.com/embed/"+t+"?&autoplay=1&mute=1&enablejsapi=1",width:"100%",height:"100%",frameBorder:"0",allow:"fullscreen"})};function VideoPlayer(n){let{video:e,image:t}=n,[i,r]=(0,m.useState)(!1),a=e&&e.length>0;return(0,o.jsxs)(b,{className:"youtube-player-component",hidePlayer:!a,children:[(0,o.jsx)(h.Z,{imageData:t}),a&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(f,{children:"REPLAY"}),(0,o.jsx)(u.P8,{clickAction:()=>r(!0)}),i&&(0,o.jsx)(YoutubeIframe,{video:e})]})]})}let b=x.ZP.div.withConfig({componentId:"sc-aab69a3a-0"})(_templateObject(),n=>n.hidePlayer?"none":"0 16px 48px rgba(60, 60, 60, 0.3);"),f=x.ZP.p.withConfig({componentId:"sc-aab69a3a-1"})(_templateObject1());var C=t(9340),v=t(5874),j=t(7421),w=t(4218),y=t(3617);function PodcastLayout_templateObject(){let n=(0,i._)(["\n  padding-bottom: 70px;\n\n  header {\n    display: flex;\n    flex-direction: column;\n    margin-bottom: 48px;\n  }\n\n  .header-player-cover {\n    width: 100%;\n    aspect-ratio: 16 / 10;\n  }\n\n  .card-label {\n    display: none;\n    margin: 8px 8px 0 0;\n  }\n\n  .video-player {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 100%;\n    width: 100%;\n  }\n\n  .header-img-container {\n    position: relative;\n    width: 50%;\n    aspect-ratio: 16 / 10;\n  }\n\n  @media "," {\n    .card-label {\n      display: inline-block;\n    }\n\n    .header-player-cover {\n      margin-top: 48px;\n    }\n  }\n  @media "," {\n    header {\n      margin-top: 90px;\n      margin-bottom: 70px;\n      flex-direction: row;\n      justify-content: space-between;\n    }\n\n    .header-text-container {\n      position: relative;\n      width: 50%;\n      aspect-ratio: 16 / 10;\n      padding-top: 24px;\n      padding-left: 16px;\n    }\n\n    .header-player-cover {\n      margin-top: 0;\n      width: calc(50% - 32px);\n    }\n  }\n"]);return PodcastLayout_templateObject=function(){return n},n}function PodcastLayout_templateObject1(){let n=(0,i._)(["\n  position: relative;\n  width: 100%;\n\n  .right-content-sticky {\n    position: sticky;\n    top: 50px;\n  }\n\n  .podcast-platform {\n    display: flex;\n    flex-wrap: wrap;\n    margin-top: 64px;\n    width: 100%;\n    gap: 32px;\n  }\n\n  @media "," {\n    .podcast-platform {\n      margin-top: 16px;\n      gap: 16px;\n    }\n  }\n  @media "," {\n    border-left: 1px solid #dddddd;\n    width: 40%;\n    padding-left: 32px;\n  }\n"]);return PodcastLayout_templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  margin-top: 70px;\n  display: block;\n\n  @media "," {\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: space-between;\n  }\n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n\n  @media "," {\n    width: 50%;\n  }\n"]);return _templateObject3=function(){return n},n}function PodcastLayout(n){var e,t;let{episode:i,relatedPosts:l}=n;if(!i)return(0,o.jsx)(o.Fragment,{});let u=(0,w.S$)(i.published_at),{route:x}=i,{lead:m,podcast:h,seo:g}=i.modules,b=null==h?void 0:null===(e=h.podcast)||void 0===e?void 0:e.platforms,f={fullName:(null==h?void 0:h.podcast.name)||"unknown",picture:null==h?void 0:h.podcast.logoSmall,url:"/podcasts/".concat(null==h?void 0:h.podcast.slug)};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(_,{className:"site-padding",children:[(0,o.jsx)(r.PB,{title:(null==g?void 0:g.metaTitle)||i.title,description:(null==g?void 0:g.metaDescription)||null,openGraph:{title:(null==g?void 0:g.metaTitle)||i.title,description:(null==g?void 0:g.metaDescription)||null,url:"https://toutpoursagloire.com".concat(x),images:[{url:(0,a.k)(i.image),alt:(null===(t=i.image)||void 0===t?void 0:t.alternativeText)||""}]},twitter:{site:"@t_p_s_g",cardType:"summary_large_image"}}),(0,o.jsxs)("header",{children:[(0,o.jsxs)("div",{className:"header-text-container",children:[(0,o.jsx)(c(),{href:"/podcasts/".concat(h.podcast.slug),children:(0,o.jsx)(d.hQ,{children:h.podcast.name})}),(0,o.jsx)(d.DZ,{children:i.title}),(0,o.jsx)("div",{children:i.topics&&i.topics.map((n,e)=>(0,o.jsx)(s.Z,{text:n.name},e))})]}),(0,o.jsx)("div",{className:"header-player-cover",children:(0,o.jsx)(p.xr,{children:(0,o.jsx)(VideoPlayer,{video:h.embedVideo,image:i.image})})})]}),(0,o.jsxs)(C.Z,{children:[(0,o.jsx)(C.Z.Text,{label:"Publi\xe9 le",content:u,addClass:""}),(0,o.jsx)(C.Z.Authors,{label:"Podcast",authors:[f],addClass:""}),(0,o.jsx)(C.Z.Social,{url:"https://toutpoursagloire.com/podcasts/".concat(null==h?void 0:h.podcast.slug,"/").concat(i.slug),addClass:"mobile-hide_flex"})]}),(0,o.jsxs)(Z,{children:[(0,o.jsxs)(O,{children:[(null==m?void 0:m.content)&&(0,o.jsx)("section",{children:(0,o.jsx)(p.oJ,{content:m.content})}),(0,o.jsx)("section",{children:(0,o.jsx)(p.oJ,{content:i.body})})]}),(0,o.jsx)(k,{children:(0,o.jsxs)("div",{className:"right-content-sticky",children:[(null==h?void 0:h.embedAudio)&&(0,o.jsx)(p.oJ,{content:"<p>".concat(h.embedAudio,"</p>")}),(0,o.jsx)("div",{className:"podcast-platform",children:b&&b.map((n,e)=>(0,o.jsx)("a",{href:n.url,target:"_blank",rel:"noreferrer",children:(0,o.jsx)(v.Z,{plateform:n.name})},e))})]})})]})]}),(0,o.jsx)(y.Z,{items:l})]})}let _=x.ZP.div.withConfig({componentId:"sc-4ca6bb54-0"})(PodcastLayout_templateObject(),j.U.tablet,j.U.desktop),k=x.ZP.div.withConfig({componentId:"sc-4ca6bb54-1"})(PodcastLayout_templateObject1(),j.U.tablet,j.U.desktop),Z=x.ZP.main.withConfig({componentId:"sc-4ca6bb54-2"})(_templateObject2(),j.U.desktop),O=x.ZP.article.withConfig({componentId:"sc-4ca6bb54-3"})(_templateObject3(),j.U.desktop);var P=t(5582),z=t(9663),L=t(9150),I=t(9562),S=t(669);function md_body_templateObject(){let n=(0,i._)(['\n  font-family: "Lora", Charter, Times, "Times New Roman", serif;\n  font-size: 18px;\n  line-height: 28px;\n  font-weight: 400;\n\n  img {\n    max-width: 100%;\n  }\n\n  h5, h6 {\n    font-size: 18px;\n    line-height: 28px;\n    font-weight: 500;\n    margin-bottom: 0;\n  }\n\n  h1,\n  h2,\n  h3,\n  h4 {\n    font-family: Stelvio, sans-serif;\n    font-weight: 600;\n    margin-bottom: 0;\n  }\n\n  h1,\n  h2 {\n    font-size: 28px;\n    line-height: 32px;\n    margin-top: 32px;\n\n    &:first-child {\n      margin-top: 0;\n    }\n  }\n\n  h3 {\n    font-size: 24px;\n    line-height: 28px;\n    margin-top: 12px;\n  }\n\n  p {\n    font-size: 18px;\n    line-height: 28px;\n    margin-top: 0;\n    margin-bottom: 24px;\n    color: #161616;\n\n    sup {\n      vertical-align: top;\n      position: relative;\n      top: -0.4em;\n      margin-left: 4px;\n      margin-right: 4px;\n    }\n  }\n\n  blockquote {\n    position: relative;\n    margin: 0;\n    padding: 8px 0 8px 36px;\n\n    &::before {\n      content: "“";\n      display: inline-block;\n      position: absolute;\n      font-size: 54px;\n      font-weight: bold;\n      color: #363636;\n      left: 0;\n      top: 18px;\n      width: 100px;\n    }\n  }\n\n  blockquote p {\n    font-size: 18px;\n    line-height: 28px;\n    font-style: italic;\n  }\n\n  em {\n  }\n\n  ul, ol {\n    margin-top: 12px;\n    padding-left: 24px;\n  }\n\n  li {\n    line-height: 170%;\n    margin: 0 0 8px 0;\n  }\n\n  a {\n    color: var(--brand-color) !important;\n    text-decoration: none !important;\n  }\n\n  a:hover {\n    color: var(--brand-color) !important;\n    text-decoration: underline !important;\n  }\n\n  /* Sp\xe9cificit\xe9 renforc\xe9e pour les liens dans les paragraphes */\n  p a {\n    color: var(--brand-color) !important;\n    text-decoration: none !important;\n  }\n  p a:hover {\n    color: var(--brand-color) !important;\n    text-decoration: underline !important;\n  }\n\n  /* Sp\xe9cificit\xe9 renforc\xe9e pour les liens dans les listes */\n  li a {\n    color: var(--brand-color) !important;\n    text-decoration: none !important;\n  }\n  li a:hover {\n    color: var(--brand-color) !important;\n    text-decoration: underline !important;\n  }\n\n  /* Sp\xe9cificit\xe9 renforc\xe9e pour les balises u */\n  u {\n    text-decoration: underline !important;\n  }\n\n  /* Styles pour les balises u \xe0 l\'int\xe9rieur des liens - TOUTES les combinaisons */\n  a u,\n  a em u,\n  a strong u,\n  a span u,\n  a i u,\n  a b u {\n    text-decoration: none !important;\n  }\n  a:hover u,\n  a:hover em u,\n  a:hover strong u,\n  a:hover span u,\n  a:hover i u,\n  a:hover b u {\n    text-decoration: underline !important;\n  }\n\n  /* Sp\xe9cificit\xe9 renforc\xe9e pour les balises u dans les liens des paragraphes */\n  p a u,\n  p a em u,\n  p a strong u,\n  p a span u,\n  p a i u,\n  p a b u {\n    text-decoration: none !important;\n  }\n  p a:hover u,\n  p a:hover em u,\n  p a:hover strong u,\n  p a:hover span u,\n  p a:hover i u,\n  p a:hover b u {\n    text-decoration: underline !important;\n  }\n\n  /* Sp\xe9cificit\xe9 renforc\xe9e pour les balises u dans les liens des listes */\n  li a u,\n  li a em u,\n  li a strong u,\n  li a span u,\n  li a i u,\n  li a b u {\n    text-decoration: none !important;\n  }\n  li a:hover u,\n  li a:hover em u,\n  li a:hover strong u,\n  li a:hover span u,\n  li a:hover i u,\n  li a:hover b u {\n    text-decoration: underline !important;\n  }\n\n  hr {\n    border: none;\n    margin: 40px 0 40px 0;\n\n    &::before {\n      content: "***";\n      display: block;\n      letter-spacing: 10px;\n      text-align: center;\n      color: #161616;\n    }\n  }\n\n  cite {\n    &:before {\n      content: "– ";\n      font-size: 600;\n    }\n  }\n\n  @media '," {\n    max-width: 720px;\n    margin: 80px 0 0 0;\n    h1, h2 {\n      font-size: 32px;\n      line-height: 36px;\n      margin-top: 44px;\n      margin-bottom: 6px;\n    }\n\n    h3, h4 {\n      margin-top: 32px;\n      margin-bottom: 0;\n      font-size: 26px;\n      line-height: 30px;\n    }\n\n    h4 {\n      opacity: 0.6;\n      font-weight: 500;\n    }\n\n    p {\n      color: #323232;\n      font-size: 20px;\n      line-height: 32px;\n      font-weight: 400;\n      margin-bottom: 24px;\n    }\n\n    ul, ol {\n      margin-top: 12px;\n      padding-left: 24px;\n      margin-bottom: 24px;\n      font-size: 20px;\n      line-height: 32px;\n    }\n\n    blockquote p {\n      font-size: 20px;\n      line-height: 32px;\n      font-weight: 500;\n      font-style: italic;\n      margin-bottom: 0;\n    }\n\n    blockquote {\n      margin-bottom: 24px;\n    }\n\n    .post-content-button {\n      display: inline-block;\n      margin-top: 16px;\n      margin-bottom: 16px;\n      padding: 10px 32px;\n      border-radius: 40px;\n      text-align: center;\n      color: white;\n      text-decoration: none;\n      font-family: Switzer, sans-serif;\n      font-weight: 400;\n      background-color: var(--brand-color);\n    }\n  }\n\n  .table-container {\n    position: relative;\n    overflow-x: auto;\n    margin-bottom: 24px;\n    width: 100%;\n    background-image: linear-gradient(to right, white, white), linear-gradient(to right, white, white), linear-gradient(to right, rgba(0, 0, 20, .50), rgba(255, 255, 255, 0)), linear-gradient(to left, rgba(0, 0, 20, .50), rgba(255, 255, 255, 0));\n    /* Shadows */\n    /* Shadow covers */\n    background-position: left center, right center, left center, right center;\n    background-repeat: no-repeat;\n    background-color: white;\n    background-size: 20px 100%, 20px 100%, 10px 100%, 16px 100%;\n    background-attachment: local, local, scroll, scroll;\n  }\n\n  table {\n    font-family: Switzer, sans-serif;\n    border: 1px solid #ccc;\n    border-collapse: collapse;\n    padding: 0;\n    width: 100%;\n    overflow-x: auto;\n  }\n\n  table caption {\n    font-size: 1.5em;\n    margin: .5em 0 .75em;\n  }\n\n  table tr {\n    background-color: rgba(248, 248, 248, 0.9);\n    border: 1px solid #ddd;\n    padding: .35em;\n  }\n\n  table th,\n  table td {\n    font-size: 0.85em;\n    line-height: 1.4em;\n    padding: .625em;\n    text-align: left;\n    min-width: 240px;\n    color: rgba(0, 0, 0, 0.72);\n  }\n\n  table th {\n    font-weight: 600;\n    color: rgba(0, 0, 0, 9);\n  }\n\n  table th {\n    font-size: 0.85em;\n    //font-weight: 500;\n    //text-transform: uppercase;\n  }\n\n  @media screen and (max-width: 600px) {\n\n    .table-container {\n      background: none;\n    }\n    table {\n      border: 0;\n    }\n\n    table caption {\n      font-size: 1.3em;\n    }\n\n    table thead {\n      border: none;\n      clip: rect(0 0 0 0);\n      height: 1px;\n      margin: -1px;\n      overflow: hidden;\n      padding: 0;\n      position: absolute;\n      width: 1px;\n    }\n\n    table tr {\n      border-bottom: 3px solid #ddd;\n      display: block;\n      margin-bottom: .625em;\n    }\n\n    table td {\n      border-bottom: 1px solid #ddd;\n      display: block;\n      font-size: .8em;\n      text-align: left;\n    }\n\n    table td::before {\n      /*\n      * aria-label has no advantage, it won't be read inside a table\n      content: attr(aria-label);\n      */\n      content: attr(data-label);\n      float: left;\n      font-weight: bold;\n      text-transform: uppercase;\n    }\n\n    table td:last-child {\n      border-bottom: 0;\n    }\n  }\n"]);return md_body_templateObject=function(){return n},n}let codeComponent=n=>{let{children:e}=n;return(0,o.jsx)(S.Z,{html:e})},tableComponent=n=>{let{children:e}=n;return(0,o.jsx)("div",{className:"table-container",children:(0,o.jsx)("table",{children:e})})},linkComponent=n=>{let{href:e,children:t,...i}=n;return(0,o.jsx)("a",{href:e,...i,children:t})},underlineComponent=n=>{let{children:e}=n;return(0,o.jsx)("u",{children:e})};function MDPost(n){let{content:e}=n;return(0,o.jsx)(N,{className:"md-post",children:(0,o.jsx)(P.U,{components:{code:codeComponent,table:tableComponent,a:linkComponent,u:underlineComponent},rehypePlugins:[z.Z,I.Z],children:(0,g.k5)(e)})})}let N=x.ZP.div.withConfig({componentId:"sc-bcf8706e-0"})(md_body_templateObject(),j.U.tablet);var B=t(1809),A=t(4012);function PreviewButton_templateObject(){let n=(0,i._)(["\n		font-family: Switzer, sans-serif;\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  height: 60px;\n  width: 100%;\n  background-color: #9fe7d3;\n  border: 54px;\n  display: flex;\n  flex-display: row;\n  align-items: center;\n		padding: 0 30px;\n  background-image: linear-gradient(45deg, #aaebda 25%, #9fe7d3 25%, #9fe7d3 50%, #aaebda 50%, #aaebda 75%, #9fe7d3 75%, #9fe7d3 100%);\n  background-size: 33.94px 33.94px;		\n\n  p {\n    color: 080808;\n    font-size: 18px;\n    font-weight: 600;\n		  margin: 0;\n  }\n		\n		.preview-message {\n				margin-left: 12px;\n    background-color: rgba(255, 255, 255, 0.7);\n    font-size: 14px;\n				font-weight: 400;\n				height: 30px;\n				line-height: 30px;\n				padding: 0 18px;\n				border-radius: 30px;\n  }\n\n  z-index: 1000;\n"]);return PreviewButton_templateObject=function(){return n},n}function PreviewButton_templateObject1(){let n=(0,i._)(["\n\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  background-color: rgba(255, 255, 255, 0.4);\n  border-radius: 60px;\n  padding: 0 18px;\n  height: 30px;\n  margin-left: 18px;\n\n  span {\n    font-size: 12px;\n    font-weight: 500;\n    color: rgba(8, 8, 8, 0.6);\n		  letter-spacing: 0.2px;\n  }\n\n  .clip-icon {\n    margin-left: 12px;\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    justify-content: center;\n  }\n\n  &:hover {\n    cursor: pointer;\n    background-color: rgba(255, 255, 255, 0.7);\n  }\n"]);return PreviewButton_templateObject1=function(){return n},n}let T="https://toutpoursagloire.com";function PreviewButton(n){let{url:e}=n,[t,i]=(0,m.useState)(""),copyUrl=()=>{navigator.clipboard.writeText("".concat(T).concat(e)),i("Copi\xe9!"),setTimeout(function(){i("")},3e3)};return(0,o.jsxs)(M,{children:[(0,o.jsx)("p",{children:"Preview"}),(0,o.jsxs)(U,{onClick:()=>copyUrl(),children:[(0,o.jsxs)("span",{children:[T,e]}),(0,o.jsx)("div",{className:"clip-icon",children:(0,o.jsx)("svg",{width:"14",height:"16",viewBox:"0 0 14 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.14492 0.315878C4.24373 0.122287 4.44572 0 4.66667 0H7C7.22095 0 7.42294 0.122287 7.52175 0.315878L7.94385 1.14286H9.91667C10.8832 1.14286 11.6667 1.91037 11.6667 2.85714V4C11.6667 4.31559 11.4055 4.57143 11.0833 4.57143C10.7612 4.57143 10.5 4.31559 10.5 4V2.85714C10.5 2.54155 10.2388 2.28571 9.91667 2.28571H9.33333V4C9.33333 4.31559 9.07217 4.57143 8.75 4.57143H2.91667C2.5945 4.57143 2.33333 4.31559 2.33333 4V2.28571H1.75C1.42783 2.28571 1.16667 2.54155 1.16667 2.85714V13.1429C1.16667 13.4585 1.42783 13.7143 1.75 13.7143H4.08333C4.4055 13.7143 4.66667 13.9701 4.66667 14.2857C4.66667 14.6013 4.4055 14.8571 4.08333 14.8571H1.75C0.783507 14.8571 0 14.0897 0 13.1429V2.85714C0 1.91037 0.783501 1.14286 1.75 1.14286H3.72281L4.14492 0.315878ZM3.5 2.28571V3.42857H8.16667V2.28571H3.5ZM5.83333 7.42857C5.83333 6.48178 6.61682 5.71429 7.58333 5.71429H12.25C13.2165 5.71429 14 6.48178 14 7.42857V14.2857C14 15.2325 13.2165 16 12.25 16H7.58333C6.61682 16 5.83333 15.2325 5.83333 14.2857V7.42857Z",fill:"#080808"})})})]}),""!==t&&(0,o.jsx)("p",{className:"preview-message",children:t})]})}let M=x.ZP.div.withConfig({componentId:"sc-9d8da094-0"})(PreviewButton_templateObject()),U=x.ZP.div.withConfig({componentId:"sc-9d8da094-1"})(PreviewButton_templateObject1());function ArticleLayout_templateObject(){let n=(0,i._)(["\n  width: 100vw;\n  padding: 0 var(--border-space);\n\n  .preview-warning {\n    position: fixed;\n    bottom: 16px;\n    left: 16px;\n    margin: 0;\n    display: inline-block;\n    font-family: Switzer, sans-serif;\n    font-size: 18px;\n    padding: 10px 40px;\n    border-radius: 100px;\n    color: var(--soft-white);\n    background-color: var(--brand-color);\n    z-index: 9999;\n    opacity: 0.86;\n  }\n"]);return ArticleLayout_templateObject=function(){return n},n}function ArticleLayout_templateObject1(){let n=(0,i._)(["\n  display: flex;\n  flex-direction: column-reverse;\n  margin-bottom: 128px;\n  margin-top: 128px;\n\n  .author-box {\n    margin-bottom: 40px;\n  }\n\n  @media "," {\n    flex-direction: row;\n    margin-bottom: 126px;\n    .author-box {\n      padding-left: 40px;\n    }\n  }\n"]);return ArticleLayout_templateObject1=function(){return n},n}function ArticleLayout_templateObject2(){let n=(0,i._)(["\n  border-top: 1px solid rgba(0, 0, 0, 0.4);\n  padding-right: 40px;\n  padding-top: 32px;\n  @media "," {\n    min-width: 40%;\n    border-right: 1px solid rgba(0, 0, 0, 0.4);\n  }\n"]);return ArticleLayout_templateObject2=function(){return n},n}function ArticleLayout_templateObject3(){let n=(0,i._)(["\n  .card-label {\n    display: none;\n    margin-right: 8px;\n  }\n\n  .article-title {\n    font-size: 38px;\n    font-weight: 500;\n    line-height: 110%;\n    margin-top: 40px;\n    margin-bottom: 14px;\n  }\n\n  .article-image {\n    position: relative;\n    width: 100%;\n    height: 260px;\n  }\n\n  .reading-time {\n    font-size: 16px;\n    font-weight: 400;\n    margin-bottom: 14px;\n  }\n\n  .social-buttons {\n    display: none;\n  }\n\n  @media ",' {\n    display: grid;\n    margin: 0 0 80px 0;\n    grid-template-columns: repeat(8, 1fr);\n\n    .article-head-text {\n      grid-column: 1/5;\n      align-self: center;\n      grid-row: 1;\n      padding: 40px 0 40px 0;\n    }\n\n    .card-label {\n      display: inline-block;\n    }\n\n    .article-title {\n      margin: 20px 0 10px 0;\n      font-size: 56px;\n      line-height: 110%;\n    }\n\n    .article-image {\n      position: relative;\n      grid-column: 6/9;\n      width: calc(100%);\n      height: unset;\n      margin-top: 0;\n\n      &:after {\n        content: "";\n        display: block;\n        padding-bottom: 100%;\n      }\n\n      .image-wrapper {\n        position: absolute;\n        width: 100%;\n        height: 100%;\n      }\n    }\n\n    .reading-time {\n      font-size: 16px;\n      font-weight: 400;\n      margin-top: 10px;\n    }\n\n    .md-post {\n      grid-column: 2 / 7;\n    }\n\n    .social-buttons {\n      display: block;\n      position: sticky;\n      top: 40px;\n      margin-top: 80px;\n    }\n\n    .abox-facename {\n      margin-top: 80px;\n      grid-column: 2/4;\n    }\n\n    .abox-about {\n      margin-top: 80px;\n      grid-column: 4/7;\n    }\n  }\n']);return ArticleLayout_templateObject3=function(){return n},n}function _templateObject4(){let n=(0,i._)(["\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  margin-top: 40px;\n\n  .article-author-picture {\n    position: relative;\n    visibility: visible;\n    width: 50px;\n    height: 50px;\n    border-radius: 50px;\n    overflow: hidden;\n    margin-right: 18px;\n  }\n\n  .article-author-name {\n    font-size: 20px;\n    margin-top: 8px;\n    font-weight: 600;\n    margin-bottom: 0;\n  }\n\n  .article-date {\n    font-size: 16px;\n    margin-top: 4px;\n    margin-bottom: 8px;\n    line-height: 100%;\n  }\n\n  .article-rt {\n    position: relative;\n    font-size: 16px;\n    margin-bottom: 8px;\n    line-height: 100%;\n\n    span {\n      font-size: 20px;\n      margin-right: 2px;\n    }\n  }\n\n  @media ",' {\n    display: flex;\n    margin: 0;\n    grid-column: 1/3;\n    grid-row: 2;\n    align-self: start;\n\n    border-top: 1px solid #dcdcdc;\n    border-bottom: none;\n    padding-top: 80px;\n    line-height: 120%;\n\n    .article-author-name {\n      margin: 12px 0 0 0;\n      font-size: 22px;\n      font-weight: 600;\n      color: #323232;\n      letter-spacing: 0.02em;\n    }\n\n    .article-date {\n      margin-top: 8px;\n      font-size: 20px;\n      font-weight: 400;\n      color: #323232;\n      letter-spacing: 0.02em;\n    }\n\n    .article-rt {\n      position: relative;\n      font-size: 18px;\n\n      span {\n        display: inline-block;\n        width: 28px;\n        text-align: center;\n        margin-right: 4px;\n        color: white;\n      }\n\n      &:before {\n        content: "";\n        display: inline-block;\n        position: absolute;\n        z-index: -1;\n        left: 0;\n        top: -7px;\n        background-color: #161616;\n        height: 28px;\n        width: 28px;\n        border-radius: 22px;\n      }\n    }\n  }\n']);return _templateObject4=function(){return n},n}function _templateObject5(){let n=(0,i._)(["\n  font-weight: 400;\n  font-size: 26px;\n  padding: 40px 0 24px 0;\n\n  p {\n    color: #161616;\n    margin: 0;\n  }\n\n  a {\n    color: var(--brand-color);\n    text-decoration: none;\n  }\n\n  a:hover {\n    color: var(--brand-color);\n    text-decoration: underline;\n  }\n\n  /* Sp\xe9cificit\xe9 renforc\xe9e pour les balises u */\n  u {\n    text-decoration: underline !important;\n  }\n\n  /* Styles pour les balises u \xe0 l'int\xe9rieur des liens - TOUTES les combinaisons */\n  a u,\n  a em u,\n  a strong u,\n  a span u,\n  a i u,\n  a b u {\n    text-decoration: none !important;\n  }\n  a:hover u,\n  a:hover em u,\n  a:hover strong u,\n  a:hover span u,\n  a:hover i u,\n  a:hover b u {\n    text-decoration: underline !important;\n  }\n\n  @media "," {\n    border-top: 1px solid #dcdcdc;\n    border-left: 1px solid #dcdcdc;\n    grid-column: 3/9;\n    grid-row: 2;\n    font-size: 32px;\n    padding: 80px 0 40px 80px;\n  }\n"]);return _templateObject5=function(){return n},n}let ArticleLayout_linkComponent=n=>{let{href:e,children:t,...i}=n;return(0,o.jsx)("a",{href:e,...i,children:t})},ArticleLayout_underlineComponent=n=>{let{children:e}=n;return(0,o.jsx)("u",{children:e})};function ArticleLayout(n){var e,t,i,l,c,d,p,u;let{post:x,preview:m,newsletter:g}=n,{modules:b,route:f}=x,C=x.blog?"/blog/".concat(x.blog.blogger.slug):"/recherche?author=".concat(x.author.fullName);return(0,o.jsxs)(D,{children:[(0,o.jsx)(r.PB,{title:(null===(e=b.seo)||void 0===e?void 0:e.metaTitle)||x.title,description:(null===(t=b.seo)||void 0===t?void 0:t.metaDescription)||null,openGraph:{title:(null===(i=b.seo)||void 0===i?void 0:i.metaTitle)||x.title,description:(null===(l=b.seo)||void 0===l?void 0:l.metaDescription)||null,url:"https://toutpoursagloire.com".concat(f),type:"article",article:{publishedTime:x.published_at,tags:x.topics.map(n=>n.name)},images:[{url:(0,a.k)(x.image),alt:(null===(c=x.image)||void 0===c?void 0:c.alternativeText)||""}]},twitter:{site:"@t_p_s_g",cardType:"summary_large_image"}}),m&&(0,o.jsx)(PreviewButton,{url:f}),(0,o.jsxs)(V,{children:[(0,o.jsxs)("div",{className:"article-head-text",children:[(0,o.jsx)("h1",{className:"article-title",children:x.title}),x.topics&&x.topics.map((n,e)=>(0,o.jsx)(s.Z,{text:n.name},e)),x.readingTime?(0,o.jsxs)("div",{className:"reading-time",children:[x.readingTime," min de lecture"]}):null]}),(0,o.jsx)("div",{className:"article-image",children:(0,o.jsx)("div",{className:"image-wrapper",children:(0,o.jsx)(h.Z,{imageData:x.image})})}),(0,o.jsxs)(E,{children:[(0,o.jsx)("a",{href:C,children:(0,o.jsx)("div",{className:"article-author-picture",children:(0,o.jsx)(h.Z,{imageData:null===(d=x.author)||void 0===d?void 0:d.picture})})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"article-author-name",children:(0,o.jsx)("a",{href:C,children:(null===(p=x.author)||void 0===p?void 0:p.fullName)||""})}),(0,o.jsx)("p",{className:"article-date",children:(0,w.S$)(x.published_at)})]})]}),(0,o.jsx)(F,{children:(0,o.jsx)(P.U,{rehypePlugins:[z.Z],components:{a:ArticleLayout_linkComponent,u:ArticleLayout_underlineComponent},children:(null===(u=b.lead)||void 0===u?void 0:u.content)||""})}),(0,o.jsx)(L.Z,{url:"https://toutpoursagloire.com/article/".concat(x.slug)}),(0,o.jsx)(MDPost,{content:x.body})]}),(0,o.jsxs)(H,{children:[(0,o.jsx)(R,{children:(0,o.jsx)(B.Z,{formString:g,title:"Ma newsletter",desc:""})}),(0,o.jsx)(A.Z,{author:x.author,blog:x.blog})]})]})}let D=x.ZP.div.withConfig({componentId:"sc-85be3c3e-0"})(ArticleLayout_templateObject()),H=x.ZP.div.withConfig({componentId:"sc-85be3c3e-1"})(ArticleLayout_templateObject1(),j.U.desktop),R=x.ZP.div.withConfig({componentId:"sc-85be3c3e-2"})(ArticleLayout_templateObject2(),j.U.desktop),V=x.ZP.div.withConfig({componentId:"sc-85be3c3e-3"})(ArticleLayout_templateObject3(),j.U.tablet),E=x.ZP.div.withConfig({componentId:"sc-85be3c3e-4"})(_templateObject4(),j.U.tablet),F=x.ZP.div.withConfig({componentId:"sc-85be3c3e-5"})(_templateObject5(),j.U.tablet);var J=t(4871);function RegisterBar_templateObject(){let n=(0,i._)(["\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  width: 100vw;\n  border-top: 1px #ffffff solid;\n  background-color: rgba(241, 241, 241, 0.9);\n\n  @supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {\n    background-color: rgba(221, 221, 221, 0.45);\n    -webkit-backdrop-filter: blur(20px);\n    backdrop-filter: blur(20px);\n  }\n\n  z-index: 1000;\n\n  .register-content {\n    position: relative;\n  }\n\n  /* Ensure the SubHeader inside RegisterBar has proper alignment */\n  .subheader {\n    border-top: none;\n    border-bottom: none;\n    padding-top: 12px;\n    padding-bottom: 12px;\n  }\n"]);return RegisterBar_templateObject=function(){return n},n}function RegisterBar(n){let{children:e}=n;return(0,o.jsx)(W,{children:(0,o.jsx)("div",{className:"register-content site-padding",children:e})})}t(6268);let W=x.ZP.div.withConfig({componentId:"sc-e7f5198c-0"})(RegisterBar_templateObject());function WebinarLayout_templateObject(){let n=(0,i._)(["\n  padding-bottom: 70px;\n\n  header {\n    display: flex;\n    flex-direction: column;\n    margin-bottom: 48px;\n  }\n\n  .header-player-cover {\n    width: 100%;\n    aspect-ratio: 16 / 10;\n  }\n\n  .card-label {\n    display: none;\n    margin: 8px 8px 0 0;\n  }\n\n  article {\n    margin-top: 70px;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n  }\n\n  .youtube-player-component {\n    /* styles for webinaires */\n  }\n\n  @media "," {\n    .card-label {\n      display: inline-block;\n    }\n\n    .header-player-cover {\n      margin-top: 48px;\n    }\n  }\n  @media "," {\n    header {\n      margin-top: 90px;\n      margin-bottom: 70px;\n      flex-direction: row;\n      justify-content: space-between;\n    }\n\n    .header-text-container {\n      position: relative;\n      width: 50%;\n      aspect-ratio: 16 / 10;\n      padding-top: 24px;\n      padding-left: 16px;\n    }\n\n    .header-player-cover {\n      margin-top: 0;\n      width: calc(50% - 32px);\n    }\n  }\n"]);return WebinarLayout_templateObject=function(){return n},n}function WebinarLayout(n){var e;let{episode:t,preview:i,relatedPosts:l}=n;if(!t)return null;let u=t.route,{lead:x,webinar:m,event:h,seo:g}=t.modules,b=(0,w.S$)(t.published_at),f=h&&new Date(h.date)>new Date;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(r.PB,{title:(null==g?void 0:g.metaTitle)||t.title,description:(null==g?void 0:g.metaDescription)||null,openGraph:{title:(null==g?void 0:g.metaTitle)||t.title,description:(null==g?void 0:g.metaDescription)||null,url:"https://toutpoursagloire.com".concat((0,J.qt)(t)),images:[{url:(0,a.k)(t.image),alt:(null===(e=t.image)||void 0===e?void 0:e.alternativeText)||""}]},twitter:{site:"@t_p_s_g",cardType:"summary_large_image"}}),i&&(0,o.jsx)(PreviewButton,{url:u}),(0,o.jsxs)(q,{className:"site-padding",children:[(0,o.jsxs)("header",{children:[(0,o.jsxs)("div",{className:"header-text-container",children:[(0,o.jsx)(d.hQ,{children:(0,o.jsx)(c(),{href:"/webinaires",children:"WEBINAIRE"})}),(0,o.jsx)(d.DZ,{children:t.title}),(0,o.jsx)("div",{children:t.topics&&t.topics.map((n,e)=>(0,o.jsx)(s.Z,{text:n.name},e))})]}),(0,o.jsx)("div",{className:"header-player-cover",children:(0,o.jsx)(VideoPlayer,{video:!f&&m.embedVideo||null,image:t.image})})]}),(0,o.jsxs)(C.Z,{children:[!f&&(0,o.jsx)(C.Z.Text,{label:"Date",content:b,addClass:f?"mobile-hide_flex":""}),(0,o.jsx)(C.Z.Authors,{label:"Orateur(s)",authors:m.speakers||!1,addClass:f?"mobile-hide_flex":""}),(0,o.jsx)(C.Z.Social,{url:"https://toutpoursagloire.com/webinaires/".concat(t.slug),addClass:"mobile-hide_flex"})]}),(0,o.jsxs)("article",{children:[(null==x?void 0:x.content)&&(0,o.jsx)("section",{children:(0,o.jsx)(p.oJ,{content:x.content})}),(0,o.jsx)("section",{children:(0,o.jsx)(p.oJ,{content:t.body})})]}),f&&(0,o.jsx)(RegisterBar,{children:(0,o.jsxs)(C.Z,{children:[(0,o.jsx)(C.Z.Text,{label:"Date",content:(0,w.S$)(h.date)}),(0,o.jsx)(C.Z.Text,{label:"D\xe9but",content:(0,w.xO)(h.date)}),(0,o.jsx)(C.Z.LinkButton,{url:h.url,text:"JE M'INSCRIS"})]})})]}),(0,o.jsx)(y.Z,{items:l})]})}let q=x.ZP.div.withConfig({componentId:"sc-f925c478-0"})(WebinarLayout_templateObject(),j.U.tablet,j.U.desktop)},5874:function(n,e,t){t.d(e,{Z:function(){return MediaButton}});var i=t(2729),o=t(5893),r=t(9521);function _templateObject(){let n=(0,i._)(["\n  \n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n  padding: 6px 6px 6px 6px;\n  \n  border: 1px solid #1E1E1D;\n  background-color: transparent;\n  box-sizing: border-box;\n  border-radius: 100px;\n  \n  span {\n    position: relative;\n    font-family: Stelvio, sans-serif;\n    white-space: nowrap;\n    font-style: normal;\n    font-weight: 500;\n    font-size: 16px;\n    line-height: 22px;\n    margin-bottom: -6px;\n    margin-left: 6px;\n    margin-right: 6px;\n    color: #1E1E1D;\n  }\n  \n  svg {\n    height: 20px;\n    width: 20px;\n  }\n  \n  &:hover {\n    background-color: #1E1E1D;\n    span {\n      color: #F8F8F3;\n    }\n    path {\n      fill: #F8F8F3;\n    }\n  }\n\n  cursor: pointer;\n"]);return _templateObject=function(){return n},n}function MediaButton(n){let{plateform:e,text:t}=n;return(0,o.jsx)(o.Fragment,{children:getIcon(e)&&(0,o.jsxs)(a,{children:[e&&getIcon(e),t&&(0,o.jsx)("span",{children:t})]})})}let getIcon=n=>{switch(n){case"apple":return(0,o.jsx)(AppleIcon,{});case"spotify":return(0,o.jsx)(SpotifyIcon,{});case"google":return(0,o.jsx)(GoogleIcon,{});case"soundcloud":return(0,o.jsx)(SoundcloudIcon,{});case"youtube":return(0,o.jsx)(YoutubeIcon,{});default:return null}},a=r.ZP.button.withConfig({componentId:"sc-76edf0e0-0"})(_templateObject()),GoogleIcon=()=>(0,o.jsxs)("svg",{width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("g",{clipPath:"url(#clip0_221_586)",children:(0,o.jsx)("path",{d:"M11 0.499969C8.82441 0.499969 6.69767 1.14511 4.88873 2.3538C3.07979 3.5625 1.66989 5.28046 0.83733 7.29045C0.00476613 9.30044 -0.213071 11.5122 0.211367 13.646C0.635804 15.7798 1.68345 17.7398 3.22183 19.2781C4.76021 20.8165 6.72022 21.8642 8.85401 22.2886C10.9878 22.713 13.1995 22.4952 15.2095 21.6626C17.2195 20.8301 18.9375 19.4202 20.1462 17.6112C21.3549 15.8023 22 13.6756 22 11.5C21.9944 8.5843 20.8337 5.78966 18.772 3.72797C16.7103 1.66628 13.9157 0.50556 11 0.499969ZM11 19.9615C9.60898 19.9604 8.23967 19.6164 7.0133 18.9599C5.78693 18.3034 4.74132 17.3547 3.96903 16.1978C3.19674 15.0408 2.72159 13.7113 2.58563 12.327C2.44968 10.9426 2.65712 9.54606 3.18959 8.26098C3.72206 6.9759 4.56314 5.84191 5.63837 4.9594C6.71361 4.07688 7.98983 3.47306 9.35406 3.20138C10.7183 2.92971 12.1285 2.99855 13.4598 3.40183C14.7911 3.80511 16.0024 4.53037 16.9865 5.51343C17.1448 5.67206 17.2336 5.88697 17.2336 6.11103C17.2336 6.33509 17.1448 6.55 16.9865 6.70862C16.9087 6.78826 16.8158 6.85153 16.7131 6.89473C16.6105 6.93793 16.5003 6.96019 16.3889 6.96019C16.2776 6.96019 16.1674 6.93793 16.0647 6.89473C15.9621 6.85153 15.8692 6.78826 15.7913 6.70862C14.7094 5.62343 13.2917 4.93678 11.7695 4.76062C10.2473 4.58446 8.71025 4.92918 7.40905 5.73859C6.10786 6.54799 5.11925 7.77433 4.60441 9.21766C4.08956 10.661 4.07886 12.2361 4.57405 13.6863C5.06923 15.1365 6.04109 16.3762 7.33117 17.2032C8.62125 18.0302 10.1534 18.3958 11.6779 18.2403C13.2024 18.0848 14.6293 17.4175 15.7259 16.3471C16.8225 15.2767 17.5241 13.8664 17.7163 12.3461H11C10.7756 12.3461 10.5604 12.257 10.4017 12.0983C10.243 11.9396 10.1538 11.7244 10.1538 11.5C10.1538 11.2756 10.243 11.0603 10.4017 10.9016C10.5604 10.743 10.7756 10.6538 11 10.6538H18.6154C18.8398 10.6538 19.055 10.743 19.2137 10.9016C19.3724 11.0603 19.4615 11.2756 19.4615 11.5C19.4587 13.7432 18.5664 15.8938 16.9801 17.4801C15.3939 19.0663 13.2433 19.9587 11 19.9615Z",fill:"black"})}),(0,o.jsx)("defs",{children:(0,o.jsx)("clipPath",{id:"clip0_221_586",children:(0,o.jsx)("rect",{width:"22",height:"22",fill:"white",transform:"translate(0 0.499969)"})})})]}),AppleIcon=()=>(0,o.jsx)("svg",{width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{d:"M11.6769 3.82724C11.9433 3.14384 12.4092 2.5564 13.0139 2.14134C13.6187 1.72628 14.3343 1.5028 15.0678 1.49997C15.2607 1.49997 15.4457 1.57659 15.5821 1.71298C15.7185 1.84937 15.7951 2.03436 15.7951 2.22724C15.7951 2.42013 15.7185 2.60511 15.5821 2.7415C15.4457 2.87789 15.2607 2.95451 15.0678 2.95451C14.6269 2.95589 14.1967 3.09024 13.8334 3.34001C13.4701 3.58979 13.1906 3.94336 13.0315 4.35452C12.9781 4.49172 12.8843 4.60951 12.7626 4.69229C12.6409 4.77507 12.4969 4.81896 12.3496 4.81815C12.263 4.81629 12.1771 4.80095 12.0951 4.7727C12.0053 4.73849 11.9231 4.68681 11.8533 4.62064C11.7836 4.55447 11.7277 4.47513 11.6888 4.38721C11.6499 4.29929 11.6288 4.20453 11.6268 4.10841C11.6247 4.0123 11.6418 3.91673 11.6769 3.82724ZM19.9769 15.3C19.3957 15.008 18.9034 14.5656 18.5514 14.0187C18.1994 13.4718 18.0005 12.8405 17.9754 12.1906C17.9503 11.5407 18.1 10.896 18.4089 10.3236C18.7177 9.75122 19.1745 9.27216 19.7315 8.93633C19.8179 8.88149 19.8921 8.80942 19.9495 8.72463C20.0069 8.63984 20.0462 8.54414 20.0649 8.4435C20.0837 8.34286 20.0815 8.23943 20.0586 8.13966C20.0356 8.03989 19.9923 7.94592 19.9315 7.86361C18.9433 6.60456 17.5052 5.77787 15.9199 5.55746C14.3346 5.33704 12.7257 5.74008 11.4315 6.68179C10.5092 6.01095 9.41936 5.6083 8.28249 5.51839C7.14561 5.42847 6.00601 5.6548 4.98978 6.17233C3.97354 6.68986 3.12027 7.47841 2.52436 8.45077C1.92845 9.42312 1.61313 10.5414 1.61328 11.6818C1.63516 14.0794 2.3589 16.4181 3.6951 18.4091C5.00419 20.3454 6.66783 21.5 8.15874 21.5H14.7042C16.7496 21.5 19.0587 19.3272 20.3315 16.2272C20.4031 16.0568 20.4062 15.8652 20.3402 15.6925C20.2742 15.5198 20.144 15.3792 19.9769 15.3Z",fill:"black"})}),SpotifyIcon=()=>(0,o.jsxs)("svg",{width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("g",{clipPath:"url(#clip0_221_590)",children:(0,o.jsx)("path",{d:"M11 0.499969C8.82441 0.499969 6.69767 1.14511 4.88873 2.3538C3.07979 3.5625 1.66989 5.28046 0.83733 7.29045C0.00476613 9.30044 -0.213071 11.5122 0.211367 13.646C0.635804 15.7798 1.68345 17.7398 3.22183 19.2781C4.76021 20.8165 6.72022 21.8642 8.85401 22.2886C10.9878 22.713 13.1995 22.4952 15.2095 21.6626C17.2195 20.8301 18.9375 19.4202 20.1462 17.6112C21.3549 15.8023 22 13.6756 22 11.5C21.9944 8.5843 20.8337 5.78966 18.772 3.72797C16.7103 1.66628 13.9157 0.50556 11 0.499969ZM14.025 15.8048C13.9232 16.0031 13.7472 16.1533 13.5353 16.2226C13.3234 16.292 13.0926 16.2748 12.8933 16.175C12.3058 15.8804 11.6572 15.7282 11 15.7307C10.3381 15.7278 9.68523 15.8838 9.09616 16.1855C8.97685 16.241 8.84694 16.2698 8.71539 16.2702C8.52666 16.2681 8.34402 16.2031 8.19652 16.0853C8.04902 15.9676 7.94513 15.8039 7.90135 15.6203C7.85758 15.4367 7.87645 15.2437 7.95496 15.0721C8.03346 14.9005 8.1671 14.76 8.33462 14.673C9.16031 14.2535 10.0738 14.036 11 14.0384C11.9214 14.0375 12.8303 14.2512 13.6548 14.6625C13.8539 14.7663 14.0043 14.9443 14.0735 15.1579C14.1428 15.3716 14.1253 15.6038 14.025 15.8048ZM15.5904 12.8115C15.5401 12.9098 15.4708 12.9972 15.3866 13.0686C15.3023 13.1399 15.2048 13.1939 15.0995 13.2273C14.9943 13.2608 14.8835 13.273 14.7735 13.2634C14.6635 13.2537 14.5565 13.2224 14.4587 13.1711C13.3867 12.6267 12.201 12.3439 10.9987 12.3457C9.79644 12.3475 8.61166 12.634 7.54135 13.1817C7.41991 13.243 7.28602 13.2755 7.15 13.2769C6.99385 13.2772 6.84072 13.2338 6.70799 13.1516C6.57525 13.0693 6.46822 12.9515 6.39904 12.8115C6.34795 12.7123 6.31699 12.6039 6.30796 12.4927C6.29892 12.3814 6.31199 12.2695 6.34639 12.1633C6.3808 12.0571 6.43588 11.9588 6.50845 11.874C6.58102 11.7892 6.66965 11.7196 6.76923 11.6692C8.07771 11.0016 9.52576 10.6535 10.9947 10.6535C12.4637 10.6535 13.9117 11.0016 15.2202 11.6692C15.3198 11.7196 15.4084 11.7892 15.481 11.874C15.5535 11.9588 15.6086 12.0571 15.643 12.1633C15.6774 12.2695 15.6905 12.3814 15.6815 12.4927C15.6724 12.6039 15.6415 12.7123 15.5904 12.8115ZM17.1558 9.80766C17.1055 9.90597 17.0362 9.99333 16.952 10.0647C16.8677 10.1361 16.7701 10.1901 16.6649 10.2235C16.5597 10.2569 16.4488 10.2692 16.3388 10.2595C16.2288 10.2499 16.1218 10.2185 16.024 10.1673C14.4684 9.37332 12.7465 8.96007 11 8.96151C9.24852 8.95715 7.5217 9.37435 5.96539 10.1779C5.84812 10.2401 5.7174 10.2728 5.58462 10.273C5.39499 10.2734 5.21075 10.21 5.06146 10.0931C4.91216 9.97613 4.80647 9.81245 4.76134 9.62827C4.71621 9.44409 4.73426 9.25009 4.81258 9.07739C4.89091 8.90469 5.02498 8.76331 5.19327 8.67593C6.9877 7.75363 8.97586 7.27164 10.9934 7.2698C13.011 7.26796 15.0001 7.74632 16.7962 8.66535C16.8948 8.71668 16.9824 8.78696 17.0539 8.87217C17.1254 8.95738 17.1794 9.05585 17.2128 9.16195C17.2462 9.26804 17.2584 9.37968 17.2486 9.49048C17.2388 9.60128 17.2073 9.70906 17.1558 9.80766Z",fill:"black"})}),(0,o.jsx)("defs",{children:(0,o.jsx)("clipPath",{id:"clip0_221_590",children:(0,o.jsx)("rect",{width:"22",height:"22",fill:"white",transform:"translate(0 0.499969)"})})})]}),SoundcloudIcon=()=>(0,o.jsxs)("svg",{width:"22",height:"23",viewBox:"0 0 22 23",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M11.8164 6.32838V16.5661H18.5801C19.2293 16.4433 20.5277 15.766 20.5277 14.0396C20.5277 11.8815 18.5275 11.3814 16.8695 11.3814C16.8695 7.67587 13.5008 6.46875 11.8164 6.32838Z",fill:"black"}),(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.8166 18.0379C11.0037 18.0379 10.3447 17.379 10.3447 16.5661V6.32838C10.3447 5.91678 10.5171 5.52398 10.82 5.24528C11.1229 4.96658 11.5286 4.82744 11.9388 4.86163C12.9932 4.94949 14.535 5.35714 15.8528 6.33671C16.9472 7.1502 17.8744 8.35633 18.2086 10.011C18.8212 10.1093 19.4584 10.2926 20.0378 10.616C21.1782 11.2525 21.9997 12.3906 21.9997 14.0396C21.9997 16.7125 19.933 17.8081 18.8539 18.0123C18.7637 18.0293 18.6721 18.0379 18.5803 18.0379H11.8166ZM16.8696 11.3814C16.8696 8.58643 14.9531 7.21286 13.2884 6.64634C12.7461 6.46177 12.2305 6.36287 11.8166 6.32838V16.5661H18.5803C19.2295 16.4433 20.5278 15.766 20.5278 14.0396C20.5278 11.8815 18.5277 11.3814 16.8696 11.3814Z",fill:"black"}),(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.98538 5.61964C7.47733 5.61964 7.06548 6.03149 7.06548 6.53953L7.06548 17.2235C7.06548 17.7315 7.47733 18.1434 7.98538 18.1434C8.49342 18.1434 8.90527 17.7315 8.90527 17.2235L8.90527 6.53954C8.90527 6.0315 8.49342 5.61964 7.98538 5.61964Z",fill:"black"}),(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.45315 7.50483C3.9451 7.50483 3.53325 7.91668 3.53325 8.42473L3.53325 17.2234C3.53325 17.7315 3.9451 18.1433 4.45315 18.1433C4.96119 18.1433 5.37305 17.7315 5.37305 17.2234L5.37305 8.42473C5.37305 7.91668 4.9612 7.50483 4.45315 7.50483Z",fill:"black"}),(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0.919947 10.9616C0.411902 10.9616 4.99609e-05 11.3734 4.99393e-05 11.8815L4.97117e-05 17.2235C4.96901e-05 17.7315 0.411902 18.1434 0.919947 18.1434C1.42799 18.1434 1.83984 17.7315 1.83984 17.2235L1.83984 11.8815C1.83984 11.3734 1.42799 10.9616 0.919947 10.9616Z",fill:"black"})]}),YoutubeIcon=()=>(0,o.jsxs)("svg",{width:"25",height:"21",viewBox:"0 0 25 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("g",{clipPath:"url(#clip0_243_539)",children:(0,o.jsx)("path",{d:"M23.8572 4.28545C23.7669 3.92336 23.5902 3.58856 23.3421 3.30973C23.0941 3.03091 22.7822 2.81639 22.4331 2.68456C19.0741 1.38813 13.7116 1.39795 13.417 1.39795C13.1223 1.39795 7.75985 1.38813 4.40092 2.68456C4.05181 2.81639 3.73987 3.03091 3.49184 3.30973C3.24381 3.58856 3.06709 3.92336 2.97681 4.28545C2.72146 5.25777 2.41699 7.04527 2.41699 10.0015C2.41699 12.9578 2.72146 14.7453 2.97681 15.7176C3.06709 16.0797 3.24381 16.4145 3.49184 16.6933C3.73987 16.9721 4.05181 17.1867 4.40092 17.3185C7.62235 18.5658 12.6706 18.6051 13.3482 18.6051H13.4857C14.1634 18.6051 19.2116 18.5658 22.4331 17.3185C22.7822 17.1867 23.0941 16.9721 23.3421 16.6933C23.5902 16.4145 23.7669 16.0797 23.8572 15.7176C24.1125 14.7453 24.417 12.9578 24.417 10.0015C24.417 7.04527 24.1125 5.25777 23.8572 4.28545ZM16.7759 10.3256L12.0616 13.4685C11.9991 13.5145 11.9232 13.5387 11.8456 13.5372C11.7805 13.5348 11.7168 13.518 11.659 13.4881C11.5962 13.4555 11.5437 13.4061 11.5073 13.3454C11.4709 13.2847 11.452 13.2151 11.4527 13.1444V6.85867C11.452 6.78791 11.4709 6.71833 11.5073 6.65765C11.5437 6.59698 11.5962 6.54757 11.659 6.51492C11.7216 6.4815 11.7921 6.46569 11.863 6.46914C11.9339 6.4726 12.0026 6.49521 12.0616 6.53456L16.7759 9.67741C16.8307 9.71192 16.8758 9.75974 16.9071 9.81642C16.9384 9.8731 16.9548 9.93678 16.9548 10.0015C16.9548 10.0663 16.9384 10.1299 16.9071 10.1866C16.8758 10.2433 16.8307 10.2911 16.7759 10.3256Z",fill:"black"})}),(0,o.jsx)("defs",{children:(0,o.jsx)("clipPath",{id:"clip0_243_539",children:(0,o.jsx)("rect",{width:"23.6364",height:"20",fill:"white",transform:"translate(0.780273 0.00146484)"})})})]})},4012:function(n,e,t){t.d(e,{Z:function(){return AuthorBox}});var i=t(2729),o=t(5893),r=t(9521),a=t(5582),l=t(9663),c=t(1304),d=t(7421);function _templateObject(){let n=(0,i._)(["\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  border-top: 1px solid rgba(0, 0, 0, 0.4);\n  padding-top: 24px;\n\n  @media "," {\n    flex-direction: row;\n    padding: 32px 0 0 0;\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  position: relative;\n  flex-shrink: 0;\n  margin: 0 32px 12px 0;\n  height: 60px;\n  width: 60px;\n  border-radius: 60px;\n  overflow: hidden;\n  background-color: white;\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  .abox-about__name {\n    font-size: 26px;\n    font-weight: 600;\n  }\n  p {\n    font-size: 22px;\n    line-height: 28px;\n    font-weight: 400;\n    margin: 12px 0 0 0;\n  }\n\n  a {\n    color: var(--brand-color);\n    text-decoration: none;\n  }\n\n  a:hover {\n    color: var(--brand-color);\n    text-decoration: underline;\n  }\n\n  /* Sp\xe9cificit\xe9 renforc\xe9e pour les balises u */\n  u {\n    text-decoration: underline !important;\n  }\n\n  /* Styles pour les balises u \xe0 l'int\xe9rieur des liens - TOUTES les combinaisons */\n  a u,\n  a em u,\n  a strong u,\n  a span u,\n  a i u,\n  a b u {\n    text-decoration: none !important;\n  }\n  a:hover u,\n  a:hover em u,\n  a:hover strong u,\n  a:hover span u,\n  a:hover i u,\n  a:hover b u {\n    text-decoration: underline !important;\n  }\n"]);return _templateObject2=function(){return n},n}t(4871),t(1664);let linkComponent=n=>{let{href:e,children:t,...i}=n;return(0,o.jsx)("a",{href:e,...i,children:t})},underlineComponent=n=>{let{children:e}=n;return(0,o.jsx)("u",{children:e})};function AuthorBox(n){var e,t;let{author:i,blog:r}=n;if(!i)return null;i.about=(null===(e=i.about)||void 0===e?void 0:e.replace(/\\/g,""))||"";let d=r?"/blog/".concat(null===(t=r.blogger)||void 0===t?void 0:t.slug):"/recherche?author=".concat(i.fullName),x="".concat(i.firstName||""," ").concat(i.lastName||"").trim();return(0,o.jsxs)(s,{className:"author-box",children:[(0,o.jsx)(p,{children:(0,o.jsx)("a",{href:d,children:(0,o.jsx)(c.Z,{imageData:i.picture})})}),(0,o.jsxs)(u,{children:[(0,o.jsx)("p",{className:"abox-about__name",children:(0,o.jsx)("a",{href:d,children:x})}),(0,o.jsx)(a.U,{rehypePlugins:[l.Z],components:{a:linkComponent,u:underlineComponent},children:i.about})]})]})}let s=r.ZP.div.withConfig({componentId:"sc-6ce06466-0"})(_templateObject(),d.U.tablet),p=r.ZP.div.withConfig({componentId:"sc-6ce06466-1"})(_templateObject1()),u=r.ZP.div.withConfig({componentId:"sc-6ce06466-2"})(_templateObject2())}}]);