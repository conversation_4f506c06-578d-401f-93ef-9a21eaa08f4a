{"kind": "FETCH", "data": {"headers": {"connection": "keep-alive", "content-length": "64399", "content-type": "application/json", "date": "Wed, 28 May 2025 11:34:02 GMT", "keep-alive": "timeout=5", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Origin", "x-frame-options": "SAMEORIGIN", "x-powered-by": "Strapi <strapi.io>", "x-response-time": "823ms"}, "body": "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", "status": 200, "url": "http://127.0.0.1:1337/graphql"}, "revalidate": 3600, "tags": []}