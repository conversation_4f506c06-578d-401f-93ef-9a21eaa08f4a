"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./pages/_document.js":
/*!****************************!*\
  !*** ./pages/_document.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyDocument)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_global__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/global */ \"./styles/global.js\");\n\n\n\n\nclass MyDocument extends (next_document__WEBPACK_IMPORTED_MODULE_1___default()) {\n    static async getInitialProps(ctx) {\n        const sheet = new styled_components__WEBPACK_IMPORTED_MODULE_2__.ServerStyleSheet();\n        const originalRenderPage = ctx.renderPage;\n        try {\n            ctx.renderPage = ()=>originalRenderPage({\n                    enhanceApp: (App)=>(props)=>sheet.collectStyles(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n                                lang: \"fr\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_styles_global__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                                        lineNumber: 16,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(App, {\n                                        ...props\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                                        lineNumber: 17,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                                lineNumber: 15,\n                                columnNumber: 15\n                            }, this))\n                });\n            const initialProps = await next_document__WEBPACK_IMPORTED_MODULE_1___default().getInitialProps(ctx);\n            return {\n                ...initialProps,\n                styles: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        initialProps.styles,\n                        sheet.getStyleElement()\n                    ]\n                }, void 0, true)\n            };\n        } finally{\n            sheet.seal();\n        }\n    }\n    render() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n            lang: \"fr\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preconnect\",\n                            href: \"https://fonts.googleapis.com\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preconnect\",\n                            href: \"https://fonts.gstatic.com\",\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            href: \"https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&display=swap\",\n                            rel: \"stylesheet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\pages\\\\_document.js\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.js\n");

/***/ }),

/***/ "./styles/device.js":
/*!**************************!*\
  !*** ./styles/device.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   device: () => (/* binding */ device)\n/* harmony export */ });\nconst size = {\n    mini: \"320px\",\n    tablet: \"744px\",\n    desktop: \"1024px\",\n    desktopXL: \"1441px\"\n};\nconst device = {\n    mini: `(max-width: ${size.mini})`,\n    tablet: `(min-width: ${size.tablet})`,\n    desktop: `(min-width: ${size.desktop})`,\n    desktopXL: `(min-width: ${size.desktopXL})`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zdHlsZXMvZGV2aWNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxNQUFNQSxPQUFPO0lBQ1hDLE1BQU07SUFDTkMsUUFBUTtJQUNSQyxTQUFTO0lBQ1RDLFdBQVc7QUFDYjtBQUVPLE1BQU1DLFNBQVM7SUFDcEJKLE1BQU0sQ0FBQyxZQUFZLEVBQUVELEtBQUtDLElBQUksQ0FBQyxDQUFDLENBQUM7SUFDakNDLFFBQVEsQ0FBQyxZQUFZLEVBQUVGLEtBQUtFLE1BQU0sQ0FBQyxDQUFDLENBQUM7SUFDckNDLFNBQVMsQ0FBQyxZQUFZLEVBQUVILEtBQUtHLE9BQU8sQ0FBQyxDQUFDLENBQUM7SUFDdkNDLFdBQVcsQ0FBQyxZQUFZLEVBQUVKLEtBQUtJLFNBQVMsQ0FBQyxDQUFDLENBQUM7QUFDN0MsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3Rwc2ctbmV4dC8uL3N0eWxlcy9kZXZpY2UuanM/ZDEwYiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzaXplID0ge1xyXG4gIG1pbmk6IFwiMzIwcHhcIixcclxuICB0YWJsZXQ6IFwiNzQ0cHhcIixcclxuICBkZXNrdG9wOiBcIjEwMjRweFwiLFxyXG4gIGRlc2t0b3BYTDogXCIxNDQxcHhcIixcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBkZXZpY2UgPSB7XHJcbiAgbWluaTogYChtYXgtd2lkdGg6ICR7c2l6ZS5taW5pfSlgLCAvLyBtYXggd2lkdGggZm9yIG1pbmlcclxuICB0YWJsZXQ6IGAobWluLXdpZHRoOiAke3NpemUudGFibGV0fSlgLFxyXG4gIGRlc2t0b3A6IGAobWluLXdpZHRoOiAke3NpemUuZGVza3RvcH0pYCxcclxuICBkZXNrdG9wWEw6IGAobWluLXdpZHRoOiAke3NpemUuZGVza3RvcFhMfSlgLFxyXG59O1xyXG4iXSwibmFtZXMiOlsic2l6ZSIsIm1pbmkiLCJ0YWJsZXQiLCJkZXNrdG9wIiwiZGVza3RvcFhMIiwiZGV2aWNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./styles/device.js\n");

/***/ }),

/***/ "./styles/global.js":
/*!**************************!*\
  !*** ./styles/global.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Global: () => (/* binding */ Global),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styles_device__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styles/device */ \"./styles/device.js\");\n\n\nconst Global = styled_components__WEBPACK_IMPORTED_MODULE_0__.createGlobalStyle`\r\n\r\n  // TODO: Renommer les variables correctement\r\n  :root {\r\n    \r\n    /**\r\n     * COLORS\r\n     */\r\n    --soft-white: #FAF7F3;\r\n    --c-cream: #FFEBD8;\r\n    --c-soft-cream: #F9F1E6;\r\n    --c-cream-A80: rgba(255, 235, 216, 0.8);\r\n    --c-cream-A40: rgba(255, 235, 216, 0.4);\r\n    --c-cream-A20: rgba(255, 235, 216, 0.2);\r\n    --soft-dark: #161616;\r\n    //--blue-dark: #081921;\r\n    --blue-dark: #081D21;\r\n    --c--blue-medium: #1C373C;\r\n    --c-dark-green: #081D21;\r\n    --brand-color: #EE5131;\r\n    --c-brand-light: #F3673B;\r\n    --c-brand-lighter: #FA7051;\r\n\r\n    --mobile-gap: 24px;\r\n    --tablet-gap: 60px;\r\n    --desktop-gap: 80px;\r\n    --desktopxl-gap: 96px;\r\n    --max-page-width: 1380px;\r\n\r\n    --border-space: 24px; // valeur des marges de la page;\r\n    --spacing-l: 48px;\r\n\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n      --border-space: 60px;\r\n      --spacing-l: 52px;\r\n    }\r\n\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop} {\r\n      --border-space: 64px;\r\n      --spacing-l: 96px;\r\n    }\r\n\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktopXL} {\r\n      --border-space: calc((100vw + 32px - var(--max-page-width)) / 2);\r\n      --spacing-l: 96px;\r\n    }\r\n\r\n    // Spacing\r\n    --fluid-space-m: clamp(3rem, 0.22rem + 5.97vw, 5rem);\r\n  }\r\n\r\n  body {\r\n    width: 100%;\r\n    overflow-x: hidden;\r\n    //overflow-y: scroll;\r\n    margin: auto;\r\n    padding-top: 80px;\r\n    background-color: var(--soft-white);\r\n    font-family: Stelvio, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,\r\n    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;\r\n  }\r\n\r\n  html {\r\n    width: 100%;\r\n    background-color: white;\r\n    //background-color: var(--soft-white);\r\n    overscroll-behavior-y: none;\r\n  }\r\n\r\n  .site-padding {\r\n    padding-left: var(--mobile-gap);\r\n    padding-right: var(--mobile-gap);\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n      padding-left: var(--tablet-gap);\r\n      padding-right: var(--tablet-gap);\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop} {\r\n      padding-left: var(--desktop-gap);\r\n      padding-right: var(--desktop-gap);\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktopXL} {\r\n      max-width: 1380px;\r\n      margin: auto;\r\n      padding-left: 32px;\r\n      padding-right: 32px;\r\n    }\r\n  }\r\n\r\n  .site-padding-left {\r\n    padding-left: var(--mobile-gap) !important;\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n      padding-left: var(--tablet-gap) !important;\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop} {\r\n      padding-left: var(--desktop-gap) !important;\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktopXL} {\r\n      padding-left: calc(32px + (100vw - 1380px) / 2) !important;\r\n    }\r\n  }\r\n\r\n  .site-padding-right {\r\n    padding-right: var(--mobile-gap) !important;\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n      padding-right: var(--tablet-gap) !important;\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop} {\r\n      padding-right: var(--desktop-gap) !important;\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktopXL} {\r\n      padding-right: calc(32px + (100vw - 1380px) / 2) !important;\r\n    }\r\n  }\r\n\r\n  .site-margin {\r\n    margin-left: var(--mobile-gap);\r\n    margin-right: var(--mobile-gap);\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n      margin-left: var(--mobile-gap);\r\n      margin-right: var(--mobile-gap);\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop} {\r\n      margin-left: var(--desktop-gap);\r\n      margin-right: var(--desktop-gap);\r\n    }\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktopXL} {\r\n      max-width: var(--max-page-width);\r\n      margin: auto;\r\n    }\r\n  }\r\n\r\n  .fw-background {\r\n    &:before {\r\n      content: '';\r\n      position: absolute;\r\n      width: 105vw;\r\n      height: 100%;\r\n      top: 0;\r\n      left: -24px;\r\n      @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n        left: -60px;\r\n      }\r\n      @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop} {\r\n        left: -80px;\r\n      }\r\n      @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktopXL} {\r\n        left: -96px;\r\n      }\r\n      background-color: #080808;\r\n      z-index: -1;\r\n    }\r\n  }\r\n\r\n\r\n  .mobile-hide {\r\n    display: none;\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n      display: inherit;\r\n    }\r\n  }\r\n\r\n  .mobile-hide_flex {\r\n    display: none !important;\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.tablet} {\r\n      display: flex !important;\r\n    }\r\n  }\r\n\r\n  .tablet-hide_flex {\r\n    display: none !important;\r\n    @media ${styles_device__WEBPACK_IMPORTED_MODULE_1__.device.desktop} {\r\n      display: flex !important;\r\n    }\r\n  }\r\n\r\n  .svg-filter {\r\n    height: 0;\r\n    left: -9999em;\r\n    margin: 0;\r\n    padding: 0;\r\n    position: absolute;\r\n    width: 0;\r\n  }\r\n/* \r\n  .with-duotone {\r\n    filter: url('#duotone-filter');\r\n  } */\r\n\r\n  .no-select {\r\n    * {\r\n      -webkit-touch-callout: none;\r\n      -webkit-user-select: none;\r\n      -moz-user-select: none;\r\n      -ms-user-select: none;\r\n      user-select: none;\r\n    }\r\n  }\r\n\r\n  .no-scroll {\r\n    overflow: hidden;\r\n  }\r\n  \r\n  .primary-hover:hover {\r\n\t\t  color: var(--brand-color) !important;\r\n  }\r\n`;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Global);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./styles/global.js\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "styled-components":
/*!************************************!*\
  !*** external "styled-components" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("styled-components");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./pages/_document.js")));
module.exports = __webpack_exports__;

})();