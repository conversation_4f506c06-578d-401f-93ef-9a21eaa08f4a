"use strict";exports.id=2832,exports.ids=[2832],exports.modules={1240:(t,e,i)=>{i.d(e,{Z:()=>DefaultPostCard});var a=i(997),r=i(7518),o=i.n(r),l=i(7467),s=i(5675),n=i.n(s),d=i(1664),c=i.n(d),p=i(6382),f=i(1077);i(8672),i(2558);var h=i(9280),x=i(5432),m=i(4130),g=i(7199);function DefaultPostCard({post:t,options:e,showAnimatedIcon:i=!1}){let r,o=(0,f.qt)(t);if(!o)return a.jsx(a.Fragment,{});t.author&&(r=t.author.fullName?t.author.fullName:t.author),t.date?(0,p.S$)(t.date):(0,p.S$)(t.published_at);let l=(0,m.k)(t.image);return a.jsx(c(),{href:o,children:(0,a.jsxs)(u,{children:[(0,a.jsxs)("div",{className:"dc-image-container",children:[l&&a.jsx(n(),{src:l,fill:!0,style:{objectFit:"cover"},sizes:"240px",alt:t.image?.alternativeText||""}),i&&a.jsx(h.YM,{type:t.type,colors:(0,x.Q)(t.type)})]}),(0,a.jsxs)("div",{className:"dc-text-container",children:[a.jsx("p",{className:"dc-author",children:r}),a.jsx("p",{className:"dc-title primary-hover",children:t.title}),e.showLead&&a.jsx("p",{className:"dc-lead",children:(0,g.Gq)((0,g.Kd)(t.lead?.content||""))})]})]})})}let u=o().div.withConfig({componentId:"sc-971df927-0"})`
  position: relative;
  display: flex;
  flex-direction: row-reverse;
  margin-bottom: 32px;
  padding-bottom: 32px;
  border-bottom: 1px solid #dcdcdc;

  .dc-text-container {
    width: 66%;
    margin-top: 0;
    .dc-author {
      font-family: "Lora", serif;
      font-style: italic;
      font-size: 14px;
      color: #888888;
      margin-top: 0;
      margin-bottom: 0;
    }

    .dc-date {
      font-size: 14px;
      color: #888888;
    }

    .dc-title {
      margin-top: 8px;
      margin-bottom: 0;
      margin-right: 16px;
      font-size: 22px;
      line-height: 120%;
      color: #161616;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .dc-image-container {
    position: relative;
    flex-shrink: 0;
    width: 28vw;
    //height: 28vw;
    aspect-ratio: 1/1;
    //&:after {
    //  content: "";
    //  display: block;
    //  padding-bottom: 100%;
    //}
  }

  @media ${l.U.tablet} {
    flex-direction: row;
    border-bottom: 1px solid #ffffff;
    cursor: pointer;
    .dc-text-container {
      //flex-grow: 1;
      .dc-title {
        font-size: 32px;
        margin-top: 12px;
      }
      .dc-author {
        font-size: 16px;
      }
    }
    .dc-image-container {
      margin-right: 32px;
      width: 30%;
      height: 100%;
      aspect-ratio: 1/1;
    }
    .dc-topics {
      margin-top: 16px;
      .card-label {
        margin-right: 8px;
      }
    }

  }
`},2832:(t,e,i)=>{i.d(e,{Zs:()=>a.Z,F3:()=>FirstArticleCard,Zo:()=>SquareVerticalCard,h3:()=>SquareVerticalFeatured});var a=i(1240),r=i(997),o=i(7518),l=i.n(o),s=i(6382),n=i(2558),d=i(7467),c=i(1664),p=i.n(c),f=i(1077);function SquareVerticalCard({post:t,options:e}){let i=e?.showDate?(0,s.S$)(t.date):null,a=e?.showAuthor?t.author?.fullName:null,o=(0,f.qt)(t);return(0,r.jsxs)(h,{className:"sv-card",children:[i&&r.jsx("p",{className:"sv-card-date",children:(0,s.S$)(t.date)}),r.jsx(p(),{href:o,children:r.jsx("div",{className:"sv-card-image",children:r.jsx(n.Z,{imageData:t.image})})}),a&&r.jsx("p",{className:"sv-card-author",children:a}),r.jsx(p(),{href:o,children:r.jsx("h3",{className:"sv-card-title primary-hover",children:t.title})})]})}let h=l().div.withConfig({componentId:"sc-9bfcf64e-0"})`
  position: relative;
  overflow: auto;
  width: 100%;

  &:hover {
    .image-glow {
      filter: blur(20px);
    }
    cursor: pointer;
  }
  
  .absolute {
    position: absolute;
  }

  .sv-card-date {
    font-family: "Novela", "Lora", sans-serif;
    font-size: 14px;
    color: #888888;
    margin-bottom: 8px;
    font-weight: 400;
    margin-top: 0;
  }

  .sv-card-image {
    position: relative;
    aspect-ratio: 1/1;
    width: 100%;
    cursor: pointer;
  }

  .sv-card-author {
    font-size: 14px;
    font-family: "Lora", serif;
    font-style: italic;
    color: #888888;
    margin-top: 18px;
    margin-bottom: 0;
  }

  .sv-card-title {
    font-size: 18px;
    font-weight: 500;
    line-height: 115%;
    color: ${t=>t.featured?"#f4f4f4":"#161616"};
  }

  @media ${d.U.tablet} {
    .sv-card-title {
      margin-top: 16px;
      margin-bottom: auto;
      font-weight: 500;
      line-height: 30px;
      font-size: 27px;
    }

    .sv-card-date {
      font-size: 18px;
    }
  }

  .image-glow {
    position: absolute;
    width: calc(100% - 48px);
    aspect-ratio: 1/1;
    filter: blur(10px);
  }
`;var x=i(9280);function FirstArticleCard({post:t,options:e}){let i=e?.showDate?(0,s.S$)(t.date):null,a=e?.showAuthor?t.author?.fullName:null,o=(0,f.qt)(t);return r.jsx(m,{className:"fa-card",children:(0,r.jsxs)(p(),{href:o,children:[i&&r.jsx("p",{className:"fa-card-date",children:(0,s.S$)(t.date)}),(0,r.jsxs)("div",{className:"fa-card-image",children:[r.jsx(n.Z,{imageData:t.image,priority:!0}),r.jsx(x.YM,{type:t.type,colors:e?.dotColors})]}),a&&r.jsx("p",{className:"fa-card-author",children:a}),r.jsx("h3",{className:"fa-card-title primary-hover",children:t.title})]})})}let m=l().div.withConfig({componentId:"sc-b1214ea6-0"})`
  width: 100%;
  grid-area: "FirstArticle";
  //overflow: auto;
  //grid-column: 1/3;

  .fa-card-date {
    font-family: "Novela", "Lora", sans-serif;
    font-size: 14px;
    color: #888888;
    margin-bottom: 8px;
    margin-top: 0;
    font-weight: 400;
  }

  .fa-card-image {
    position: relative;
    width: 100%;
    aspect-ratio: 16/10;
  }

  .fa-card-title {
    width: 95%;
    font-size: 30px;
    font-weight: 600;
    margin-top: 18px;
    line-height: 110%;
    color: #262626;
    cursor: pointer;
  }

  .fa-card-author {
    font-size: 18px;
    font-family: "Lora", serif;
    font-style: italic;
    color: #888888;
    margin-top: 24px;
    margin-bottom: 0;
  }

  @media ${d.U.tablet} {
    //grid-column: 2/4;

    .fa-card-image {
    }

    .fa-card-title {
      font-size: 48px;
      font-weight: 500;
      line-height: 105%;
      margin-top: 32px;
    }
    .fa-card-date {
      font-size: 18px;
    }
  }
  @media ${d.U.desktop} {
    .fa-card-image {
    }
  }
`;l().div.withConfig({componentId:"sc-6e3eb749-0"})`
  position: relative;
  display: flex;
  flex: 0 0 1;
  justify-content: start;
  flex-direction: column;
  min-width: calc(80%);
  width: calc(100% - 48px);
  margin-right: 16px;
  background-color: ${t=>t.isDark?"#242424":"#f4f4f4"};
  text-align: center;
  height: 364px;

  @media ${d.U.tablet} {
    min-width: 295px;
  }

  @media ${d.U.desktop} {
    width: 25%;
    margin-right: 32px
  }
  
  .circle-image {
    position: relative;
    flex: 0 1 auto;
    margin: -25px auto 0 auto;
    width: 50px;
    height: 50px;
    border-radius: 50px;
    border-style: solid;
    border-width: 1px;
    border-color: ${t=>t.isDark?"#888888":"white"};
    background-color: ${t=>t.isDark?"#242424":"white"};
    z-index: 2;
    //overflow: hidden;
    img {
      border-radius: 50px;
    }
    
    &:before {
      content: "";
      position: absolute;
      top: -7px;
      left: -7px;
      width: 50px;
      height: 50px;
      border-radius: 50px;
      border: 6px solid ${t=>t.isDark?"#080808":"white"};
    }
  }

  .card-author {
    flex: 0 1 auto;
    font-family: "Lora", serif;
    font-style: italic;
    font-size: 16px;
    margin: 27px 24px 0 24px;
    color: #888;
  }

  h3 {
    flex: 0 1 auto;
    margin: 12px 24px 0 24px;
    font-weight: 500;
    font-size: 21px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    color: ${t=>t.isDark?"#dcdcdc":"#161616"};
  }
  
  .thumbnail {
    position: relative;
    flex: 1 1 auto;
    margin-top: 24px;
    width: 100%;
  }




`;var g=i(2570);function SquareVerticalFeatured({item:t}){return t.cta?.url?r.jsx(u,{className:"svf-card",children:r.jsx(g.Z,{link:t.cta.url,children:(0,r.jsxs)(v,{children:[r.jsx("div",{className:"svf-hidden-image",children:r.jsx("div",{className:"svf-blurred-image",children:r.jsx(n.Z,{imageData:t.image})})}),r.jsx("div",{className:"svf-image",children:r.jsx(n.Z,{imageData:t.image})}),r.jsx("h3",{className:"svf-card-title",children:t.title})]})})}):r.jsx(r.Fragment,{})}let u=l().div.withConfig({componentId:"sc-24c25690-0"})`
  position: relative;
  width: 100%;
  cursor: pointer;
`,v=l().div.withConfig({componentId:"sc-24c25690-1"})`
  background-color: #161616;
  padding: 24px;
  box-shadow: 0 7px 14px 3px rgba(0, 0, 0, 0.29);
  
  .svf-image {
    position: relative;
    width: 100%;
    aspect-ratio: 1/1;
  }
  .svf-blurred-image {
    position: relative;
  }
  .svf-card-title {
    font-size: 18px;
    font-weight: 500;
    line-height: 115%;
    color: #f4f4f4;
  }
  @media ${d.U.desktop} {
    .svf-card-title {
      
    }
  }
`}};