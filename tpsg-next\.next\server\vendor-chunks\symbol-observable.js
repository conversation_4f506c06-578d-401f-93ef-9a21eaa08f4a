"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/symbol-observable";
exports.ids = ["vendor-chunks/symbol-observable"];
exports.modules = {

/***/ "(rsc)/./node_modules/symbol-observable/lib/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/symbol-observable/lib/index.js ***!
  \*****************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/* module decorator */ module = __webpack_require__.nmd(module);\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _ponyfill = __webpack_require__(/*! ./ponyfill.js */ \"(rsc)/./node_modules/symbol-observable/lib/ponyfill.js\");\nvar _ponyfill2 = _interopRequireDefault(_ponyfill);\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        \"default\": obj\n    };\n}\nvar root; /* global window */ \nif (typeof self !== \"undefined\") {\n    root = self;\n} else if (false) {} else if (typeof global !== \"undefined\") {\n    root = global;\n} else if (true) {\n    root = module;\n} else {}\nvar result = (0, _ponyfill2[\"default\"])(root);\nexports[\"default\"] = result;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/symbol-observable/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/symbol-observable/lib/ponyfill.js":
/*!********************************************************!*\
  !*** ./node_modules/symbol-observable/lib/ponyfill.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = symbolObservablePonyfill;\nfunction symbolObservablePonyfill(root) {\n    var result;\n    var _Symbol = root.Symbol;\n    if (typeof _Symbol === \"function\") {\n        if (_Symbol.observable) {\n            result = _Symbol.observable;\n        } else {\n            if (typeof _Symbol[\"for\"] === \"function\") {\n                // This just needs to be something that won't trample other user's Symbol.for use\n                // It also will guide people to the source of their issues, if this is problematic.\n                // META: It's a resource locator!\n                result = _Symbol[\"for\"](\"https://github.com/benlesh/symbol-observable\");\n            } else {\n                // Symbol.for didn't exist! The best we can do at this point is a totally \n                // unique symbol. Note that the string argument here is a descriptor, not\n                // an identifier. This symbol is unique.\n                result = _Symbol(\"https://github.com/benlesh/symbol-observable\");\n            }\n            try {\n                _Symbol.observable = result;\n            } catch (err) {\n            // Do nothing. In some environments, users have frozen `Symbol` for security reasons,\n            // if it is frozen assigning to it will throw. In this case, we don't care, because\n            // they will need to use the returned value from the ponyfill.\n            }\n        }\n    } else {\n        result = \"@@observable\";\n    }\n    return result;\n}\n;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvc3ltYm9sLW9ic2VydmFibGUvbGliL3BvbnlmaWxsLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUFBLDhDQUE2QztJQUM1Q0csT0FBTztBQUNSLENBQUMsRUFBQztBQUNGRCxrQkFBa0IsR0FBR0U7QUFDckIsU0FBU0EseUJBQXlCQyxJQUFJO0lBQ3JDLElBQUlDO0lBQ0osSUFBSUMsVUFBVUYsS0FBS0csTUFBTTtJQUV6QixJQUFJLE9BQU9ELFlBQVksWUFBWTtRQUNsQyxJQUFJQSxRQUFRRSxVQUFVLEVBQUU7WUFDdkJILFNBQVNDLFFBQVFFLFVBQVU7UUFDNUIsT0FBTztZQUVOLElBQUksT0FBT0YsT0FBTyxDQUFDLE1BQU0sS0FBSyxZQUFZO2dCQUN6QyxpRkFBaUY7Z0JBQ2pGLG1GQUFtRjtnQkFDbkYsaUNBQWlDO2dCQUNqQ0QsU0FBU0MsT0FBTyxDQUFDLE1BQU0sQ0FBQztZQUN6QixPQUFPO2dCQUNOLDBFQUEwRTtnQkFDMUUseUVBQXlFO2dCQUN6RSx3Q0FBd0M7Z0JBQ3hDRCxTQUFTQyxRQUFRO1lBQ2xCO1lBQ0EsSUFBSTtnQkFDSEEsUUFBUUUsVUFBVSxHQUFHSDtZQUN0QixFQUFFLE9BQU9JLEtBQUs7WUFDYixxRkFBcUY7WUFDckYsbUZBQW1GO1lBQ25GLDhEQUE4RDtZQUMvRDtRQUNEO0lBQ0QsT0FBTztRQUNOSixTQUFTO0lBQ1Y7SUFFQSxPQUFPQTtBQUNSIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHBzZy1uZXh0Ly4vbm9kZV9tb2R1bGVzL3N5bWJvbC1vYnNlcnZhYmxlL2xpYi9wb255ZmlsbC5qcz80YjAwIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG5cdHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHNbJ2RlZmF1bHQnXSA9IHN5bWJvbE9ic2VydmFibGVQb255ZmlsbDtcbmZ1bmN0aW9uIHN5bWJvbE9ic2VydmFibGVQb255ZmlsbChyb290KSB7XG5cdHZhciByZXN1bHQ7XG5cdHZhciBfU3ltYm9sID0gcm9vdC5TeW1ib2w7XG5cblx0aWYgKHR5cGVvZiBfU3ltYm9sID09PSAnZnVuY3Rpb24nKSB7XG5cdFx0aWYgKF9TeW1ib2wub2JzZXJ2YWJsZSkge1xuXHRcdFx0cmVzdWx0ID0gX1N5bWJvbC5vYnNlcnZhYmxlO1xuXHRcdH0gZWxzZSB7XG5cblx0XHRcdGlmICh0eXBlb2YgX1N5bWJvbFsnZm9yJ10gPT09ICdmdW5jdGlvbicpIHtcblx0XHRcdFx0Ly8gVGhpcyBqdXN0IG5lZWRzIHRvIGJlIHNvbWV0aGluZyB0aGF0IHdvbid0IHRyYW1wbGUgb3RoZXIgdXNlcidzIFN5bWJvbC5mb3IgdXNlXG5cdFx0XHRcdC8vIEl0IGFsc28gd2lsbCBndWlkZSBwZW9wbGUgdG8gdGhlIHNvdXJjZSBvZiB0aGVpciBpc3N1ZXMsIGlmIHRoaXMgaXMgcHJvYmxlbWF0aWMuXG5cdFx0XHRcdC8vIE1FVEE6IEl0J3MgYSByZXNvdXJjZSBsb2NhdG9yIVxuXHRcdFx0XHRyZXN1bHQgPSBfU3ltYm9sWydmb3InXSgnaHR0cHM6Ly9naXRodWIuY29tL2Jlbmxlc2gvc3ltYm9sLW9ic2VydmFibGUnKTtcblx0XHRcdH0gZWxzZSB7XG5cdFx0XHRcdC8vIFN5bWJvbC5mb3IgZGlkbid0IGV4aXN0ISBUaGUgYmVzdCB3ZSBjYW4gZG8gYXQgdGhpcyBwb2ludCBpcyBhIHRvdGFsbHkgXG5cdFx0XHRcdC8vIHVuaXF1ZSBzeW1ib2wuIE5vdGUgdGhhdCB0aGUgc3RyaW5nIGFyZ3VtZW50IGhlcmUgaXMgYSBkZXNjcmlwdG9yLCBub3Rcblx0XHRcdFx0Ly8gYW4gaWRlbnRpZmllci4gVGhpcyBzeW1ib2wgaXMgdW5pcXVlLlxuXHRcdFx0XHRyZXN1bHQgPSBfU3ltYm9sKCdodHRwczovL2dpdGh1Yi5jb20vYmVubGVzaC9zeW1ib2wtb2JzZXJ2YWJsZScpO1xuXHRcdFx0fVxuXHRcdFx0dHJ5IHtcblx0XHRcdFx0X1N5bWJvbC5vYnNlcnZhYmxlID0gcmVzdWx0O1xuXHRcdFx0fSBjYXRjaCAoZXJyKSB7XG5cdFx0XHRcdC8vIERvIG5vdGhpbmcuIEluIHNvbWUgZW52aXJvbm1lbnRzLCB1c2VycyBoYXZlIGZyb3plbiBgU3ltYm9sYCBmb3Igc2VjdXJpdHkgcmVhc29ucyxcblx0XHRcdFx0Ly8gaWYgaXQgaXMgZnJvemVuIGFzc2lnbmluZyB0byBpdCB3aWxsIHRocm93LiBJbiB0aGlzIGNhc2UsIHdlIGRvbid0IGNhcmUsIGJlY2F1c2Vcblx0XHRcdFx0Ly8gdGhleSB3aWxsIG5lZWQgdG8gdXNlIHRoZSByZXR1cm5lZCB2YWx1ZSBmcm9tIHRoZSBwb255ZmlsbC5cblx0XHRcdH1cblx0XHR9XG5cdH0gZWxzZSB7XG5cdFx0cmVzdWx0ID0gJ0BAb2JzZXJ2YWJsZSc7XG5cdH1cblxuXHRyZXR1cm4gcmVzdWx0O1xufTsiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJzeW1ib2xPYnNlcnZhYmxlUG9ueWZpbGwiLCJyb290IiwicmVzdWx0IiwiX1N5bWJvbCIsIlN5bWJvbCIsIm9ic2VydmFibGUiLCJlcnIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/symbol-observable/lib/ponyfill.js\n");

/***/ })

};
;