"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/coredata/route";
exports.ids = ["app/api/coredata/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("string_decoder");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcoredata%2Froute&page=%2Fapi%2Fcoredata%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcoredata%2Froute.js&appDir=C%3A%5Crep%5CTPSG%5Ctpsg-next%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Crep%5CTPSG%5Ctpsg-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcoredata%2Froute&page=%2Fapi%2Fcoredata%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcoredata%2Froute.js&appDir=C%3A%5Crep%5CTPSG%5Ctpsg-next%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Crep%5CTPSG%5Ctpsg-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var C_rep_TPSG_tpsg_next_app_api_coredata_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/coredata/route.js */ \"(rsc)/./app/api/coredata/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/api/coredata/route\",\n        pathname: \"/api/coredata\",\n        filename: \"route\",\n        bundlePath: \"app/api/coredata/route\"\n    },\n    resolvedPagePath: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\app\\\\api\\\\coredata\\\\route.js\",\n    nextConfigOutput,\n    userland: C_rep_TPSG_tpsg_next_app_api_coredata_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/coredata/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcoredata%2Froute&page=%2Fapi%2Fcoredata%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcoredata%2Froute.js&appDir=C%3A%5Crep%5CTPSG%5Ctpsg-next%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Crep%5CTPSG%5Ctpsg-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./api/apollo-client.js":
/*!******************************!*\
  !*** ./api/apollo-client.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"(rsc)/./node_modules/@apollo/client/main.cjs\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst client = new _apollo_client__WEBPACK_IMPORTED_MODULE_0__.ApolloClient({\n    uri: \"http://127.0.0.1:1337/graphql\",\n    cache: new _apollo_client__WEBPACK_IMPORTED_MODULE_0__.InMemoryCache({\n        addTypename: false\n    }),\n    defaultOptions: {\n        query: {\n            fetchPolicy: \"no-cache\"\n        }\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (client);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcGkvYXBvbGxvLWNsaWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkQ7QUFFN0QsTUFBTUUsU0FBUyxJQUFJRix3REFBWUEsQ0FBQztJQUM5QkcsS0FBS0MsK0JBQWtDO0lBQ3ZDRyxPQUFPLElBQUlOLHlEQUFhQSxDQUFDO1FBQ3ZCTyxhQUFhO0lBQ2Y7SUFDQUMsZ0JBQWdCO1FBQ2RDLE9BQU87WUFDTEMsYUFBYTtRQUNmO0lBQ0Y7QUFDRjtBQUVBLGlFQUFlVCxNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHBzZy1uZXh0Ly4vYXBpL2Fwb2xsby1jbGllbnQuanM/NDA3NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcG9sbG9DbGllbnQsIEluTWVtb3J5Q2FjaGUgfSBmcm9tIFwiQGFwb2xsby9jbGllbnRcIjtcclxuXHJcbmNvbnN0IGNsaWVudCA9IG5ldyBBcG9sbG9DbGllbnQoe1xyXG4gIHVyaTogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1RSQVBJX0dRTCxcclxuICBjYWNoZTogbmV3IEluTWVtb3J5Q2FjaGUoe1xyXG4gICAgYWRkVHlwZW5hbWU6IGZhbHNlXHJcbiAgfSksXHJcbiAgZGVmYXVsdE9wdGlvbnM6IHtcclxuICAgIHF1ZXJ5OiB7XHJcbiAgICAgIGZldGNoUG9saWN5OiBcIm5vLWNhY2hlXCIsXHJcbiAgICB9LFxyXG4gIH1cclxufSk7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBjbGllbnQ7XHJcbiJdLCJuYW1lcyI6WyJBcG9sbG9DbGllbnQiLCJJbk1lbW9yeUNhY2hlIiwiY2xpZW50IiwidXJpIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NUUkFQSV9HUUwiLCJjYWNoZSIsImFkZFR5cGVuYW1lIiwiZGVmYXVsdE9wdGlvbnMiLCJxdWVyeSIsImZldGNoUG9saWN5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./api/apollo-client.js\n");

/***/ }),

/***/ "(rsc)/./api/gql-queries.js":
/*!****************************!*\
  !*** ./api/gql-queries.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CORE_POST_SET: () => (/* binding */ CORE_POST_SET),\n/* harmony export */   POST_TYPE_MODULES: () => (/* binding */ POST_TYPE_MODULES),\n/* harmony export */   QUERY_POPUPS: () => (/* binding */ QUERY_POPUPS),\n/* harmony export */   fragments: () => (/* binding */ fragments),\n/* harmony export */   queries: () => (/* binding */ queries)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"(rsc)/./node_modules/@apollo/client/main.cjs\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst POST_TYPE_MODULES = _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n  fragment postTypeModules on Post {\r\n    modules {\r\n      ... on ComponentModulePodcast {\r\n        podcast {\r\n          slug\r\n          name\r\n        }\r\n      }\r\n      ... on ComponentModuleFormation {\r\n        __typename\r\n        speakers {\r\n          fullName\r\n        }\r\n        link\r\n        youtubeEmbed\r\n      }\r\n    }\r\n  }\r\n`;\nconst fragments = {\n    CORE_POST_FIELDS: _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n    fragment CorePostFields on Post {\r\n      id\r\n      title\r\n      body\r\n      slug\r\n      type\r\n      readingTime\r\n      author {\r\n        fullName\r\n        firstName\r\n        lastName\r\n        about\r\n        slug\r\n        picture {\r\n          url\r\n          width\r\n          height\r\n          provider\r\n          alternativeText\r\n        }\r\n      }\r\n      published_at\r\n      image {\r\n        url\r\n        provider\r\n        alternativeText\r\n        caption\r\n        width\r\n        height\r\n      }\r\n      serie {\r\n        id\r\n        name\r\n      }\r\n      topics {\r\n        name\r\n        slug\r\n      }\r\n      blog {\r\n        blogger {\r\n          fullName\r\n          slug\r\n        }\r\n      }\r\n      modules {\r\n        __typename\r\n        ... on ComponentModuleLead {\r\n          content\r\n        }\r\n        ... on ComponentModuleSeo {\r\n          metaDescription\r\n          metaTitle\r\n        }\r\n      }\r\n    }\r\n  `\n};\nconst FULL_POST_FRAGMENT = _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n  fragment fullPostFragment on Post {\r\n    id\r\n    title\r\n    slug\r\n    type\r\n    published_at\r\n    body\r\n    author {\r\n      fullName\r\n      picture {\r\n        url\r\n        provider\r\n      }\r\n    }\r\n    image {\r\n      url\r\n      height\r\n      width\r\n      alternativeText\r\n      provider\r\n    }\r\n    topics {\r\n      name\r\n    }\r\n    modules {\r\n      ... on ComponentModuleWebinar {\r\n        __typename\r\n        webinar {\r\n          slug\r\n          name\r\n        }\r\n        embedVideo\r\n        speakers {\r\n          fullName\r\n          firstName\r\n          lastName\r\n          picture {\r\n            url\r\n            provider\r\n            size\r\n          }\r\n        }\r\n      }\r\n      ... on ComponentModulePodcast {\r\n        __typename\r\n        podcast {\r\n          slug\r\n          name\r\n        }\r\n        embedAudio\r\n        embedVideo\r\n      }\r\n      ... on ComponentModuleLead {\r\n        __typename\r\n        content\r\n      }\r\n    }\r\n  }\r\n`;\nconst queries = {\n    QUERY_TOPIC: _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n    query MainTopic($slug: String!) {\r\n      topics(where: { slug: $slug }) {\r\n        name\r\n        slug\r\n        id\r\n        postCount\r\n        description\r\n        parent {\r\n          slug\r\n          name\r\n          parent {\r\n            slug\r\n            name\r\n          }\r\n        }\r\n      }\r\n    }\r\n  `,\n    QUERY_TOPIC_GROUP: _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n    query TopicGroup($slug: String!) {\r\n      topicGroups(where: { slug: $slug }) {\r\n        name\r\n        description\r\n        cover {\r\n          formats\r\n        }\r\n        topics {\r\n          id\r\n          postCount\r\n        }\r\n        featured {\r\n          title\r\n          description\r\n          inColumn\r\n          image {\r\n            url\r\n            width\r\n            height\r\n            provider\r\n            caption\r\n            alternativeText\r\n          }\r\n        }\r\n      }\r\n    }\r\n  `,\n    QUERY_TOPICS_POSTS: _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n    query Posts($topicIds: [ID], $offset: Int!) {\r\n      posts(\r\n        limit: 20\r\n        start: $offset\r\n        where: { topics: { id_in: $topicIds } }\r\n      ) {\r\n        title\r\n        slug\r\n        published_at\r\n        image {\r\n          url\r\n          height\r\n          width\r\n          provider\r\n          caption\r\n          alternativeText\r\n        }\r\n        author {\r\n          fullName\r\n        }\r\n        topics {\r\n          slug\r\n        }\r\n        modules {\r\n          __typename\r\n          ... on ComponentModulePodcast {\r\n            podcast {\r\n              name\r\n              slug\r\n            }\r\n          }\r\n        }\r\n        type\r\n      }\r\n    }\r\n  `,\n    QUERY_RELATED: _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n    query GetRelated($id: ID!) {\r\n      relatedPosts(id: $id) {\r\n        section\r\n        score\r\n        origin\r\n        post {\r\n          id\r\n          title\r\n          slug\r\n          type\r\n          published_at\r\n          author {\r\n            fullName\r\n            picture {\r\n              url\r\n              provider\r\n            }\r\n          }\r\n          image {\r\n            url\r\n            height\r\n            width\r\n            alternativeText\r\n            provider\r\n          }\r\n          topics {\r\n            name\r\n          }\r\n          modules {\r\n            __typename\r\n            ... on ComponentModuleWebinar {\r\n              webinar {\r\n                slug\r\n                name\r\n              }\r\n              speakers {\r\n                fullName\r\n                firstName\r\n                lastName\r\n                picture {\r\n                  url\r\n                  provider\r\n                  size\r\n                }\r\n              }\r\n            }\r\n            ... on ComponentModulePodcast {\r\n              podcast {\r\n                slug\r\n                name\r\n              }\r\n            }\r\n            ... on ComponentModuleLead {\r\n              content\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  `,\n    QUERY_BLOG_POSTS: _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n    ${FULL_POST_FRAGMENT}\r\n    query BlogPosts($blog: ID!, $limit: Int!, $sort: String!) {\r\n      posts(limit: $limit, where: { blog: $blog }, sort: $sort) {\r\n        ...fullPostFragment\r\n      }\r\n    }\r\n  `,\n    QUERY_POSTS: _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n    ${FULL_POST_FRAGMENT}\r\n    query Posts($limit: Int!, $sort: String!) {\r\n      posts(limit: $limit, sort: $sort) {\r\n        ...fullPostFragment\r\n      }\r\n    }\r\n  `\n};\nconst CORE_POST_SET = _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n  fragment CorePostSet on Post {\r\n    title\r\n    slug\r\n    type\r\n    published_at\r\n    topics {\r\n      name\r\n    }\r\n    image {\r\n      url\r\n      width\r\n      height\r\n      alternativeText\r\n      provider\r\n    }\r\n    author {\r\n      fullName\r\n      picture {\r\n        url\r\n        formats\r\n      }\r\n    }\r\n    modules {\r\n      __typename\r\n      ... on ComponentModulePodcast {\r\n        podcast {\r\n          slug\r\n          name\r\n        }\r\n      }\r\n      ... on ComponentModuleFormation {\r\n        speakers {\r\n          fullName\r\n        }\r\n        link\r\n        youtubeEmbed\r\n      }\r\n    }\r\n  }\r\n`;\nconst QUERY_POPUPS = _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n  query Popups {\r\n    popups(sort: \"published_at:desc\") {\r\n      id\r\n      title\r\n      body\r\n      image {\r\n        url\r\n        provider\r\n      }\r\n      startDate\r\n      endDate\r\n      published_at\r\n      button {\r\n        name\r\n        url\r\n      }\r\n    }\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./api/gql-queries.js\n");

/***/ }),

/***/ "(rsc)/./app/api/coredata/route.js":
/*!***********************************!*\
  !*** ./app/api/coredata/route.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   revalidate: () => (/* binding */ revalidate)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @apollo/client */ \"(rsc)/./node_modules/@apollo/client/main.cjs\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var api_apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! api/apollo-client */ \"(rsc)/./api/apollo-client.js\");\n/* harmony import */ var services_feeds__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! services/feeds */ \"(rsc)/./services/feeds.js\");\n/**\r\n * API route /api/coredata\r\n * Returns coreData, making the most of NextJS's Data Cache\r\n * Cf. https://nextjs.org/docs/13/app/building-your-application/caching#data-cache\r\n * Cf. https://nextjs.org/docs/13/app/api-reference/file-conventions/route-segment-config#revalidate\r\n */ \n\n\nconst revalidate = 3600 // seconds // 3600 seconds = 1 hour\n;\nasync function GET() {\n    let coreData = await api_apollo_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].query({\n        query: QUERY_CORE_DATA\n    });\n    // Mise à jour au passage des flux RSS\n    (0,services_feeds__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(coreData.data);\n    return Response.json({\n        updatedAt: new Date(Date.now()).toString(),\n        ...coreData.data\n    });\n}\nconst QUERY_CORE_DATA = _apollo_client__WEBPACK_IMPORTED_MODULE_2__.gql`\r\n  query GetCoreData{\r\n    authors{\r\n      fullName,\r\n      firstName,\r\n      lastName,\r\n    }\r\n    blogs{\r\n      id\r\n      slug\r\n      blogger{\r\n        firstName\r\n        lastName\r\n        fullName\r\n        picture {\r\n          provider\r\n          url\r\n        }\r\n      }\r\n    }\r\n    topics{\r\n      name\r\n      slug\r\n      postCount\r\n    }\r\n    podcasts{\r\n      name\r\n      slug\r\n    }\r\n    topicGroups{\r\n      name\r\n      slug\r\n      type\r\n      children {\r\n        name\r\n        slug\r\n        type\r\n      }\r\n    }\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/coredata/route.js\n");

/***/ }),

/***/ "(rsc)/./services/feeds.js":
/*!***************************!*\
  !*** ./services/feeds.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ generateFeeds)\n/* harmony export */ });\n/* harmony import */ var feed__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! feed */ \"(rsc)/./node_modules/feed/lib/feed.js\");\n/* harmony import */ var showdown__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! showdown */ \"(rsc)/./node_modules/showdown/dist/showdown.js\");\n/* harmony import */ var showdown__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(showdown__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_components_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/components.utils */ \"(rsc)/./utils/components.utils.js\");\n/* harmony import */ var api_gql_queries__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! api/gql-queries */ \"(rsc)/./api/gql-queries.js\");\n/* harmony import */ var _api_apollo_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../api/apollo-client */ \"(rsc)/./api/apollo-client.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _utils_posts_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/posts.utils */ \"(rsc)/./utils/posts.utils.js\");\n/* harmony import */ var _utils_string_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/string.utils */ \"(rsc)/./utils/string.utils.js\");\n\n\n\n\n\n\n\n\nconst APP_URL = \"http://127.0.0.1:3000\";\n// const FEED_LENGTH = process.env.FEED_LENGTH || 10;\nfunction generateFeeds(coreData) {\n    generateMainFeed();\n    generateBlogFeeds(coreData.blogs);\n}\nasync function generateMainFeed() {\n    // Récupère les posts pour le flux principal\n    const posts = await _api_apollo_client__WEBPACK_IMPORTED_MODULE_4__[\"default\"].query({\n        query: api_gql_queries__WEBPACK_IMPORTED_MODULE_3__.queries.QUERY_POSTS,\n        variables: {\n            limit: 10,\n            sort: \"published_at:DESC\"\n        }\n    }).then((response)=>{\n        return response.data.posts;\n    });\n    const now = new Date();\n    // Paramètres du flux principal\n    const feedHeader = {\n        title: \"TPSG\",\n        id: APP_URL,\n        link: APP_URL,\n        description: \"ToutPourSaGloire.com, c'est des milliers de ressources pour vous aider \\xe0 mener une vie qui glorifie Dieu\",\n        language: \"fr\",\n        updated: now,\n        copyright: `All rights reserved ${now.getFullYear()}, ToutPourSaGloire`,\n        author: {\n            name: \"ToutPourSaGloire.com\",\n            email: \"<EMAIL>\"\n        }\n    };\n    // Initialise le flux RSS\n    let feed = new feed__WEBPACK_IMPORTED_MODULE_0__.Feed(feedHeader);\n    // Ajoute les posts au flux RSS\n    posts.forEach((post)=>{\n        feed.addItem(preparePost(post));\n    });\n    // Enregistre le flux RSS\n    fs__WEBPACK_IMPORTED_MODULE_5__.writeFileSync(\"./public/main-rss.xml\", feed.rss2());\n}\nasync function generateBlogFeeds(blogs) {\n    let queryOptions = {\n        query: api_gql_queries__WEBPACK_IMPORTED_MODULE_3__.queries.QUERY_BLOG_POSTS,\n        variables: {\n            limit: 10,\n            sort: \"published_at:DESC\"\n        }\n    };\n    for (let blog of blogs){\n        queryOptions.variables.blog = blog.id;\n        // Récupère les posts du blog\n        const posts = await _api_apollo_client__WEBPACK_IMPORTED_MODULE_4__[\"default\"].query(queryOptions).then((response)=>{\n            return response.data.posts;\n        });\n        const now = new Date();\n        const feedHeader = {\n            title: blog.blogger.fullName,\n            id: `${APP_URL}/${blog.slug}`,\n            link: `${APP_URL}/${blog.slug}`,\n            description: `Blog de ${blog.blogger.fullName} sur le site toutpoursagloire.com`,\n            language: \"fr\",\n            updated: now,\n            copyright: `All rights reserved ${now.getFullYear()}, ToutPourSaGloire`,\n            author: {\n                name: blog.blogger.fullname,\n                email: `${blog.blogger.fullName}@toutpoursagloire.com`\n            }\n        };\n        // Initialise le flux RSS\n        let feed = new feed__WEBPACK_IMPORTED_MODULE_0__.Feed(feedHeader);\n        // Ajoute les posts au flux RSS\n        posts.forEach((post)=>{\n            feed.addItem(preparePost(post));\n        });\n        let filePath = `./public/${blog.slug}-rss.xml`;\n        // Enregistre le flux RSS\n        fs__WEBPACK_IMPORTED_MODULE_5__.writeFileSync(filePath, feed.rss2());\n    }\n}\n// Prépare les posts pour le flux RSS\nfunction preparePost(post) {\n    // Initialise le convertisseur Markdown\n    let converter = new (showdown__WEBPACK_IMPORTED_MODULE_1___default().Converter)();\n    let body = converter.makeHtml(post.body.replace(/`/g, \"\"));\n    if (post.type === \"webinaire\" || post.type === \"podcast\") {\n        body = getEmbed(post) + body;\n    }\n    const postModule = (0,_utils_components_utils__WEBPACK_IMPORTED_MODULE_2__.modulesAsObj)(post.modules);\n    let lead = postModule?.lead?.content ? postModule.lead.content : \"\";\n    lead = (0,_utils_string_utils__WEBPACK_IMPORTED_MODULE_7__.removeMarkdown)(lead);\n    return {\n        title: post.title,\n        // id: post.slug,\n        link: `${APP_URL}${(0,_utils_posts_utils__WEBPACK_IMPORTED_MODULE_6__.getPostRoute)(post)}`,\n        content: body,\n        description: truncate(lead),\n        date: new Date(post.published_at)\n    };\n}\nfunction truncate(str) {\n    let res = str;\n    const spaceCount = str.split(\" \").length - 1;\n    if (spaceCount > 50) {\n        res = str.split(\" \").splice(0, 50).join(\" \");\n        res = res + \"...\";\n    }\n    return res;\n}\nfunction getEmbed(post) {\n    const postModules = (0,_utils_components_utils__WEBPACK_IMPORTED_MODULE_2__.modulesAsObj)(post.modules);\n    if (postModules.podcast) {\n        return postModules.podcast.embedAudio || postModules.podcast.embedVideo;\n    }\n    if (postModules.webinar) {\n        return postModules.webinar.embedVideo;\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./services/feeds.js\n");

/***/ }),

/***/ "(rsc)/./utils/components.utils.js":
/*!***********************************!*\
  !*** ./utils/components.utils.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getChannelSlug: () => (/* binding */ getChannelSlug),\n/* harmony export */   getModuleWithShortName: () => (/* binding */ getModuleWithShortName),\n/* harmony export */   menuAsObj: () => (/* binding */ menuAsObj),\n/* harmony export */   modulesAsObj: () => (/* binding */ modulesAsObj)\n/* harmony export */ });\nconst MODULE_NAMES = {\n    \"lead\": \"ComponentModuleLead\",\n    \"webinar\": \"ComponentModuleWebinar\",\n    \"podcast\": \"ComponentModulePodcast\",\n    \"journey\": \"ComponentModuleEmailJourney\",\n    \"formation\": \"ComponentModuleFormation\",\n    \"seo\": \"ComponentModuleSeo\"\n};\nfunction getModuleWithShortName(modules, shortName) {\n    return modules.find(function(module) {\n        return module.__typename === MODULE_NAMES[shortName];\n    });\n}\n/**\r\n * Conversion d'un tableau de modules en objet.\r\n * Cette fonction est utilisée pour simplifier l'accès aux différents modules\r\n * associés aux posts / pages.\r\n * @param modules\r\n * @returns {{}}\r\n */ function modulesAsObj(modules) {\n    if (!modules) return null;\n    let modulesObj = {};\n    modules.forEach((module)=>{\n        switch(module.__typename){\n            case \"ComponentModuleLead\":\n                modulesObj.lead = module;\n                break;\n            case \"ComponentModuleWebinar\":\n                modulesObj.webinar = module;\n                break;\n            case \"ComponentModuleEmailJourney\":\n                modulesObj.journey = module;\n                break;\n            case \"ComponentModuleEvent\":\n                modulesObj.event = module;\n                break;\n            case \"ComponentModuleFormation\":\n                modulesObj.formation = module;\n                break;\n            case \"ComponentModuleSeo\":\n                modulesObj.seo = module;\n                break;\n            case \"ComponentModulePodcast\":\n                modulesObj.podcast = module;\n                break;\n        }\n    });\n    return modulesObj;\n}\nfunction menuAsObj(menu) {\n    if (!menu.length) return null;\n    let menuObj = {\n        groups: [],\n        singles: []\n    };\n    menu.forEach((item)=>{\n        if (item.label.includes(\"/\")) {\n            let groupName = item.label.split(\"/\")[0];\n            if (!menuObj.groups.some((groupItem)=>groupItem.name === groupName)) {\n                menuObj.groups.push({\n                    name: groupName,\n                    items: []\n                });\n            }\n            for (const obj of menuObj.groups){\n                if (obj.name === groupName) {\n                    obj.items.push({\n                        label: item.label.split(\"/\")[1],\n                        value: item.value,\n                        type: item.type\n                    });\n                    break;\n                }\n            }\n        } else {\n            menuObj.singles.push(item);\n        }\n    });\n    return menuObj;\n}\n/**\r\n *\r\n * @param post\r\n * @param {string} type doit être 'podcast' ou 'webinar'\r\n * @return {*|null}\r\n */ function getChannelSlug(post, type) {\n    let { webinar } = getModuleWithShortName(post.modules, type);\n    return webinar?.slug || null;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./utils/components.utils.js\n");

/***/ }),

/***/ "(rsc)/./utils/image-utils.js":
/*!******************************!*\
  !*** ./utils/image-utils.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withRealSrc: () => (/* binding */ withRealSrc)\n/* harmony export */ });\nfunction withRealSrc(image) {\n    if (image?.provider === \"local\") {\n        return \"http://localhost:1337\" + image.url;\n    }\n    if (image?.url) return image.url;\n    return undefined;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi91dGlscy9pbWFnZS11dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsU0FBU0EsWUFBWUMsS0FBSztJQUN4QixJQUFJQSxPQUFPQyxhQUFhLFNBQVM7UUFDL0IsT0FBTywwQkFBMEJELE1BQU1FLEdBQUc7SUFDNUM7SUFDQSxJQUFJRixPQUFPRSxLQUFLLE9BQU9GLE1BQU1FLEdBQUc7SUFDaEMsT0FBT0M7QUFDVDtBQUlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHBzZy1uZXh0Ly4vdXRpbHMvaW1hZ2UtdXRpbHMuanM/NDRlNiJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiB3aXRoUmVhbFNyYyhpbWFnZSkge1xyXG4gIGlmIChpbWFnZT8ucHJvdmlkZXIgPT09IFwibG9jYWxcIikge1xyXG4gICAgcmV0dXJuIFwiaHR0cDovL2xvY2FsaG9zdDoxMzM3XCIgKyBpbWFnZS51cmxcclxuICB9XHJcbiAgaWYgKGltYWdlPy51cmwpIHJldHVybiBpbWFnZS51cmw7XHJcbiAgcmV0dXJuIHVuZGVmaW5lZDtcclxufVxyXG5cclxuZXhwb3J0IHtcclxuICB3aXRoUmVhbFNyY1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ3aXRoUmVhbFNyYyIsImltYWdlIiwicHJvdmlkZXIiLCJ1cmwiLCJ1bmRlZmluZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./utils/image-utils.js\n");

/***/ }),

/***/ "(rsc)/./utils/posts.utils.js":
/*!******************************!*\
  !*** ./utils/posts.utils.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPostLead: () => (/* binding */ getPostLead),\n/* harmony export */   getPostRoute: () => (/* binding */ getPostRoute),\n/* harmony export */   getPostSpeakers: () => (/* binding */ getPostSpeakers)\n/* harmony export */ });\n/* harmony import */ var _components_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components.utils */ \"(rsc)/./utils/components.utils.js\");\n/* harmony import */ var _string_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./string.utils */ \"(rsc)/./utils/string.utils.js\");\n/* harmony import */ var _image_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./image-utils */ \"(rsc)/./utils/image-utils.js\");\n\n\n\n//TODO: Vérifier les appels à cette fonction.\n// OGérer les cas des parcours email.\nfunction getPostRoute(post) {\n    if (post.route) {\n        // Correction: éviter les doubles slashes\n        // Si la route commence déjà par \"/\", la retourner telle quelle\n        if (post.route.startsWith(\"/\")) {\n            return post.route;\n        }\n        // Sinon, ajouter le \"/\" au début\n        return \"/\" + post.route;\n    }\n    const type = post.type || \"undefined\";\n    switch(type){\n        case \"article\":\n            return `/article/${post.slug}`;\n        case \"webinaire\":\n            return `/webinaires/${post.slug}`;\n        case \"podcast\":\n            const { podcast } = (0,_components_utils__WEBPACK_IMPORTED_MODULE_0__.modulesAsObj)(post.modules);\n            if (!podcast?.podcast) {\n                return null;\n            }\n            return `/podcasts/${podcast.podcast.slug}/${post.slug}`;\n        case \"formation\":\n            let route = post.modules?.formation?.link;\n            return route ? route : `/article/${post.slug}`;\n        case \"parcours\":\n            // TODO: Faire des trucs ici parce que ça va pas hein...\n            return `/parcours-emails/${post.slug}`;\n        // const { journey } = journey.link;\n        // return journey.link\n        default:\n            return `/article/${post.slug}`;\n    }\n}\n// TODO: On ne devrait jamais avoir à utiliser le body pour obtenur le lead de l'article.\n//  Le Lead est normalement déjà créé de cette façon quand l'article est créé sur Meilisearch.\n//  Récupérer le corps des articles sur Strapi pour garnir les listes est trop lourd.\nfunction getPostLead(post) {\n    let lead = \"\";\n    if (post.lead && post.lead !== \"\") {\n        // Meilisearch Data\n        lead = post.lead;\n    } else {\n        // Strapi Data\n        const postModule = (0,_components_utils__WEBPACK_IMPORTED_MODULE_0__.modulesAsObj)(post.modules);\n        lead = postModule?.lead?.content ? postModule.lead.content : post.body?.slice(0, 255) || \"\";\n        lead = (0,_string_utils__WEBPACK_IMPORTED_MODULE_1__.removeHtml)((0,_string_utils__WEBPACK_IMPORTED_MODULE_1__.removeMarkdown)(lead));\n    }\n    return lead;\n}\nfunction getPostSpeakers(post) {\n    const modules = (0,_components_utils__WEBPACK_IMPORTED_MODULE_0__.modulesAsObj)(post.modules);\n    let speakers;\n    if (modules?.webinar) {\n        speakers = modules.webinar.speakers;\n    }\n    if (modules?.formation) {\n        speakers = modules.formation.speakers;\n    }\n    if (!speakers) return null;\n    let res = {\n        pictures: [],\n        names: []\n    };\n    for (const speaker of speakers){\n        res.pictures.push((0,_image_utils__WEBPACK_IMPORTED_MODULE_2__.withRealSrc)(speaker.picture));\n        res.names.push(`${speaker.firstName.charAt(0)}. ${speaker.lastName}`);\n    }\n    return {\n        pictures: res.pictures,\n        names: speakersNamesToString(res.names)\n    };\n}\nfunction speakersNamesToString(names) {\n    let separator = names.length > 2 ? \", \" : \" et \";\n    let res = \"\";\n    for (const [i, name] of names.entries()){\n        if (i > 0) {\n            res += separator + name;\n            separator = \" et \";\n        } else {\n            res += name;\n        }\n    }\n    return res;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi91dGlscy9wb3N0cy51dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBa0Q7QUFDVTtBQUNoQjtBQUU1Qyw2Q0FBNkM7QUFDN0MscUNBQXFDO0FBQ3JDLFNBQVNJLGFBQWFDLElBQUk7SUFFeEIsSUFBSUEsS0FBS0MsS0FBSyxFQUFFO1FBQ2QseUNBQXlDO1FBQ3pDLCtEQUErRDtRQUMvRCxJQUFJRCxLQUFLQyxLQUFLLENBQUNDLFVBQVUsQ0FBQyxNQUFNO1lBQzlCLE9BQU9GLEtBQUtDLEtBQUs7UUFDbkI7UUFDQSxpQ0FBaUM7UUFDakMsT0FBTyxNQUFNRCxLQUFLQyxLQUFLO0lBQ3pCO0lBRUEsTUFBTUUsT0FBT0gsS0FBS0csSUFBSSxJQUFJO0lBRTFCLE9BQVFBO1FBQ04sS0FBSztZQUNILE9BQU8sQ0FBQyxTQUFTLEVBQUVILEtBQUtJLElBQUksQ0FBQyxDQUFDO1FBQ2hDLEtBQUs7WUFDSCxPQUFPLENBQUMsWUFBWSxFQUFFSixLQUFLSSxJQUFJLENBQUMsQ0FBQztRQUNuQyxLQUFLO1lBQ0gsTUFBTSxFQUFFQyxPQUFPLEVBQUUsR0FBR1YsK0RBQVlBLENBQUNLLEtBQUtNLE9BQU87WUFDN0MsSUFBSSxDQUFDRCxTQUFTQSxTQUFTO2dCQUNyQixPQUFPO1lBQ1Q7WUFDQSxPQUFPLENBQUMsVUFBVSxFQUFFQSxRQUFRQSxPQUFPLENBQUNELElBQUksQ0FBQyxDQUFDLEVBQUVKLEtBQUtJLElBQUksQ0FBQyxDQUFDO1FBQ3pELEtBQUs7WUFDSCxJQUFJSCxRQUFRRCxLQUFLTSxPQUFPLEVBQUVDLFdBQVdDO1lBQ3JDLE9BQU9QLFFBQVFBLFFBQVEsQ0FBQyxTQUFTLEVBQUVELEtBQUtJLElBQUksQ0FBQyxDQUFDO1FBQ2hELEtBQUs7WUFDSCx3REFBd0Q7WUFDeEQsT0FBTyxDQUFDLGlCQUFpQixFQUFFSixLQUFLSSxJQUFJLENBQUMsQ0FBQztRQUN4QyxvQ0FBb0M7UUFDcEMsc0JBQXNCO1FBQ3RCO1lBQ0UsT0FBTyxDQUFDLFNBQVMsRUFBRUosS0FBS0ksSUFBSSxDQUFDLENBQUM7SUFDbEM7QUFDRjtBQUVBLHlGQUF5RjtBQUN6Riw4RkFBOEY7QUFDOUYscUZBQXFGO0FBQ3JGLFNBQVNLLFlBQVlULElBQUk7SUFDdkIsSUFBSVUsT0FBTztJQUNYLElBQUlWLEtBQUtVLElBQUksSUFBSVYsS0FBS1UsSUFBSSxLQUFLLElBQUk7UUFDakMsbUJBQW1CO1FBQ25CQSxPQUFPVixLQUFLVSxJQUFJO0lBQ2xCLE9BQU87UUFDTCxjQUFjO1FBQ2QsTUFBTUMsYUFBYWhCLCtEQUFZQSxDQUFDSyxLQUFLTSxPQUFPO1FBQzVDSSxPQUFPQyxZQUFZRCxNQUFNRSxVQUFVRCxXQUFXRCxJQUFJLENBQUNFLE9BQU8sR0FBR1osS0FBS2EsSUFBSSxFQUFFQyxNQUFNLEdBQUcsUUFBUTtRQUN6RkosT0FBT2QseURBQVVBLENBQUNDLDZEQUFjQSxDQUFDYTtJQUNuQztJQUNBLE9BQU9BO0FBQ1Q7QUFFQSxTQUFTSyxnQkFBZ0JmLElBQUk7SUFFM0IsTUFBTU0sVUFBVVgsK0RBQVlBLENBQUNLLEtBQUtNLE9BQU87SUFDekMsSUFBSVU7SUFFSixJQUFJVixTQUFTVyxTQUFTO1FBQ3BCRCxXQUFXVixRQUFRVyxPQUFPLENBQUNELFFBQVE7SUFDckM7SUFDQSxJQUFJVixTQUFTQyxXQUFXO1FBQ3RCUyxXQUFXVixRQUFRQyxTQUFTLENBQUNTLFFBQVE7SUFDdkM7SUFFQSxJQUFJLENBQUNBLFVBQVUsT0FBTztJQUV0QixJQUFJRSxNQUFNO1FBQ1JDLFVBQVUsRUFBRTtRQUNaQyxPQUFPLEVBQUU7SUFDWDtJQUVBLEtBQUssTUFBTUMsV0FBV0wsU0FBVTtRQUM5QkUsSUFBSUMsUUFBUSxDQUFDRyxJQUFJLENBQUN4Qix5REFBV0EsQ0FBQ3VCLFFBQVFFLE9BQU87UUFDN0NMLElBQUlFLEtBQUssQ0FBQ0UsSUFBSSxDQUFDLENBQUMsRUFBRUQsUUFBUUcsU0FBUyxDQUFDQyxNQUFNLENBQUMsR0FBRyxFQUFFLEVBQUVKLFFBQVFLLFFBQVEsQ0FBQyxDQUFDO0lBQ3RFO0lBRUEsT0FBTztRQUNMUCxVQUFVRCxJQUFJQyxRQUFRO1FBQ3RCQyxPQUFPTyxzQkFBc0JULElBQUlFLEtBQUs7SUFDeEM7QUFDRjtBQUVBLFNBQVNPLHNCQUFzQlAsS0FBSztJQUNsQyxJQUFJUSxZQUFZUixNQUFNUyxNQUFNLEdBQUcsSUFBSSxPQUFPO0lBQzFDLElBQUlYLE1BQU07SUFFVixLQUFLLE1BQU0sQ0FBQ1ksR0FBR0MsS0FBSyxJQUFJWCxNQUFNWSxPQUFPLEdBQUk7UUFDdkMsSUFBSUYsSUFBSSxHQUFHO1lBQ1RaLE9BQU9VLFlBQVlHO1lBQ25CSCxZQUFZO1FBQ2QsT0FBTztZQUNMVixPQUFPYTtRQUNUO0lBQ0Y7SUFDQSxPQUFPYjtBQUNUO0FBTUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90cHNnLW5leHQvLi91dGlscy9wb3N0cy51dGlscy5qcz8zMjQ5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG1vZHVsZXNBc09iaiB9IGZyb20gXCIuL2NvbXBvbmVudHMudXRpbHNcIjtcclxuaW1wb3J0IHsgcmVtb3ZlSHRtbCwgcmVtb3ZlTWFya2Rvd24gfSBmcm9tIFwiLi9zdHJpbmcudXRpbHNcIjtcclxuaW1wb3J0IHsgd2l0aFJlYWxTcmMgfSBmcm9tIFwiLi9pbWFnZS11dGlsc1wiO1xyXG5cclxuLy9UT0RPOiBWw6lyaWZpZXIgbGVzIGFwcGVscyDDoCBjZXR0ZSBmb25jdGlvbi5cclxuLy8gT0fDqXJlciBsZXMgY2FzIGRlcyBwYXJjb3VycyBlbWFpbC5cclxuZnVuY3Rpb24gZ2V0UG9zdFJvdXRlKHBvc3QpIHtcclxuXHJcbiAgaWYgKHBvc3Qucm91dGUpIHtcclxuICAgIC8vIENvcnJlY3Rpb246IMOpdml0ZXIgbGVzIGRvdWJsZXMgc2xhc2hlc1xyXG4gICAgLy8gU2kgbGEgcm91dGUgY29tbWVuY2UgZMOpasOgIHBhciBcIi9cIiwgbGEgcmV0b3VybmVyIHRlbGxlIHF1ZWxsZVxyXG4gICAgaWYgKHBvc3Qucm91dGUuc3RhcnRzV2l0aChcIi9cIikpIHtcclxuICAgICAgcmV0dXJuIHBvc3Qucm91dGU7XHJcbiAgICB9XHJcbiAgICAvLyBTaW5vbiwgYWpvdXRlciBsZSBcIi9cIiBhdSBkw6lidXRcclxuICAgIHJldHVybiBcIi9cIiArIHBvc3Qucm91dGU7XHJcbiAgfVxyXG5cclxuICBjb25zdCB0eXBlID0gcG9zdC50eXBlIHx8IFwidW5kZWZpbmVkXCJcclxuXHJcbiAgc3dpdGNoICh0eXBlKSB7XHJcbiAgICBjYXNlIFwiYXJ0aWNsZVwiOlxyXG4gICAgICByZXR1cm4gYC9hcnRpY2xlLyR7cG9zdC5zbHVnfWBcclxuICAgIGNhc2UgXCJ3ZWJpbmFpcmVcIjpcclxuICAgICAgcmV0dXJuIGAvd2ViaW5haXJlcy8ke3Bvc3Quc2x1Z31gXHJcbiAgICBjYXNlIFwicG9kY2FzdFwiOlxyXG4gICAgICBjb25zdCB7IHBvZGNhc3QgfSA9IG1vZHVsZXNBc09iaihwb3N0Lm1vZHVsZXMpXHJcbiAgICAgIGlmICghcG9kY2FzdD8ucG9kY2FzdCkge1xyXG4gICAgICAgIHJldHVybiBudWxsXHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuIGAvcG9kY2FzdHMvJHtwb2RjYXN0LnBvZGNhc3Quc2x1Z30vJHtwb3N0LnNsdWd9YFxyXG4gICAgY2FzZSBcImZvcm1hdGlvblwiOlxyXG4gICAgICBsZXQgcm91dGUgPSBwb3N0Lm1vZHVsZXM/LmZvcm1hdGlvbj8ubGlua1xyXG4gICAgICByZXR1cm4gcm91dGUgPyByb3V0ZSA6IGAvYXJ0aWNsZS8ke3Bvc3Quc2x1Z31gXHJcbiAgICBjYXNlIFwicGFyY291cnNcIjpcclxuICAgICAgLy8gVE9ETzogRmFpcmUgZGVzIHRydWNzIGljaSBwYXJjZSBxdWUgw6dhIHZhIHBhcyBoZWluLi4uXHJcbiAgICAgIHJldHVybiBgL3BhcmNvdXJzLWVtYWlscy8ke3Bvc3Quc2x1Z31gXHJcbiAgICAvLyBjb25zdCB7IGpvdXJuZXkgfSA9IGpvdXJuZXkubGluaztcclxuICAgIC8vIHJldHVybiBqb3VybmV5LmxpbmtcclxuICAgIGRlZmF1bHQ6XHJcbiAgICAgIHJldHVybiBgL2FydGljbGUvJHtwb3N0LnNsdWd9YDtcclxuICB9XHJcbn1cclxuXHJcbi8vIFRPRE86IE9uIG5lIGRldnJhaXQgamFtYWlzIGF2b2lyIMOgIHV0aWxpc2VyIGxlIGJvZHkgcG91ciBvYnRlbnVyIGxlIGxlYWQgZGUgbCdhcnRpY2xlLlxyXG4vLyAgTGUgTGVhZCBlc3Qgbm9ybWFsZW1lbnQgZMOpasOgIGNyw6nDqSBkZSBjZXR0ZSBmYcOnb24gcXVhbmQgbCdhcnRpY2xlIGVzdCBjcsOpw6kgc3VyIE1laWxpc2VhcmNoLlxyXG4vLyAgUsOpY3Vww6lyZXIgbGUgY29ycHMgZGVzIGFydGljbGVzIHN1ciBTdHJhcGkgcG91ciBnYXJuaXIgbGVzIGxpc3RlcyBlc3QgdHJvcCBsb3VyZC5cclxuZnVuY3Rpb24gZ2V0UG9zdExlYWQocG9zdCkge1xyXG4gIGxldCBsZWFkID0gXCJcIjtcclxuICBpZiAocG9zdC5sZWFkICYmIHBvc3QubGVhZCAhPT0gXCJcIikge1xyXG4gICAgLy8gTWVpbGlzZWFyY2ggRGF0YVxyXG4gICAgbGVhZCA9IHBvc3QubGVhZDtcclxuICB9IGVsc2Uge1xyXG4gICAgLy8gU3RyYXBpIERhdGFcclxuICAgIGNvbnN0IHBvc3RNb2R1bGUgPSBtb2R1bGVzQXNPYmoocG9zdC5tb2R1bGVzKTtcclxuICAgIGxlYWQgPSBwb3N0TW9kdWxlPy5sZWFkPy5jb250ZW50ID8gcG9zdE1vZHVsZS5sZWFkLmNvbnRlbnQgOiBwb3N0LmJvZHk/LnNsaWNlKDAsIDI1NSkgfHwgXCJcIjtcclxuICAgIGxlYWQgPSByZW1vdmVIdG1sKHJlbW92ZU1hcmtkb3duKGxlYWQpKTtcclxuICB9XHJcbiAgcmV0dXJuIGxlYWQ7XHJcbn1cclxuXHJcbmZ1bmN0aW9uIGdldFBvc3RTcGVha2Vycyhwb3N0KSB7XHJcblxyXG4gIGNvbnN0IG1vZHVsZXMgPSBtb2R1bGVzQXNPYmoocG9zdC5tb2R1bGVzKTtcclxuICBsZXQgc3BlYWtlcnM7XHJcblxyXG4gIGlmIChtb2R1bGVzPy53ZWJpbmFyKSB7XHJcbiAgICBzcGVha2VycyA9IG1vZHVsZXMud2ViaW5hci5zcGVha2VycztcclxuICB9XHJcbiAgaWYgKG1vZHVsZXM/LmZvcm1hdGlvbikge1xyXG4gICAgc3BlYWtlcnMgPSBtb2R1bGVzLmZvcm1hdGlvbi5zcGVha2VycztcclxuICB9XHJcblxyXG4gIGlmICghc3BlYWtlcnMpIHJldHVybiBudWxsO1xyXG5cclxuICBsZXQgcmVzID0ge1xyXG4gICAgcGljdHVyZXM6IFtdLFxyXG4gICAgbmFtZXM6IFtdLFxyXG4gIH1cclxuXHJcbiAgZm9yIChjb25zdCBzcGVha2VyIG9mIHNwZWFrZXJzKSB7XHJcbiAgICByZXMucGljdHVyZXMucHVzaCh3aXRoUmVhbFNyYyhzcGVha2VyLnBpY3R1cmUpKTtcclxuICAgIHJlcy5uYW1lcy5wdXNoKGAke3NwZWFrZXIuZmlyc3ROYW1lLmNoYXJBdCgwKX0uICR7c3BlYWtlci5sYXN0TmFtZX1gKVxyXG4gIH1cclxuXHJcbiAgcmV0dXJuIHtcclxuICAgIHBpY3R1cmVzOiByZXMucGljdHVyZXMsXHJcbiAgICBuYW1lczogc3BlYWtlcnNOYW1lc1RvU3RyaW5nKHJlcy5uYW1lcylcclxuICB9XHJcbn1cclxuXHJcbmZ1bmN0aW9uIHNwZWFrZXJzTmFtZXNUb1N0cmluZyhuYW1lcykge1xyXG4gIGxldCBzZXBhcmF0b3IgPSBuYW1lcy5sZW5ndGggPiAyID8gXCIsIFwiIDogXCIgZXQgXCJcclxuICBsZXQgcmVzID0gXCJcIjtcclxuXHJcbiAgZm9yIChjb25zdCBbaSwgbmFtZV0gb2YgbmFtZXMuZW50cmllcygpKSB7XHJcbiAgICBpZiAoaSA+IDApIHtcclxuICAgICAgcmVzICs9IHNlcGFyYXRvciArIG5hbWU7XHJcbiAgICAgIHNlcGFyYXRvciA9IFwiIGV0IFwiO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgcmVzICs9IG5hbWVcclxuICAgIH1cclxuICB9XHJcbiAgcmV0dXJuIHJlcztcclxufVxyXG5cclxuZXhwb3J0IHtcclxuICBnZXRQb3N0Um91dGUsXHJcbiAgZ2V0UG9zdExlYWQsXHJcbiAgZ2V0UG9zdFNwZWFrZXJzXHJcbn1cclxuIl0sIm5hbWVzIjpbIm1vZHVsZXNBc09iaiIsInJlbW92ZUh0bWwiLCJyZW1vdmVNYXJrZG93biIsIndpdGhSZWFsU3JjIiwiZ2V0UG9zdFJvdXRlIiwicG9zdCIsInJvdXRlIiwic3RhcnRzV2l0aCIsInR5cGUiLCJzbHVnIiwicG9kY2FzdCIsIm1vZHVsZXMiLCJmb3JtYXRpb24iLCJsaW5rIiwiZ2V0UG9zdExlYWQiLCJsZWFkIiwicG9zdE1vZHVsZSIsImNvbnRlbnQiLCJib2R5Iiwic2xpY2UiLCJnZXRQb3N0U3BlYWtlcnMiLCJzcGVha2VycyIsIndlYmluYXIiLCJyZXMiLCJwaWN0dXJlcyIsIm5hbWVzIiwic3BlYWtlciIsInB1c2giLCJwaWN0dXJlIiwiZmlyc3ROYW1lIiwiY2hhckF0IiwibGFzdE5hbWUiLCJzcGVha2Vyc05hbWVzVG9TdHJpbmciLCJzZXBhcmF0b3IiLCJsZW5ndGgiLCJpIiwibmFtZSIsImVudHJpZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./utils/posts.utils.js\n");

/***/ }),

/***/ "(rsc)/./utils/string.utils.js":
/*!*******************************!*\
  !*** ./utils/string.utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getYouTubeVideoIdFromUrl: () => (/* binding */ getYouTubeVideoIdFromUrl),\n/* harmony export */   isLinkExternal: () => (/* binding */ isLinkExternal),\n/* harmony export */   noHyphen: () => (/* binding */ noHyphen),\n/* harmony export */   removeHtml: () => (/* binding */ removeHtml),\n/* harmony export */   removeLastBackSlash: () => (/* binding */ removeLastBackSlash),\n/* harmony export */   removeMarkdown: () => (/* binding */ removeMarkdown),\n/* harmony export */   reverseWords: () => (/* binding */ reverseWords),\n/* harmony export */   slugify: () => (/* binding */ slugify)\n/* harmony export */ });\nfunction slugify(str) {\n    str = str.replace(/^\\s+|\\s+$/g, \"\");\n    str = str.toLowerCase();\n    let from = \"\\xc1\\xc4\\xc2\\xc0\\xc3\\xc5Č\\xc7ĆĎ\\xc9Ě\\xcb\\xc8\\xcaẼĔȆ\\xcd\\xcc\\xce\\xcfŇ\\xd1\\xd3\\xd6\\xd2\\xd4\\xd5\\xd8ŘŔŠŤ\\xdaŮ\\xdc\\xd9\\xdb\\xddŸŽ\\xe1\\xe4\\xe2\\xe0\\xe3\\xe5č\\xe7ćď\\xe9ě\\xeb\\xe8\\xeaẽĕȇ\\xed\\xec\\xee\\xefň\\xf1\\xf3\\xf6\\xf2\\xf4\\xf5\\xf8\\xf0řŕšť\\xfaů\\xfc\\xf9\\xfb\\xfd\\xffž\\xfe\\xdeĐđ\\xdf\\xc6a\\xb7/_,:;\";\n    let to = \"AAAAAACCCDEEEEEEEEIIIINNOOOOOORRSTUUUUUYYZaaaaaacccdeeeeeeeeiiiinnooooooorrstuuuuuyyzbBDdBAa------\";\n    for(let i = 0, l = from.length; i < l; i++){\n        str = str.replace(new RegExp(from.charAt(i), \"g\"), to.charAt(i));\n    }\n    // Remove invalid chars\n    str = str.replace(/[^a-z0-9 -]/g, \"\")// Collapse whitespace and replace by -\n    .replace(/\\s+/g, \"-\")// Collapse dashes\n    .replace(/-+/g, \"-\");\n    return str;\n}\n/**\r\n * Retourne la chaine passée en paramètre sans markdown.\r\n * Seuls les noms des liens sont conservés, pas les url.\r\n */ function removeMarkdown(text) {\n    return text.replace(/(?:_|[*#])|\\[(.*?)\\]\\(.*?\\)/gm, \"$1\");\n}\n/**\r\n * Retourne la chaine passée en paramètre sans balise html\r\n * en gardant le contenu.\r\n */ function removeHtml(str) {\n    return str.replace(/`|<[^>]*(>|…)/gm, \"\");\n}\n/**\r\n * Retourne true si le lien passé redirige vers un site externe.\r\n * @param link {string}\r\n * @returns {boolean}\r\n */ function isLinkExternal(link) {\n    return link.includes(\".\");\n}\nconst getYouTubeVideoIdFromUrl = (url)=>{\n    url = url.split(/(vi\\/|v=|\\/v\\/|youtu\\.be\\/|\\/embed\\/)/);\n    return url[2] !== undefined ? url[2].split(/[^0-9a-z_\\-]/i)[0] : url[0];\n};\n/**\r\n  * Retourne la chaîne passée en paramètre sans le \"\\\" de fin\r\n  * Utiliser pour le body des articles\r\n  */ function removeLastBackSlash(str) {\n    return str.replace(/^\\\\/gm, \"\");\n}\nfunction noHyphen(text) {\n    return text?.replace(/-/g, \" \") || \"\";\n}\nfunction reverseWords(str) {\n    const arr = str.split(\" \");\n    return [\n        arr.pop(),\n        [\n            ...arr\n        ].join(\" \")\n    ].join(\" \");\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./utils/string.utils.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/graphql","vendor-chunks/@apollo","vendor-chunks/xml-js","vendor-chunks/feed","vendor-chunks/@wry","vendor-chunks/ts-invariant","vendor-chunks/zen-observable","vendor-chunks/symbol-observable","vendor-chunks/optimism","vendor-chunks/graphql-tag","vendor-chunks/zen-observable-ts","vendor-chunks/tslib","vendor-chunks/showdown","vendor-chunks/sax"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcoredata%2Froute&page=%2Fapi%2Fcoredata%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcoredata%2Froute.js&appDir=C%3A%5Crep%5CTPSG%5Ctpsg-next%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Crep%5CTPSG%5Ctpsg-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();