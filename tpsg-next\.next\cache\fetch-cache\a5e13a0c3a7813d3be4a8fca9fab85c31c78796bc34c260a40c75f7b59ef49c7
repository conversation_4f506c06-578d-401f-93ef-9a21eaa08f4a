{"kind": "FETCH", "data": {"headers": {"connection": "keep-alive", "content-length": "155964", "content-type": "application/json", "date": "Wed, 28 May 2025 09:53:59 GMT", "keep-alive": "timeout=5", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Origin", "x-frame-options": "SAMEORIGIN", "x-powered-by": "Strapi <strapi.io>", "x-response-time": "765ms"}, "body": "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", "status": 200, "url": "http://127.0.0.1:1337/graphql"}, "revalidate": 3600, "tags": []}