{"kind": "FETCH", "data": {"headers": {"connection": "keep-alive", "content-length": "128777", "content-type": "application/json", "date": "Wed, 28 May 2025 09:53:54 GMT", "keep-alive": "timeout=5", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Origin", "x-frame-options": "SAMEORIGIN", "x-powered-by": "Strapi <strapi.io>", "x-response-time": "528ms"}, "body": "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", "status": 200, "url": "http://127.0.0.1:1337/graphql"}, "revalidate": 3600, "tags": []}