[{"C:\\rep\\TPSG\\tpsg-next\\app\\api\\coredata\\route.js": "1", "C:\\rep\\TPSG\\tpsg-next\\app\\layout.js": "2", "C:\\rep\\TPSG\\tpsg-next\\pages\\api\\hello.js": "3", "C:\\rep\\TPSG\\tpsg-next\\pages\\article\\[article].js": "4", "C:\\rep\\TPSG\\tpsg-next\\pages\\blog\\[blog]\\filtres.js": "5", "C:\\rep\\TPSG\\tpsg-next\\pages\\blog\\[blog]\\index.js": "6", "C:\\rep\\TPSG\\tpsg-next\\pages\\categories\\index.js": "7", "C:\\rep\\TPSG\\tpsg-next\\pages\\categories\\ministere\\[ministry]\\index.js": "8", "C:\\rep\\TPSG\\tpsg-next\\pages\\categories\\ministere\\[ministry]\\ressources.js": "9", "C:\\rep\\TPSG\\tpsg-next\\pages\\categories\\vocation\\[vocation]\\index.js": "10", "C:\\rep\\TPSG\\tpsg-next\\pages\\categories\\vocation\\[vocation]\\ressources.js": "11", "C:\\rep\\TPSG\\tpsg-next\\pages\\categories\\[topic]\\index.js": "12", "C:\\rep\\TPSG\\tpsg-next\\pages\\categories\\[topic]\\ressources.js": "13", "C:\\rep\\TPSG\\tpsg-next\\pages\\formations\\index.js": "14", "C:\\rep\\TPSG\\tpsg-next\\pages\\formations\\[formation].js": "15", "C:\\rep\\TPSG\\tpsg-next\\pages\\index.js": "16", "C:\\rep\\TPSG\\tpsg-next\\pages\\parcours-emails\\index.js": "17", "C:\\rep\\TPSG\\tpsg-next\\pages\\parcours-emails\\[parcours].js": "18", "C:\\rep\\TPSG\\tpsg-next\\pages\\podcasts\\index.js": "19", "C:\\rep\\TPSG\\tpsg-next\\pages\\podcasts\\[podcast]\\index.js": "20", "C:\\rep\\TPSG\\tpsg-next\\pages\\podcasts\\[podcast]\\[episode].js": "21", "C:\\rep\\TPSG\\tpsg-next\\pages\\preview.js": "22", "C:\\rep\\TPSG\\tpsg-next\\pages\\recherche.js": "23", "C:\\rep\\TPSG\\tpsg-next\\pages\\robots.txt.js": "24", "C:\\rep\\TPSG\\tpsg-next\\pages\\sitemap.xml.js": "25", "C:\\rep\\TPSG\\tpsg-next\\pages\\webinaires\\index.js": "26", "C:\\rep\\TPSG\\tpsg-next\\pages\\webinaires\\[episode].js": "27", "C:\\rep\\TPSG\\tpsg-next\\pages\\[page].js": "28", "C:\\rep\\TPSG\\tpsg-next\\pages\\_app.js": "29", "C:\\rep\\TPSG\\tpsg-next\\pages\\_document.js": "30", "C:\\rep\\TPSG\\tpsg-next\\components\\blog\\BlogHeader.js": "31", "C:\\rep\\TPSG\\tpsg-next\\components\\blog\\menu.js": "32", "C:\\rep\\TPSG\\tpsg-next\\components\\categories\\CardMinistere.js": "33", "C:\\rep\\TPSG\\tpsg-next\\components\\categories\\CardVocation.js": "34", "C:\\rep\\TPSG\\tpsg-next\\components\\categories\\ChildrenList.js": "35", "C:\\rep\\TPSG\\tpsg-next\\components\\categories\\index.js": "36", "C:\\rep\\TPSG\\tpsg-next\\components\\categories\\MainList.js": "37", "C:\\rep\\TPSG\\tpsg-next\\components\\categories\\TopicHeader.js": "38", "C:\\rep\\TPSG\\tpsg-next\\components\\CookieBanner.js": "39", "C:\\rep\\TPSG\\tpsg-next\\components\\formation\\BannerFormation.js": "40", "C:\\rep\\TPSG\\tpsg-next\\components\\formation\\Medal.js": "41", "C:\\rep\\TPSG\\tpsg-next\\components\\home\\index.js": "42", "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionArticles.js": "43", "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionBloggers.js": "44", "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionDouble.js": "45", "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionMission.js": "46", "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionMostRead.js": "47", "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionPodcasts.js": "48", "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionQuote.js": "49", "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionShop.js": "50", "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionTopics.js": "51", "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Footer.js": "52", "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\HeaderDropDown.js": "53", "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\HeaderMenu\\Blogs.js": "54", "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\HeaderMenu\\index.js": "55", "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\HeaderMenu\\Vocations.js": "56", "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\index.js": "57", "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\Logo.js": "58", "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\MenuButtons.js": "59", "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\NavigationBar.js": "60", "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\index.js": "61", "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\PagesLayout\\ArticleLayout.js": "62", "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\PagesLayout\\index.js": "63", "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\PagesLayout\\PodcastLayout.js": "64", "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\PagesLayout\\WebinarLayout.js": "65", "C:\\rep\\TPSG\\tpsg-next\\components\\parcours-emails\\Stamp.js": "66", "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\logos\\index.js": "67", "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\logos\\Podcast1PVR.js": "68", "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\logos\\Podcast1PVRSwitch.js": "69", "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\logos\\PodcastChretienne.js": "70", "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\logos\\PodcastMemento.js": "71", "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\logos\\PodcastPMM.js": "72", "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\logos\\PodcastPredications.js": "73", "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\PodcastDescriptionData.js": "74", "C:\\rep\\TPSG\\tpsg-next\\components\\Popup\\index.js": "75", "C:\\rep\\TPSG\\tpsg-next\\components\\Preview\\PreviewButton.js": "76", "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\AuthorCard.js": "77", "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\big-input.js": "78", "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\filter-bar.js": "79", "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\filter-list.js": "80", "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\ListAuthor.js": "81", "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\ListTopic.js": "82", "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\ListTypes.js": "83", "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\search-paginate.js": "84", "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\search-tool.js": "85", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\animation\\AnimatedList.js": "86", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\animated-arrow.js": "87", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\animated-icon.js": "88", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\BlurPlay.js": "89", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\button.js": "90", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\Buttons\\BigCta.js": "91", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\index.js": "92", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\rounded-label.js": "93", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\section-background.js": "94", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\SocialMedia.js": "95", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\Spacer.js": "96", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Button.js": "97", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\AnimatedArrowButton.jsx": "98", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\AnimatedTextButton.jsx": "99", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\ButtonClose.js": "100", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\ButtonLink.jsx": "101", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\CircleCta.jsx": "102", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\index.js": "103", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\MediaButton.jsx": "104", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\SmallButton.jsx": "105", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\CornerStoneCard.js": "106", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\DefaultCard.js": "107", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\Elements\\Author.js": "108", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\Elements\\index.js": "109", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\Elements\\Speakers.js": "110", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\first-article-card.js": "111", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\GridCard.js": "112", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\HorizontalPostCard.js": "113", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\HorizontalReversePostCard.js": "114", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\index.js": "115", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\LargeCard.js": "116", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\LargeRelatedCard.js": "117", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\search-card.js": "118", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\slide-card.js": "119", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\square-vertical-card.js": "120", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\SquareVerticalFeatured.js": "121", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\VerticalCard.js": "122", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\CardPost.js": "123", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\categories\\CategoriesHeader.js": "124", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\categories\\SectionMinistries.js": "125", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\categories\\SectionVocations.js": "126", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\condimage.js": "127", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\CondLink.js": "128", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\ConvertkitForm\\CKForm.js": "129", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\ConvertkitForm\\DynamicForm.js": "130", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\CookieWall.jsx": "131", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\DuotoneFilter.js": "132", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Featured.js": "133", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\icons.js": "134", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\index.js": "135", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\inputs\\checkbox.js": "136", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\inputs\\index.js": "137", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\inputs\\toggle.js": "138", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\ListLink.js": "139", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\pagination\\ssr-paginate.js": "140", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\pagination\\sticky-pagination\\AnimatedNumber.js": "141", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\pagination\\sticky-pagination\\NavigationButtons.js": "142", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\pagination\\sticky-pagination\\PaginateSection.js": "143", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\pagination\\sticky-pagination\\PaginationNumber.js": "144", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\pagination\\sticky-pagination\\StickyPagination.js": "145", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\post\\author-box.js": "146", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\post\\md-body.js": "147", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Preview.js": "148", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\RefTagger.js": "149", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Related.js": "150", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\RenderMardown.js": "151", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\section\\GridCardSection.js": "152", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\section\\SectionHeader.js": "153", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\SimplePagination.js": "154", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Speakers.js": "155", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\strapi-modules\\index.js": "156", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\strapi-modules\\YoutubeEmbed.js": "157", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\SubHeader\\Items\\Authors.js": "158", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\SubHeader\\Items\\index.js": "159", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\SubHeader\\Items\\LinkButton.js": "160", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\SubHeader\\Items\\Social.js": "161", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\SubHeader\\Items\\Text.js": "162", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\SubHeader\\SubHeader.js": "163", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\topics-horizontal-list.js": "164", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\VideoPlayer.js": "165", "C:\\rep\\TPSG\\tpsg-next\\components\\svg\\chevron-down.js": "166", "C:\\rep\\TPSG\\tpsg-next\\components\\svg\\index.js": "167", "C:\\rep\\TPSG\\tpsg-next\\components\\svg\\long-arrow.js": "168", "C:\\rep\\TPSG\\tpsg-next\\components\\test\\card\\SliderCard.js": "169", "C:\\rep\\TPSG\\tpsg-next\\components\\test\\Carousel.js": "170", "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\RegisterBar.js": "171", "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\Ticket\\svg\\NotchSvgBlack.js": "172", "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\Ticket\\svg\\NotchSvgBlackMobile.js": "173", "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\Ticket\\svg\\NotchSvgWhite.js": "174", "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\Ticket\\svg\\NotchSvgWhiteMobile.js": "175", "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\Ticket\\Ticket.js": "176", "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\Ticket\\TicketInfo.js": "177", "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\WebEvent.js": "178", "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\WebSelector.js": "179", "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\WepisodeCard.js": "180"}, {"size": 1308, "mtime": 1747654278969, "results": "181", "hashOfConfig": "182"}, {"size": 138, "mtime": 1747654278984, "results": "183", "hashOfConfig": "182"}, {"size": 218, "mtime": 1747654279169, "results": "184", "hashOfConfig": "182"}, {"size": 2794, "mtime": 1745424367214, "results": "185", "hashOfConfig": "182"}, {"size": 5081, "mtime": 1745424367230, "results": "186", "hashOfConfig": "182"}, {"size": 12840, "mtime": 1747842013036, "results": "187", "hashOfConfig": "182"}, {"size": 4200, "mtime": 1745424367230, "results": "188", "hashOfConfig": "182"}, {"size": 10881, "mtime": 1748342601515, "results": "189", "hashOfConfig": "182"}, {"size": 4640, "mtime": 1745424367245, "results": "190", "hashOfConfig": "182"}, {"size": 11669, "mtime": 1748349484017, "results": "191", "hashOfConfig": "182"}, {"size": 4620, "mtime": 1745424367245, "results": "192", "hashOfConfig": "182"}, {"size": 8705, "mtime": 1747839039197, "results": "193", "hashOfConfig": "182"}, {"size": 4758, "mtime": 1745424367230, "results": "194", "hashOfConfig": "182"}, {"size": 3828, "mtime": 1745424367261, "results": "195", "hashOfConfig": "182"}, {"size": 1798, "mtime": 1747654279184, "results": "196", "hashOfConfig": "182"}, {"size": 3649, "mtime": 1747823333167, "results": "197", "hashOfConfig": "182"}, {"size": 3444, "mtime": 1747654279184, "results": "198", "hashOfConfig": "182"}, {"size": 6562, "mtime": 1747654279184, "results": "199", "hashOfConfig": "182"}, {"size": 7166, "mtime": 1745424367283, "results": "200", "hashOfConfig": "182"}, {"size": 12667, "mtime": 1747654279200, "results": "201", "hashOfConfig": "182"}, {"size": 5887, "mtime": 1747654279184, "results": "202", "hashOfConfig": "182"}, {"size": 3410, "mtime": 1745424367285, "results": "203", "hashOfConfig": "182"}, {"size": 8940, "mtime": 1747839031049, "results": "204", "hashOfConfig": "182"}, {"size": 527, "mtime": 1745424367288, "results": "205", "hashOfConfig": "182"}, {"size": 1343, "mtime": 1747651126990, "results": "206", "hashOfConfig": "182"}, {"size": 11677, "mtime": 1745424367294, "results": "207", "hashOfConfig": "182"}, {"size": 3133, "mtime": 1747653531073, "results": "208", "hashOfConfig": "182"}, {"size": 3727, "mtime": 1745424367214, "results": "209", "hashOfConfig": "182"}, {"size": 6253, "mtime": 1748357157442, "results": "210", "hashOfConfig": "182"}, {"size": 1470, "mtime": 1748357118000, "results": "211", "hashOfConfig": "182"}, {"size": 1532, "mtime": 1745424366807, "results": "212", "hashOfConfig": "182"}, {"size": 6247, "mtime": 1745424366808, "results": "213", "hashOfConfig": "182"}, {"size": 232, "mtime": 1745424366810, "results": "214", "hashOfConfig": "182"}, {"size": 227, "mtime": 1745424366811, "results": "215", "hashOfConfig": "182"}, {"size": 1477, "mtime": 1745424366815, "results": "216", "hashOfConfig": "182"}, {"size": 137, "mtime": 1745424366815, "results": "217", "hashOfConfig": "182"}, {"size": 13516, "mtime": 1745424366815, "results": "218", "hashOfConfig": "182"}, {"size": 2569, "mtime": 1745424366815, "results": "219", "hashOfConfig": "182"}, {"size": 6027, "mtime": 1745424366801, "results": "220", "hashOfConfig": "182"}, {"size": 4847, "mtime": 1745424366815, "results": "221", "hashOfConfig": "182"}, {"size": 19250, "mtime": 1745424366815, "results": "222", "hashOfConfig": "182"}, {"size": 702, "mtime": 1745424366831, "results": "223", "hashOfConfig": "182"}, {"size": 4391, "mtime": 1748425105736, "results": "224", "hashOfConfig": "182"}, {"size": 1752, "mtime": 1747654278984, "results": "225", "hashOfConfig": "182"}, {"size": 2564, "mtime": 1748425149615, "results": "226", "hashOfConfig": "182"}, {"size": 2421, "mtime": 1748428372364, "results": "227", "hashOfConfig": "182"}, {"size": 1573, "mtime": 1745424366831, "results": "228", "hashOfConfig": "182"}, {"size": 2768, "mtime": 1745424366831, "results": "229", "hashOfConfig": "182"}, {"size": 352, "mtime": 1748425012745, "results": "230", "hashOfConfig": "182"}, {"size": 327, "mtime": 1748425179673, "results": "231", "hashOfConfig": "182"}, {"size": 2345, "mtime": 1745424366831, "results": "232", "hashOfConfig": "182"}, {"size": 2289, "mtime": 1745424366831, "results": "233", "hashOfConfig": "182"}, {"size": 3157, "mtime": 1747654279000, "results": "234", "hashOfConfig": "182"}, {"size": 1845, "mtime": 1745424366847, "results": "235", "hashOfConfig": "182"}, {"size": 5290, "mtime": 1747654279000, "results": "236", "hashOfConfig": "182"}, {"size": 3533, "mtime": 1745424366847, "results": "237", "hashOfConfig": "182"}, {"size": 1136, "mtime": 1745424366862, "results": "238", "hashOfConfig": "182"}, {"size": 815, "mtime": 1745424366847, "results": "239", "hashOfConfig": "182"}, {"size": 3455, "mtime": 1745424366862, "results": "240", "hashOfConfig": "182"}, {"size": 3722, "mtime": 1747654279047, "results": "241", "hashOfConfig": "182"}, {"size": 408, "mtime": 1747654279071, "results": "242", "hashOfConfig": "182"}, {"size": 9345, "mtime": 1748431557885, "results": "243", "hashOfConfig": "182"}, {"size": 204, "mtime": 1745424366862, "results": "244", "hashOfConfig": "182"}, {"size": 6259, "mtime": 1745424366862, "results": "245", "hashOfConfig": "182"}, {"size": 5086, "mtime": 1747849317157, "results": "246", "hashOfConfig": "182"}, {"size": 31649, "mtime": 1747654279074, "results": "247", "hashOfConfig": "182"}, {"size": 350, "mtime": 1745424366914, "results": "248", "hashOfConfig": "182"}, {"size": 365, "mtime": 1747654279080, "results": "249", "hashOfConfig": "182"}, {"size": 365, "mtime": 1747654279082, "results": "250", "hashOfConfig": "182"}, {"size": 60173, "mtime": 1745424366894, "results": "251", "hashOfConfig": "182"}, {"size": 1124829, "mtime": 1745424366914, "results": "252", "hashOfConfig": "182"}, {"size": 2995, "mtime": 1745424366914, "results": "253", "hashOfConfig": "182"}, {"size": 502, "mtime": 1745424366914, "results": "254", "hashOfConfig": "182"}, {"size": 1932, "mtime": 1747654279078, "results": "255", "hashOfConfig": "182"}, {"size": 6296, "mtime": 1745424366803, "results": "256", "hashOfConfig": "182"}, {"size": 3395, "mtime": 1745424366805, "results": "257", "hashOfConfig": "182"}, {"size": 3561, "mtime": 1747654279086, "results": "258", "hashOfConfig": "182"}, {"size": 6405, "mtime": 1747654279087, "results": "259", "hashOfConfig": "182"}, {"size": 14032, "mtime": 1745424366929, "results": "260", "hashOfConfig": "182"}, {"size": 2587, "mtime": 1745424366929, "results": "261", "hashOfConfig": "182"}, {"size": 2052, "mtime": 1745424366929, "results": "262", "hashOfConfig": "182"}, {"size": 1858, "mtime": 1745424366929, "results": "263", "hashOfConfig": "182"}, {"size": 1037, "mtime": 1745424366929, "results": "264", "hashOfConfig": "182"}, {"size": 2064, "mtime": 1747654279091, "results": "265", "hashOfConfig": "182"}, {"size": 5531, "mtime": 1747654279094, "results": "266", "hashOfConfig": "182"}, {"size": 3941, "mtime": 1745424367046, "results": "267", "hashOfConfig": "182"}, {"size": 2243, "mtime": 1745424367062, "results": "268", "hashOfConfig": "182"}, {"size": 6873, "mtime": 1745424367062, "results": "269", "hashOfConfig": "182"}, {"size": 1348, "mtime": 1745424367046, "results": "270", "hashOfConfig": "182"}, {"size": 915, "mtime": 1745424367062, "results": "271", "hashOfConfig": "182"}, {"size": 2317, "mtime": 1745424367046, "results": "272", "hashOfConfig": "182"}, {"size": 233, "mtime": 1745424367062, "results": "273", "hashOfConfig": "182"}, {"size": 918, "mtime": 1745424367062, "results": "274", "hashOfConfig": "182"}, {"size": 322, "mtime": 1745424367062, "results": "275", "hashOfConfig": "182"}, {"size": 4832, "mtime": 1745424367062, "results": "276", "hashOfConfig": "182"}, {"size": 207, "mtime": 1745424367062, "results": "277", "hashOfConfig": "182"}, {"size": 440, "mtime": 1745424366945, "results": "278", "hashOfConfig": "182"}, {"size": 2360, "mtime": 1745424366945, "results": "279", "hashOfConfig": "182"}, {"size": 1804, "mtime": 1745424366945, "results": "280", "hashOfConfig": "182"}, {"size": 701, "mtime": 1745424366945, "results": "281", "hashOfConfig": "182"}, {"size": 828, "mtime": 1745424366945, "results": "282", "hashOfConfig": "182"}, {"size": 1385, "mtime": 1747654279100, "results": "283", "hashOfConfig": "182"}, {"size": 231, "mtime": 1745424366961, "results": "284", "hashOfConfig": "182"}, {"size": 13152, "mtime": 1745424366961, "results": "285", "hashOfConfig": "182"}, {"size": 598, "mtime": 1745424366961, "results": "286", "hashOfConfig": "182"}, {"size": 3272, "mtime": 1748347678982, "results": "287", "hashOfConfig": "182"}, {"size": 4021, "mtime": 1748425056726, "results": "288", "hashOfConfig": "182"}, {"size": 2545, "mtime": 1745424366961, "results": "289", "hashOfConfig": "182"}, {"size": 105, "mtime": 1745424366979, "results": "290", "hashOfConfig": "182"}, {"size": 1687, "mtime": 1745424366978, "results": "291", "hashOfConfig": "182"}, {"size": 2422, "mtime": 1745424367001, "results": "292", "hashOfConfig": "182"}, {"size": 2116, "mtime": 1747654279106, "results": "293", "hashOfConfig": "182"}, {"size": 3702, "mtime": 1747654279110, "results": "294", "hashOfConfig": "182"}, {"size": 3815, "mtime": 1748357737110, "results": "295", "hashOfConfig": "182"}, {"size": 427, "mtime": 1745424367003, "results": "296", "hashOfConfig": "182"}, {"size": 2994, "mtime": 1747654279110, "results": "297", "hashOfConfig": "182"}, {"size": 3879, "mtime": 1747654279110, "results": "298", "hashOfConfig": "182"}, {"size": 3722, "mtime": 1745424367004, "results": "299", "hashOfConfig": "182"}, {"size": 2281, "mtime": 1745424367006, "results": "300", "hashOfConfig": "182"}, {"size": 2412, "mtime": 1745424367007, "results": "301", "hashOfConfig": "182"}, {"size": 1382, "mtime": 1747839308469, "results": "302", "hashOfConfig": "182"}, {"size": 3270, "mtime": 1745424367000, "results": "303", "hashOfConfig": "182"}, {"size": 531, "mtime": 1745424367009, "results": "304", "hashOfConfig": "182"}, {"size": 2496, "mtime": 1745424367077, "results": "305", "hashOfConfig": "182"}, {"size": 1004, "mtime": 1745424367079, "results": "306", "hashOfConfig": "182"}, {"size": 1288, "mtime": 1745424367083, "results": "307", "hashOfConfig": "182"}, {"size": 922, "mtime": 1746796780679, "results": "308", "hashOfConfig": "182"}, {"size": 295, "mtime": 1747839376463, "results": "309", "hashOfConfig": "182"}, {"size": 1474, "mtime": 1745424367015, "results": "310", "hashOfConfig": "182"}, {"size": 144, "mtime": 1745424367015, "results": "311", "hashOfConfig": "182"}, {"size": 1881, "mtime": 1745424367015, "results": "312", "hashOfConfig": "182"}, {"size": 987, "mtime": 1745424367015, "results": "313", "hashOfConfig": "182"}, {"size": 6134, "mtime": 1748424964309, "results": "314", "hashOfConfig": "182"}, {"size": 271, "mtime": 1745424367083, "results": "315", "hashOfConfig": "182"}, {"size": 276, "mtime": 1745424367083, "results": "316", "hashOfConfig": "182"}, {"size": 277, "mtime": 1745424367083, "results": "317", "hashOfConfig": "182"}, {"size": 55, "mtime": 1745424367083, "results": "318", "hashOfConfig": "182"}, {"size": 1389, "mtime": 1745424367083, "results": "319", "hashOfConfig": "182"}, {"size": 1157, "mtime": 1745424367015, "results": "320", "hashOfConfig": "182"}, {"size": 1374, "mtime": 1745424367083, "results": "321", "hashOfConfig": "182"}, {"size": 1766, "mtime": 1745424367101, "results": "322", "hashOfConfig": "182"}, {"size": 4008, "mtime": 1745424367103, "results": "323", "hashOfConfig": "182"}, {"size": 1504, "mtime": 1745424367104, "results": "324", "hashOfConfig": "182"}, {"size": 963, "mtime": 1745424367106, "results": "325", "hashOfConfig": "182"}, {"size": 2911, "mtime": 1745424367107, "results": "326", "hashOfConfig": "182"}, {"size": 2536, "mtime": 1748431505468, "results": "327", "hashOfConfig": "182"}, {"size": 7072, "mtime": 1748431455265, "results": "328", "hashOfConfig": "182"}, {"size": 2134, "mtime": 1745424367015, "results": "329", "hashOfConfig": "182"}, {"size": 1396, "mtime": 1745424367015, "results": "330", "hashOfConfig": "182"}, {"size": 2834, "mtime": 1745424367015, "results": "331", "hashOfConfig": "182"}, {"size": 7736, "mtime": 1748431419397, "results": "332", "hashOfConfig": "182"}, {"size": 1342, "mtime": 1745424367115, "results": "333", "hashOfConfig": "182"}, {"size": 1022, "mtime": 1745424367115, "results": "334", "hashOfConfig": "182"}, {"size": 3000, "mtime": 1745424367030, "results": "335", "hashOfConfig": "182"}, {"size": 1332, "mtime": 1745424367030, "results": "336", "hashOfConfig": "182"}, {"size": 73, "mtime": 1745424367115, "results": "337", "hashOfConfig": "182"}, {"size": 792, "mtime": 1745424367115, "results": "338", "hashOfConfig": "182"}, {"size": 2754, "mtime": 1747654279129, "results": "339", "hashOfConfig": "182"}, {"size": 191, "mtime": 1745424367046, "results": "340", "hashOfConfig": "182"}, {"size": 649, "mtime": 1747849217624, "results": "341", "hashOfConfig": "182"}, {"size": 367, "mtime": 1745424367030, "results": "342", "hashOfConfig": "182"}, {"size": 857, "mtime": 1745424367046, "results": "343", "hashOfConfig": "182"}, {"size": 1187, "mtime": 1747849273275, "results": "344", "hashOfConfig": "182"}, {"size": 2310, "mtime": 1745424367115, "results": "345", "hashOfConfig": "182"}, {"size": 1786, "mtime": 1745424367046, "results": "346", "hashOfConfig": "182"}, {"size": 304, "mtime": 1745424367115, "results": "347", "hashOfConfig": "182"}, {"size": 124, "mtime": 1745424367131, "results": "348", "hashOfConfig": "182"}, {"size": 651, "mtime": 1745424367131, "results": "349", "hashOfConfig": "182"}, {"size": 5484, "mtime": 1745424367131, "results": "350", "hashOfConfig": "182"}, {"size": 3969, "mtime": 1745424367131, "results": "351", "hashOfConfig": "182"}, {"size": 935, "mtime": 1747849289299, "results": "352", "hashOfConfig": "182"}, {"size": 90896, "mtime": 1745424367147, "results": "353", "hashOfConfig": "182"}, {"size": 90928, "mtime": 1745424367147, "results": "354", "hashOfConfig": "182"}, {"size": 846, "mtime": 1745424367147, "results": "355", "hashOfConfig": "182"}, {"size": 820, "mtime": 1745424367162, "results": "356", "hashOfConfig": "182"}, {"size": 10813, "mtime": 1747849342520, "results": "357", "hashOfConfig": "182"}, {"size": 947, "mtime": 1745424367147, "results": "358", "hashOfConfig": "182"}, {"size": 1480, "mtime": 1747654279137, "results": "359", "hashOfConfig": "182"}, {"size": 4341, "mtime": 1745424367162, "results": "360", "hashOfConfig": "182"}, {"size": 1231, "mtime": 1747654279137, "results": "361", "hashOfConfig": "182"}, {"filePath": "362", "messages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "u5mc62", {"filePath": "364", "messages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "420"}, {"filePath": "421", "messages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "483"}, {"filePath": "484", "messages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "500"}, {"filePath": "501", "messages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "500"}, {"filePath": "503", "messages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "521"}, {"filePath": "522", "messages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "536"}, {"filePath": "537", "messages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "571"}, {"filePath": "572", "messages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "724"}, {"filePath": "725", "messages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "729"}, "C:\\rep\\TPSG\\tpsg-next\\app\\api\\coredata\\route.js", [], "C:\\rep\\TPSG\\tpsg-next\\app\\layout.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\api\\hello.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\article\\[article].js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\blog\\[blog]\\filtres.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\blog\\[blog]\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\categories\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\categories\\ministere\\[ministry]\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\categories\\ministere\\[ministry]\\ressources.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\categories\\vocation\\[vocation]\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\categories\\vocation\\[vocation]\\ressources.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\categories\\[topic]\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\categories\\[topic]\\ressources.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\formations\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\formations\\[formation].js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\parcours-emails\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\parcours-emails\\[parcours].js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\podcasts\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\podcasts\\[podcast]\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\podcasts\\[podcast]\\[episode].js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\preview.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\recherche.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\robots.txt.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\sitemap.xml.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\webinaires\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\webinaires\\[episode].js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\[page].js", [], "C:\\rep\\TPSG\\tpsg-next\\pages\\_app.js", ["730"], "import Layout from \"/components/layout\"\r\nimport \"../styles/globals.css\"\r\nimport Head from \"next/head\";\r\nimport Script from \"next/script\";\r\nimport { CookiesProvider, useCookies } from \"react-cookie\";\r\nimport { useRouter } from \"next/router\";\r\n\r\nfunction MyApp({ Component, pageProps }) {\r\n  const getParams = (query, paramsToInclude) => {\r\n    let params = paramsToInclude.reduce((params, param) => {\r\n      // don\"t include empty params or page=1\r\n      if (query[param] && query[param] !== \"\" && (param === \"page\" && Number(query.page) !== 1)) {\r\n        params.push(`${param}=${query[param]}`)\r\n      }\r\n      return params\r\n    }, [])\r\n    return params.length ? `?${params.join(\"&\")}` : \"\"\r\n  }\r\n\r\n  const createCanonicalUrl = () => {\r\n    const { pathname, query } = useRouter()\r\n\r\n    let url = \"\"\r\n    switch (pathname) {\r\n    // article\r\n    case \"/article/[article]\":\r\n      url = `/article/${query.article}`\r\n      break\r\n\r\n    // blog\r\n    case \"/blog/[blog]\":\r\n      url = `/blog/${query.blog}`\r\n      break\r\n\r\n    case \"/blog/[blog]/filtres\":\r\n      url = `/blog/${query.blog}/filtres${getParams(query, [\"topic\", \"type\", \"page\"])}`\r\n      break\r\n\r\n    // categories\r\n    case \"/categories\":\r\n      url = \"/categories\"\r\n      break\r\n\r\n    case \"/categories/[topic]\":\r\n      url = `/categories/${query.topic}`\r\n      break\r\n\r\n    case \"/categories/[topic]/ressources\":\r\n      url = `/categories/${query.topic}/ressources${getParams(query, [\"page\"])}`\r\n      break\r\n\r\n    case \"/categories/ministere/[ministry]\":\r\n      url = `/categories/ministere/${query.ministry}`\r\n      break\r\n\r\n    case \"/categories/ministere/[ministry]/ressources\":\r\n      url = `/categories/ministere/${query.ministry}/ressources${getParams(query, [\"page\"])}`\r\n      break\r\n\r\n    case \"/categories/vocation/[vocation]\":\r\n      url = `/categories/vocation/${query.vocation}`\r\n      break\r\n\r\n    case \"/categories/vocation/[vocation]/ressources\":\r\n      url = `/categories/vocation/${query.vocation}/ressources${getParams(query, [\"page\"])}`\r\n      break\r\n\r\n    // formation\r\n    case \"/formations\":\r\n      url = \"/formations\"\r\n      break\r\n\r\n    case \"/formations/[formation]\":\r\n      url = `/formations/${query.formation}`\r\n      break\r\n\r\n    // parcours email\r\n    case \"/parcours-emails\":\r\n      url = \"/parcours-emails\"\r\n      break\r\n\r\n    case \"/parcours-emails/[parcours]\":\r\n      url = `/parcours-emails/${query.parcours}`\r\n      break\r\n\r\n    // podcasts\r\n    case \"/podcasts\":\r\n      url = \"/podcasts\"\r\n      break\r\n\r\n    case \"/podcasts/[podcast]\":\r\n      url = `/podcasts/${query.podcast}${getParams(query, [\"page\"])}`\r\n      break\r\n\r\n    case \"/podcasts/[podcast]/[episode]\":\r\n      url = `/podcasts/${query.podcast}/${query.episode}`\r\n      break\r\n\r\n    // webinaires\r\n    case \"/webinaires\":\r\n      url = `/webinaires${getParams(query, [\"page\"])}`\r\n      break\r\n\r\n    case \"/webinaires/[episode]\":\r\n      url = `/webinaires/${query.episode}${getParams(query, [\"page\"])}`\r\n      break\r\n\r\n    case \"/[page]\":\r\n      url = `/${query.page}`\r\n      break\r\n\r\n    // default\r\n    default:\r\n      url = \"\"\r\n    }\r\n\r\n    return new URL(url, \"https://toutpoursagloire.com\").href // or use process.env.NEXT_PUBLIC_URL?\r\n  }\r\n\r\n  return (\r\n    <CookiesProvider>\r\n      <Layout>\r\n        <Head>\r\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0\" />\r\n          <link rel=\"canonical\" href={createCanonicalUrl()} />\r\n          <link rel=\"icon\" href=\"/favicon.png\" />\r\n        </Head>\r\n        <Component {...pageProps} />\r\n        {CookieScripts()}\r\n      </Layout>\r\n    </CookiesProvider>\r\n  )\r\n}\r\n\r\nfunction CookieScripts() {\r\n\r\n  let gaTagId = process.env.NEXT_PUBLIC_GATAG_ID;\r\n\r\n  const [cookie, setCookie] = useCookies([\"preferences\"]);\r\n\r\n  return (\r\n    <>\r\n      {cookie[\"preferences\"]?.analytics &&\r\n        <>\r\n          <Script async src={`https://www.googletagmanager.com/gtag/js?id=${gaTagId}`} id={\"ga-url-script\"}>\r\n          </Script >\r\n          <Script id={\"ga-analytics-script\"}>\r\n            {`window.dataLayer = window.dataLayer || [];\r\n              function gtag(){ dataLayer.push(arguments); }\r\n              gtag(\"js\", new Date());\r\n              gtag(\"config\", \"${gaTagId}\");\r\n            `}\r\n          </Script>\r\n        </>\r\n      }\r\n      {\r\n        cookie[\"preferences\"]?.medias &&\r\n        <>\r\n          <Script id={\"marker-io-config\"}>\r\n            {\r\n              `window.markerConfig = {\r\n                project: \"647f03e6a572cb7307800759\",\r\n                source: \"snippet\" };`\r\n            }\r\n          </Script>\r\n          {/*<Script id={\"marker-io-function\"}>*/}\r\n          {/*  {\"!function(e,r,a){if(!e.__Marker){e.__Marker={};var t=[],n={__cs:t};[\\\"show\\\",\\\"hide\\\",\\\"isVisible\\\",\\\"capture\\\",\\\"cancelCapture\\\",\\\"unload\\\",\\\"reload\\\",\\\"isExtensionInstalled\\\",\\\"setReporter\\\",\\\"setCustomData\\\",\\\"on\\\",\\\"off\\\"].forEach(function(e){n[e]=function(){var r=Array.prototype.slice.call(arguments);r.unshift(e),t.push(r)}}),e.Marker=n;var s=r.createElement(\\\"script\\\");s.async=1,s.src=\\\"https://edge.marker.io/latest/shim.js\\\";var i=r.getElementsByTagName(\\\"script\\\")[0];i.parentNode.insertBefore(s,i)}}(window,document);\"}*/}\r\n          {/*</Script>*/}\r\n        </>\r\n      }\r\n    </>\r\n  )\r\n}\r\n\r\nfunction GoogleAnalytics() {\r\n\r\n  let gaTagId = process.env.NEXT_PUBLIC_GATAG_ID;\r\n\r\n  const [cookie, setCookie] = useCookies([\"preferences\"]);\r\n\r\n  if (!cookie[\"preferences\"]?.analytics) {\r\n    return;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Script async src={`https://www.googletagmanager.com/gtag/js?id=${gaTagId}`} id={\"ga-url-script\"}>\r\n      </Script>\r\n      <Script id={\"ga-analytics-script\"}>\r\n        {`window.dataLayer = window.dataLayer || [];\r\n        function gtag(){ dataLayer.push(arguments); }\r\n        gtag(\"js\", new Date());\r\n        gtag(\"config\", \"${gaTagId}\");\r\n         `}\r\n      </Script>\r\n    </>\r\n  )\r\n}\r\n\r\n\r\nexport default MyApp\r\n\r\n// <script async src=\"https://www.googletagmanager.com/gtag/js?id=${gaTagId}%22%3E\"/>\r\n//         <script>\r\n//           window.dataLayer = window.dataLayer || [];\r\n//           function gtag(){dataLayer.push(arguments);}\r\n//           gtag(\"js\", new Date());\r\n//           gtag(\"config\", \"${gaTagId}\");\r\n//         </script>\r\n", "C:\\rep\\TPSG\\tpsg-next\\pages\\_document.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\blog\\BlogHeader.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\blog\\menu.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\categories\\CardMinistere.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\categories\\CardVocation.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\categories\\ChildrenList.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\categories\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\categories\\MainList.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\categories\\TopicHeader.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\CookieBanner.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\formation\\BannerFormation.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\formation\\Medal.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\home\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionArticles.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionBloggers.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionDouble.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionMission.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionMostRead.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionPodcasts.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionQuote.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionShop.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\home\\SectionTopics.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Footer.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\HeaderDropDown.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\HeaderMenu\\Blogs.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\HeaderMenu\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\HeaderMenu\\Vocations.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\Logo.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\MenuButtons.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\Header\\NavigationBar.js", ["731"], "import styled from \"styled-components\";\r\nimport Logo from \"./Logo\";\r\nimport { useContext, useEffect, useRef } from \"react\";\r\nimport { HeaderContext } from \"context/HeaderContext\";\r\nimport MenuButtons from \"./MenuButtons\";\r\nimport { device } from \"styles/device\";\r\nimport Link from \"next/link\";\r\n\r\nexport default function NavigationBar({ invert }) {\r\n\r\n  const ddRef = useRef(null);\r\n\r\n  const { headerState, onDropDownButtonClick, onDropDownClickOutside } = useContext(HeaderContext);\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (ddRef.current && !ddRef.current.contains(event.target)) {\r\n        if(headerState.dropDownOpen) {\r\n          onDropDownClickOutside();\r\n          event.stopPropagation();\r\n        }\r\n      }\r\n    };\r\n\r\n    document &&\r\n    document.addEventListener(\"click\", handleClickOutside, true);\r\n\r\n    return () => {\r\n      document &&\r\n      document.removeEventListener(\"click\", handleClickOutside, true);\r\n    };\r\n  }, [headerState]);\r\n\r\n  return (\r\n    <Wrapper menuOpen={headerState.showMenu} invert={invert}>\r\n      <hr className={\"animated-buttons-line\"}/>\r\n      <Logo white={invert}/>\r\n      <RightNav invert={invert} ref={ddRef}>\r\n        <ul>\r\n          <li onClick={() => onDropDownButtonClick(\"blogs\")}>Blogs</li>\r\n          <li onClick={() => onDropDownClickOutside() }><Link href={\"/categories\"}>Thèmes</Link></li>\r\n          <li onClick={() => onDropDownClickOutside() }><Link href={\"/formations\"}>Formations</Link></li>\r\n          <li onClick={() => onDropDownButtonClick(\"podcasts\")}>Podcasts</li>\r\n          <li onClick={() => onDropDownClickOutside() }><Link href={\"/webinaires\"}>Webinaires</Link></li>\r\n          <li onClick={() => onDropDownClickOutside() } className={\"highlight-top-menu\"}><Link href={\"/soutenir\"}>Soutenir</Link></li>\r\n        </ul>\r\n        <div className={\"nav-v-separator\"}/>\r\n        <MenuButtons invert={invert}/>\r\n      </RightNav>\r\n\r\n      <div className={\"animated-background\"}/>\r\n\r\n    </Wrapper>\r\n  )\r\n}\r\n\r\n\r\nconst Wrapper = styled.div`\r\n  display: flex;\r\n  width: 100%;\r\n  height: 80px;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  border-bottom: 1px solid rgba(0,0,0,0.20);\r\n\r\n  .animated-buttons-line {\r\n    position: absolute;\r\n    right: 0;\r\n    top: 70px;\r\n    z-index: 2100;\r\n    border: ${p => p.menuOpen ? \"1px solid #1C2E33\" : \"1px solid transparent\"};\r\n    width: 100%;\r\n    transition: all 600ms ease-in-out;\r\n\r\n    @media ${ device.desktop } {\r\n      right: 15px;\r\n      width: ${p => p.menuOpen ? \"calc(var(--border-space) + 98px)\" : \"0\"};\r\n    }\r\n  }\r\n  \r\n  .animated-background {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    height: 100%;\r\n    width: 100%;\r\n    background-color: var(--blue-dark);\r\n    transform: ${p => p.menuOpen ? \"translateY(0)\" : \"translateY(-100%)\"};\r\n    transition: transform 450ms ease-in-out;\r\n    z-index: 2100;\r\n\r\n    @media ${ device.desktop } {\r\n      display: none;\r\n    }\r\n  }\r\n  \r\n\r\n`;\r\n\r\nconst RightNav = styled.div`\r\n\r\n  position: relative;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  font-family: \"Switzer\", \"Helvetica Neue\", Helvetica, sans-serif;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n\r\n  ul li {\r\n    list-style: none;\r\n    display: none;\r\n    @media ${ device.desktop } {\r\n      display: inline;\r\n    }\r\n  }\r\n  li {\r\n    margin-left: 32px;\r\n    cursor: pointer;\r\n    color: ${p => p.invert ? \"var(--soft-white)\" : \"black\"};\r\n  }\r\n  \r\n  .highlight-top-menu {\r\n    color: ${p => p.invert ? \"var(--soft-white)\" : \"var(--brand-color)\"};\r\n  }\r\n  \r\n  .nav-v-separator {\r\n    height: 100%;\r\n    margin: 0 0 0 32px;\r\n    width: 0;\r\n    border-left: 1px solid rgba(0,0,0,0.2);\r\n  }\r\n  \r\n`;\r\n", "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\PagesLayout\\ArticleLayout.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\PagesLayout\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\PagesLayout\\PodcastLayout.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\layout\\PagesLayout\\WebinarLayout.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\parcours-emails\\Stamp.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\logos\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\logos\\Podcast1PVR.js", ["732"], "import Image from \"next/image\";\r\nimport styled from \"styled-components\";\r\n\r\nconst Podcast1PVR = () => {\r\n  return(\r\n    <Wrapper>\r\n      <Image src={\"/images/1pvr-logo-red.png\"} layout={\"fill\"} objectFit={\"contain\"}/>\r\n    </Wrapper>\r\n  )\r\n}\r\n\r\nconst Wrapper = styled.div`\r\n  position: relative;\r\n  width: 126px;\r\n  height: 126px;\r\n`;\r\n\r\nexport default Podcast1PVR;", "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\logos\\Podcast1PVRSwitch.js", ["733"], "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\logos\\PodcastChretienne.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\logos\\PodcastMemento.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\logos\\PodcastPMM.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\logos\\PodcastPredications.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\podcast\\PodcastDescriptionData.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\Popup\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\Preview\\PreviewButton.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\AuthorCard.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\big-input.js", ["734"], "import styled from \"styled-components\";\r\nimport { useEffect, useState } from \"react\";\r\nimport getMatchingFilter from \"services/matchingFilter\";\r\nimport { device } from \"styles/device\"\r\n\r\nconst BigInput = ({ listState, changeFilter, changeQueryTerms, autoFocus = false }) => {\r\n\r\n  const [matchingFilter, setMatchingFilter] = useState(\"\");\r\n  const [inputValue, setInputValue] = useState(listState.queryTerms);\r\n  const [rulerString, setRulerString] = useState(\"\");\r\n  const [rulerWidth, setRulerWidth] = useState(0);\r\n  \r\n  const handleTextChange = (text) => {\r\n    setInputValue(text);\r\n    if (text.length >= 3 && !listState.activeFilter) {\r\n      let mf = getMatchingFilter(text);\r\n      if (mf.value) {\r\n        mf.displayValue = text + mf.value.slice(text.length)\r\n      }\r\n      setMatchingFilter(mf);\r\n    } else {\r\n      setMatchingFilter(null);\r\n    }\r\n  }\r\n\r\n  const handleKeyDown = (e) => {\r\n    // Tab is pressed\r\n    if (e.keyCode === 9) {\r\n      e.preventDefault();\r\n      if (matchingFilter) {\r\n        addFilter();\r\n      }\r\n    }\r\n    // Backspace is pressed\r\n    if (e.keyCode === 8 && inputValue.length === 0) {\r\n      changeFilter(null, null);\r\n      changeQueryTerms(\"\");\r\n    }\r\n    if (e.key === \"Enter\") {\r\n      changeQueryTerms(inputValue);\r\n    }\r\n  }\r\n\r\n\r\n  const addFilter = () => {\r\n    setInputValue(\"\") // Reset du text de l'input\r\n    changeQueryTerms(\"\");\r\n    changeFilter(matchingFilter.value, matchingFilter.type, true)\r\n    setMatchingFilter(null) // Reset de l'autocomplete\r\n  }\r\n\r\n  const updateCaret = () => {\r\n    let input = document.getElementById(\"input\");\r\n    setRulerString(\r\n      inputValue\r\n        .replaceAll(\" \", \"!\")\r\n        .slice(0, input.selectionStart));\r\n  }\r\n\r\n  useEffect(() => {\r\n    updateCaret()\r\n  }, [inputValue])\r\n\r\n  useEffect(() => {\r\n    let ruler = document.getElementById(\"ruler\");\r\n    if (ruler) {\r\n      let width = ruler.offsetWidth;\r\n      setRulerWidth(width);\r\n    }\r\n  }, [rulerString])\r\n\r\n  return (\r\n    <Wrapper>\r\n      {matchingFilter?.displayValue &&\r\n        <div className={\"filter-label\"} onClick={() => addFilter()}>AJOUTER: CLICK ou TAB</div>\r\n      }\r\n      <p id={\"ruler\"}>{rulerString} <Caret width={rulerWidth}/></p>\r\n      <p>{matchingFilter?.displayValue || \"\"}</p>\r\n      <input\r\n        id={\"input\"}\r\n        autoFocus={autoFocus}\r\n        onKeyUp={(e) => updateCaret()}\r\n        onKeyDown={(e) => handleKeyDown(e)}\r\n        onClick={() => updateCaret()}\r\n        onChange={(e) => handleTextChange(e.target.value)}\r\n        placeholder={\"Rechercher\"}\r\n        value={inputValue}/>\r\n      <SearchSVG/>\r\n    </Wrapper>\r\n  )\r\n}\r\n\r\nexport default BigInput;\r\n\r\n\r\nconst Caret = styled.span`\r\n  position: absolute;\r\n  top: 8px;\r\n  left: 0;\r\n  display: none;\r\n  width: ${props => props.width}px;\r\n  height: 80%;\r\n  border-right-style: solid;\r\n  border-right-color: #FF856A;\r\n  border-right-width: ${props => props.width === 0 ? \"3px\" : \"36px\"};\r\n  transition: width 300ms cubic-bezier(.55, .77, .17, .97);\r\n\r\n  @media ${device.tablet} {\r\n    //display: inline-block;\r\n  }\r\n`;\r\n\r\nconst Wrapper = styled.div`\r\n  position: relative;\r\n  margin-top: 16px;\r\n  width: 100%;\r\n  height: 48px;\r\n  background-color: transparent;\r\n\r\n  @media ${device.tablet} {\r\n    height: 92px;\r\n  }\r\n\r\n  #ruler {\r\n    position: absolute;\r\n    font-family: Stelvio, Arial, sans-serif;\r\n    font-weight: 500;\r\n    font-size: 72px;\r\n    height: 72px;\r\n    color: transparent;\r\n    padding-top: 8px;\r\n  }\r\n\r\n  input {\r\n    position: absolute;\r\n    top: 0;\r\n    box-sizing: border-box;\r\n    @media ${device.tablet} {\r\n      //caret-color: transparent;\r\n      font-size: 72px;\r\n      height: auto;\r\n    }\r\n    padding-top: 0;\r\n    margin-top: 0;\r\n    padding-left: 0;\r\n    width: 100%;\r\n    font-family: Stelvio, Arial, sans-serif;\r\n    font-weight: 500;\r\n    font-size: 32px;\r\n    //border: 1px solid green;\r\n    border: none;\r\n    caret-color: #F45D3C;\r\n    background-color: transparent;\r\n    z-index: 10;\r\n\r\n    &:focus {\r\n      outline: none;\r\n      background-color: transparent;\r\n    }\r\n\r\n    &::placeholder {\r\n      color: rgba(244, 93, 60, 0.25);\r\n    }\r\n  }\r\n\r\n  // Permet de cacher le bas du caret \r\n  // (trop long avec la font Stelvio)\r\n  &:after {\r\n    content: '';\r\n    display: inline-block;\r\n    box-sizing: border-box;\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    height: 12px;\r\n    width: 100%;\r\n    background-color: var(--soft-white);\r\n    z-index: 20;\r\n\r\n    @media ${device.tablet} {\r\n      height: 20px;\r\n    }\r\n  }\r\n\r\n  p {\r\n    position: absolute;\r\n    margin-top: 0;\r\n    margin-bottom: 0;\r\n    height: 48px;\r\n    width: 100%; // à suprimer pour faire fonctionner le caret\r\n    top: 0;\r\n    left: 0;\r\n    font-family: Stelvio, Arial, sans-serif;\r\n    font-weight: 500;\r\n    font-size: 32px;\r\n    color: rgba(244, 93, 60, 0.5);\r\n    z-index: 0;\r\n\r\n    @media ${device.tablet} {\r\n      font-size: 72px;\r\n    }\r\n  }\r\n\r\n  .search-btn {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 4px;\r\n    z-index: 100;\r\n\r\n    svg {\r\n      //background-color: rgba(138, 43, 226, 0.76);\r\n      height: 24px;\r\n      width: 24px;\r\n    }\r\n\r\n    @media ${device.tablet} {\r\n      top: 12px;\r\n      right: 10px;\r\n      svg {\r\n        height: 45px;\r\n        width: 45px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .filter-label {\r\n    position: absolute;\r\n    left: 0;\r\n    padding: 4px 8px 4px 8px;\r\n    color: #f4f4f4;\r\n    background-color: black;\r\n    top: -36px;\r\n    font-size: 0.65rem;\r\n    font-weight: 400;\r\n    font-family: Arial, sans-serif;\r\n\r\n    &:before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 100%;\r\n      left: 0;\r\n      transform: rotate(90deg);\r\n      border-bottom: 12px solid transparent;\r\n      border-left: 12px solid #121212;\r\n    }\r\n\r\n    @media ${device.tablet} {\r\n      font-size: 0.75rem;\r\n      top: -46px;\r\n      padding: 5px 10px 5px 10px;\r\n      &:before {\r\n        border-bottom: 18px solid transparent;\r\n        border-left: 18px solid #121212;\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nconst SearchSVG = () => {\r\n  return (\r\n    <div className=\"search-btn\">\r\n      <svg width=\"50\" height=\"50\" viewBox=\"0 0 50 49\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n        <path d=\"M35.0234 36.1094L47.8259 48.912L49.2401 47.4977L36.4376 34.6952L35.0234 36.1094Z\" fill=\"black\"/>\r\n        <circle cx=\"21.6367\" cy=\"21.3087\" r=\"20.1035\" stroke=\"black\" strokeWidth=\"2\"/>\r\n      </svg>\r\n    </div>\r\n  )\r\n}\r\n", "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\filter-bar.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\filter-list.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\ListAuthor.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\ListTopic.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\ListTypes.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\search-paginate.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\recherche\\search-tool.js", ["735"], "import { Fragment, useEffect, useState } from \"react\";\r\nimport styled from \"styled-components\";\r\nimport BigInput from \"components/recherche/big-input\";\r\nimport FilterBar from \"components/recherche/filter-bar\";\r\nimport FilterList from \"components/recherche/filter-list\";\r\nimport { useCoreData } from \"context/CoreDataContext\";\r\n\r\n\r\nfunction getInitialState(initQuery){\r\n  return {\r\n    activeList: initQuery?.filter?.type ? initQuery.filter.type : \"type\", // liste en cours d'affichage\r\n    activeFilter: initQuery?.filter?.value, // filtre choisit par l'utilisateur\r\n    filterType: initQuery?.filter?.type, // type du filtre sélectionné\r\n    queryTerms: initQuery?.terms ? initQuery.terms : \"\", // termes contenus dans l'input\r\n    listOpen: false, // état d'ouverture du panneau contenant les listes\r\n    filterOpen: !!initQuery?.filter?.type, // état d'ouverture de la barre contenant le filtre\r\n    listVisible: false // état de visibilité de la liste à afficher\r\n  }\r\n}\r\n\r\nconst SearchTool = ({ setQuery, initQuery, autoFocus = false }) => {\r\n  let coreData = useCoreData()\r\n\r\n  coreData.types = [\r\n    { name: \"Podcast\", displayName: \"Podcasts\" },\r\n    { name: \"Formation\", displayName: \"Formations\" },\r\n    { name: \"Article\", displayName: \"Articles\" },\r\n    { name: \"Webinaire\", displayName: \"Webinaires\" },\r\n    { name: \"Parcours\", displayName: \"Parcours\" },\r\n  ]\r\n\r\n  const [state, setState] = useState(getInitialState(initQuery))\r\n\r\n  /*\r\n  * Retourne si une liste doit être affichée à l'écran ou pas.\r\n  * */\r\n  const displayList = (listName) => {\r\n    return state.activeList === listName && state.listVisible\r\n  }\r\n\r\n  /*\r\n  * Envois les paramètres de la recherche quand le filtre\r\n  * ou les termes de la recherche ont été mis à jour.\r\n  * */\r\n  useEffect(() => {\r\n    if (state !== getInitialState(initQuery)) {\r\n      setQuery({\r\n        terms: state.queryTerms,\r\n        filter: { value: state.activeFilter, type: state.filterType },\r\n        page: 0\r\n      });\r\n    }\r\n  }, [state.activeFilter, state.queryTerms])\r\n\r\n\r\n  useEffect(() => {\r\n    setTimeout(() => {\r\n      setState(prevState => {\r\n        return {\r\n          ...prevState,\r\n          listVisible: state.listOpen,\r\n        }\r\n      })\r\n    }, state.listOpen ? 350 : 0);\r\n  }, [state.listOpen]);\r\n\r\n  return (\r\n    <Fragment>\r\n      <BigInput\r\n        changeFilter={changeFilter}\r\n        listState={state}\r\n        changeQueryTerms={changeQueryTerms}\r\n        autoFocus={autoFocus}\r\n      />\r\n      <FilterBar\r\n        setListState={setState}\r\n        listState={state}\r\n        changeList={changeList}\r\n        changeFilter={changeFilter}/>\r\n      <ListsWrapper open={state.listOpen}>\r\n        <StickyBackground open={state.listOpen}/>\r\n        <div className={state.listVisible ? \"lists visible\" : \"lists\"}>\r\n          {displayList(\"author\") &&\r\n            <FilterList\r\n              separator={true}\r\n              changeFilter={changeFilter}\r\n              haveDisplayName={true}\r\n              data={coreData.authors.map(entry => (\r\n                {\r\n                  displayName: entry.lastName + \" \" + entry.firstName,\r\n                  fullName: entry.fullName\r\n                }\r\n              ) )}\r\n              fieldName={\"fullName\"}/>\r\n          }\r\n          {displayList(\"topics\") &&\r\n            <FilterList\r\n              separator={true}\r\n              changeFilter={changeFilter}\r\n              data={coreData.topics}\r\n              fieldName={\"name\"}/>\r\n          }\r\n          {displayList(\"type\") &&\r\n            <FilterList\r\n              separator={false}\r\n              changeFilter={changeFilter}\r\n              data={coreData.types}\r\n              haveDisplayName={true}\r\n              fieldName={\"name\"}/>\r\n          }\r\n        </div>\r\n      </ListsWrapper>\r\n    </Fragment>\r\n  )\r\n\r\n  function closeList() {\r\n    window.scrollTo({ top: 0 });\r\n    setState(prevState => {\r\n      return { ...prevState, listOpen: false }\r\n    })\r\n  }\r\n\r\n  function changeList(list) {\r\n    if (state.activeList === list && state.listOpen) {\r\n      closeList();\r\n      return;\r\n    }\r\n    setState(prevState => {\r\n      return { ...prevState, activeList: list, listOpen: true }\r\n    })\r\n  }\r\n\r\n  function changeFilter(value, type = state.activeList, curentTerms = false) {\r\n    if (!value) {\r\n      setState(prevState => {\r\n        return {\r\n          ...prevState,\r\n          queryTerms: curentTerms ? curentTerms : prevState.queryTerms,\r\n          filterOpen: false\r\n        }\r\n      })\r\n    }\r\n    setTimeout(() => {\r\n      setState(prevState => {\r\n        return {\r\n          ...prevState,\r\n          activeFilter: value,\r\n          filterType: type,\r\n          filterOpen: value !== false\r\n        }\r\n      })\r\n    }, value ? 0 : 250);\r\n    closeList();\r\n  }\r\n\r\n  function changeQueryTerms(value) {\r\n    setState(prevState => {\r\n      return { ...prevState, queryTerms: value }\r\n    })\r\n  }\r\n}\r\n\r\nexport default SearchTool;\r\n\r\nconst ListsWrapper = styled.div`\r\n  position: relative;\r\n  width: 100%;\r\n\r\n  div {\r\n    width: 100%;\r\n    //background-color: #EF4523;\r\n    //background-color: black;\r\n  }\r\n`;\r\n\r\nconst StickyBackground = styled.div`\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: ${props => props.open ? \"100\" : \"0\"}vh;\r\n  transform-origin: top;\r\n  background-color: #161616;\r\n  //background-color: #E9EDFB;\r\n  //background-color: #F45D3C;\r\n  transform: ${props => props.open ? \"scaleY(1)\" : \"scaleY(0)\"};\r\n  transition: all 650ms cubic-bezier(1, 0.72, 0.15, 1.01);\r\n  z-index: 10;\r\n`;\r\n", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\animation\\AnimatedList.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\animated-arrow.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\animated-icon.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\BlurPlay.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\button.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\Buttons\\BigCta.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\rounded-label.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\section-background.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\SocialMedia.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\atoms\\Spacer.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Button.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\AnimatedArrowButton.jsx", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\AnimatedTextButton.jsx", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\ButtonClose.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\ButtonLink.jsx", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\CircleCta.jsx", ["736", "737"], "import React from \"react\";\r\n\r\n// installed components\r\nimport styled from \"styled-components\";\r\n\r\nexport default function CircleCTA({ text, link, color = \"#000000\" }) {\r\n\r\n  return (\r\n    <Wrapper>\r\n      <Arrow>\r\n        <img src=\"/images/icons/arrow.svg\"/>\r\n      </Arrow>\r\n\r\n      <svg width=\"120\" height=\"120\">\r\n        <path\r\n          fill={\"transparent\"}\r\n          id=\"circle-path\"\r\n          d=\"\r\n\t\t\t\t\t\tM 30, 60\r\n\t\t\t\t\t\ta 30,30 0 1,1 60,0\r\n\t\t\t\t\t\ta 30,30 0 1,1 -60,0\r\n\t\t\t\t\t\t\"\r\n        />\r\n        <text fill={color}>\r\n          <textPath xlinkHref=\"#circle-path\">\r\n            {text} {text} {text} {text}\r\n          </textPath>\r\n        </text>\r\n      </svg>\r\n\r\n    </Wrapper>\r\n  );\r\n}\r\n\r\nconst Wrapper = styled.div`\r\n\r\n\tposition: relative;\r\n\theight: 120px;\r\n\twidth: 120px;\r\n\r\n\tsvg {\r\n\t\tfont-size: 14px;\r\n\t\tletter-spacing: 2px;\r\n\t\tposition: relative;\r\n\t\tz-index: 101;\r\n\r\n\t\t:hover {\r\n\t\t\tcursor: pointer;\r\n\t\t\tanimation-name: rotate;\r\n\t\t\tanimation-duration: 6s;\r\n\t\t\tanimation-iteration-count: infinite;\r\n\t\t\tanimation-timing-function: linear;\r\n\t\t}\r\n\r\n\t\t@keyframes rotate {\r\n\t\t\tfrom {\r\n\t\t\t\ttransform: rotate(0deg);\r\n\t\t\t}\r\n\t\t\tto {\r\n\t\t\t\ttransform: rotate(360deg);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n`;\r\n\r\nconst Arrow = styled.div`\r\n\tposition: absolute;\r\n\theight: 100%;\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\r\n\timg {\r\n\t\twidth: 32px;\r\n\t}\r\n\r\n\tz-index: 100;\r\n`;\r\n", "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\MediaButton.jsx", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Buttons\\SmallButton.jsx", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\CornerStoneCard.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\DefaultCard.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\Elements\\Author.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\Elements\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\Elements\\Speakers.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\first-article-card.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\GridCard.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\HorizontalPostCard.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\HorizontalReversePostCard.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\LargeCard.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\LargeRelatedCard.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\search-card.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\slide-card.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\square-vertical-card.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\SquareVerticalFeatured.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Card\\VerticalCard.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\CardPost.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\categories\\CategoriesHeader.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\categories\\SectionMinistries.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\categories\\SectionVocations.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\condimage.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\CondLink.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\ConvertkitForm\\CKForm.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\ConvertkitForm\\DynamicForm.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\CookieWall.jsx", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\DuotoneFilter.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Featured.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\icons.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\inputs\\checkbox.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\inputs\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\inputs\\toggle.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\ListLink.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\pagination\\ssr-paginate.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\pagination\\sticky-pagination\\AnimatedNumber.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\pagination\\sticky-pagination\\NavigationButtons.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\pagination\\sticky-pagination\\PaginateSection.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\pagination\\sticky-pagination\\PaginationNumber.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\pagination\\sticky-pagination\\StickyPagination.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\post\\author-box.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\post\\md-body.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Preview.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\RefTagger.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Related.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\RenderMardown.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\section\\GridCardSection.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\section\\SectionHeader.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\SimplePagination.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\Speakers.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\strapi-modules\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\strapi-modules\\YoutubeEmbed.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\SubHeader\\Items\\Authors.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\SubHeader\\Items\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\SubHeader\\Items\\LinkButton.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\SubHeader\\Items\\Social.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\SubHeader\\Items\\Text.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\SubHeader\\SubHeader.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\topics-horizontal-list.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\shared\\VideoPlayer.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\svg\\chevron-down.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\svg\\index.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\svg\\long-arrow.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\test\\card\\SliderCard.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\test\\Carousel.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\RegisterBar.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\Ticket\\svg\\NotchSvgBlack.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\Ticket\\svg\\NotchSvgBlackMobile.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\Ticket\\svg\\NotchSvgWhite.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\Ticket\\svg\\NotchSvgWhiteMobile.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\Ticket\\Ticket.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\Ticket\\TicketInfo.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\WebEvent.js", ["738"], "import styled from \"styled-components\";\r\nimport { dateForHumans } from \"utils/date.utils\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { withRealSrc } from \"utils/image-utils\";\r\n\r\nexport default function WebEvent({ webEvent }) {\r\n  const eventDate = dateForHumans(webEvent.modules.event.date);\r\n  return (\r\n    <Wrapper className={\"site-padding\"}>\r\n      <h2>Prochaine webinaire:</h2>\r\n      <h3>{webEvent.title}</h3>\r\n      <p className={\"webevent-date\"}> Date : {eventDate}</p>\r\n      <div className={\"image-wrapper\"}>\r\n        <Image src={withRealSrc(webEvent.image)} layout={\"fill\"}/>\r\n      </div>\r\n      <Link href={`/webinaires/${webEvent.slug}`} >\r\n        <p className={\"webevent-cta\"}> En savoir plus </p>\r\n      </Link>\r\n      <a href={webEvent.modules.event.url} target=\"_blank\" rel=\"noopener noreferrer\">\r\n        <p className={\"webevent-cta\"}>INSCRIPTION</p>\r\n      </a>\r\n    </Wrapper>\r\n  )\r\n}\r\n\r\nconst Wrapper = styled.div`\r\n  padding-top: 24px;\r\n  height: 40vw;\r\n  background-color: orange;\r\n  .webevent-date {\r\n    display: inline-block;\r\n    padding: 12px;\r\n    font-size: 32px;\r\n    color: white;\r\n  }\r\n  .webevent-cta {\r\n    display: inline-block;\r\n    text-align: center;\r\n    height: 50px;\r\n    padding-top: 16px;\r\n    width: 120px;\r\n    color: white;\r\n    border-radius: 100px;\r\n    background-color: green;\r\n    cursor: pointer;\r\n  }\r\n  .image-wrapper {\r\n    position: relative;\r\n    width: 400px;\r\n    height: 200px;\r\n  }\r\n`\r\n", "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\WebSelector.js", [], "C:\\rep\\TPSG\\tpsg-next\\components\\webinars\\WepisodeCard.js", ["739"], "import styled from \"styled-components\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { withRealSrc } from \"utils/image-utils\";\r\nimport { dateForHumans } from \"utils/date.utils\";\r\n\r\n\r\nconst Speakers = ({ speakers }) => {\r\n  if (!speakers) return null;\r\n  return (\r\n    <>\r\n      {speakers.map((x, key) => <span key={key}>{x.fullName}</span>)}\r\n    </>\r\n  )\r\n}\r\n\r\nconst WepisodeCard = ({ post }) => {\r\n  const { webinar } = post.modules;\r\n  return (\r\n    <Link href={`/webinaires/${post.slug}`}>\r\n      <CardWrapper>\r\n        <ImageWrapper>\r\n          <Image src={withRealSrc(post.image)} layout={\"fill\"} objectFit={\"cover\"}/>\r\n        </ImageWrapper>\r\n        <TextWrapper>\r\n          <p>{post.title}</p>\r\n          <p>{dateForHumans(post.published_at)}</p>\r\n          <Speakers speakers={webinar.speakers}/>\r\n        </TextWrapper>\r\n      </CardWrapper>\r\n    </Link>\r\n  )\r\n}\r\n\r\n\r\nexport default WepisodeCard;\r\n\r\nconst CardWrapper = styled.div`\r\n  display: flex;\r\n  flex-direction: row;\r\n  cursor: pointer;\r\n`;\r\n\r\nconst ImageWrapper = styled.div`\r\n  position: relative;\r\n  width: 100px;\r\n  height: 100px;\r\n`;\r\n\r\nconst TextWrapper = styled.div`\r\n  margin-left: 16px;\r\n  span {\r\n    color: green;\r\n  }\r\n`;\r\n", {"ruleId": "740", "severity": 1, "message": "741", "line": 21, "column": 33, "nodeType": "742", "endLine": 21, "endColumn": 42}, {"ruleId": "743", "severity": 1, "message": "744", "line": 32, "column": 6, "nodeType": "745", "endLine": 32, "endColumn": 19, "suggestions": "746"}, {"ruleId": "747", "severity": 1, "message": "748", "line": 7, "column": 7, "nodeType": "749", "endLine": 7, "endColumn": 87}, {"ruleId": "747", "severity": 1, "message": "748", "line": 7, "column": 7, "nodeType": "749", "endLine": 7, "endColumn": 87}, {"ruleId": "743", "severity": 1, "message": "750", "line": 62, "column": 6, "nodeType": "745", "endLine": 62, "endColumn": 18, "suggestions": "751"}, {"ruleId": "743", "severity": 1, "message": "752", "line": 53, "column": 6, "nodeType": "745", "endLine": 53, "endColumn": 44, "suggestions": "753"}, {"ruleId": "754", "severity": 1, "message": "755", "line": 11, "column": 9, "nodeType": "749", "endLine": 11, "endColumn": 45}, {"ruleId": "747", "severity": 1, "message": "756", "line": 11, "column": 9, "nodeType": "749", "endLine": 11, "endColumn": 45}, {"ruleId": "747", "severity": 1, "message": "748", "line": 15, "column": 9, "nodeType": "749", "endLine": 15, "endColumn": 67}, {"ruleId": "747", "severity": 1, "message": "748", "line": 23, "column": 11, "nodeType": "749", "endLine": 23, "endColumn": 85}, "react-hooks/rules-of-hooks", "React Hook \"useRouter\" is called in function \"createCanonicalUrl\" that is neither a React function component nor a custom React Hook function. React component names must start with an uppercase letter. React Hook names must start with the word \"use\".", "Identifier", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'onDropDownClickOutside'. Either include it or remove the dependency array.", "ArrayExpression", ["757"], "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'updateCaret'. Either include it or remove the dependency array.", ["758"], "React Hook useEffect has missing dependencies: 'initQuery', 'setQuery', and 'state'. Either include them or remove the dependency array. If 'setQuery' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["759"], "@next/next/no-img-element", "Do not use `<img>` element. Use `<Image />` from `next/image` instead. See: https://nextjs.org/docs/messages/no-img-element", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", {"desc": "760", "fix": "761"}, {"desc": "762", "fix": "763"}, {"desc": "764", "fix": "765"}, "Update the dependencies array to be: [headerState, onDropDownClickOutside]", {"range": "766", "text": "767"}, "Update the dependencies array to be: [inputValue, updateCaret]", {"range": "768", "text": "769"}, "Update the dependencies array to be: [initQuery, setQuery, state, state.activeFilter, state.queryTerms]", {"range": "770", "text": "771"}, [976, 989], "[headerState, onDropDownClickOutside]", [1768, 1780], "[inputValue, updateCaret]", [1992, 2030], "[initQ<PERSON>y, setQuery, state, state.activeFilter, state.queryTerms]"]