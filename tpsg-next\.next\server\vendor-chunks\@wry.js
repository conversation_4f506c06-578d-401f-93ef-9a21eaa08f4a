"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wry";
exports.ids = ["vendor-chunks/@wry"];
exports.modules = {

/***/ "(rsc)/./node_modules/@wry/context/lib/context.js":
/*!**************************************************!*\
  !*** ./node_modules/@wry/context/lib/context.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n// This currentContext variable will only be used if the makeSlotClass\n// function is called, which happens only if this is the first copy of the\n// @wry/context package to be imported.\nvar currentContext = null;\n// This unique internal object is used to denote the absence of a value\n// for a given Slot, and is never exposed to outside code.\nvar MISSING_VALUE = {};\nvar idCounter = 1;\n// Although we can't do anything about the cost of duplicated code from\n// accidentally bundling multiple copies of the @wry/context package, we can\n// avoid creating the Slot class more than once using makeSlotClass.\nvar makeSlotClass = function() {\n    return /** @class */ function() {\n        function Slot() {\n            // If you have a Slot object, you can find out its slot.id, but you cannot\n            // guess the slot.id of a Slot you don't have access to, thanks to the\n            // randomized suffix.\n            this.id = [\n                \"slot\",\n                idCounter++,\n                Date.now(),\n                Math.random().toString(36).slice(2)\n            ].join(\":\");\n        }\n        Slot.prototype.hasValue = function() {\n            for(var context_1 = currentContext; context_1; context_1 = context_1.parent){\n                // We use the Slot object iself as a key to its value, which means the\n                // value cannot be obtained without a reference to the Slot object.\n                if (this.id in context_1.slots) {\n                    var value = context_1.slots[this.id];\n                    if (value === MISSING_VALUE) break;\n                    if (context_1 !== currentContext) {\n                        // Cache the value in currentContext.slots so the next lookup will\n                        // be faster. This caching is safe because the tree of contexts and\n                        // the values of the slots are logically immutable.\n                        currentContext.slots[this.id] = value;\n                    }\n                    return true;\n                }\n            }\n            if (currentContext) {\n                // If a value was not found for this Slot, it's never going to be found\n                // no matter how many times we look it up, so we might as well cache\n                // the absence of the value, too.\n                currentContext.slots[this.id] = MISSING_VALUE;\n            }\n            return false;\n        };\n        Slot.prototype.getValue = function() {\n            if (this.hasValue()) {\n                return currentContext.slots[this.id];\n            }\n        };\n        Slot.prototype.withValue = function(value, callback, // Given the prevalence of arrow functions, specifying arguments is likely\n        // to be much more common than specifying `this`, hence this ordering:\n        args, thisArg) {\n            var _a;\n            var slots = (_a = {\n                __proto__: null\n            }, _a[this.id] = value, _a);\n            var parent = currentContext;\n            currentContext = {\n                parent: parent,\n                slots: slots\n            };\n            try {\n                // Function.prototype.apply allows the arguments array argument to be\n                // omitted or undefined, so args! is fine here.\n                return callback.apply(thisArg, args);\n            } finally{\n                currentContext = parent;\n            }\n        };\n        // Capture the current context and wrap a callback function so that it\n        // reestablishes the captured context when called.\n        Slot.bind = function(callback) {\n            var context = currentContext;\n            return function() {\n                var saved = currentContext;\n                try {\n                    currentContext = context;\n                    return callback.apply(this, arguments);\n                } finally{\n                    currentContext = saved;\n                }\n            };\n        };\n        // Immediately run a callback function without any captured context.\n        Slot.noContext = function(callback, // Given the prevalence of arrow functions, specifying arguments is likely\n        // to be much more common than specifying `this`, hence this ordering:\n        args, thisArg) {\n            if (currentContext) {\n                var saved = currentContext;\n                try {\n                    currentContext = null;\n                    // Function.prototype.apply allows the arguments array argument to be\n                    // omitted or undefined, so args! is fine here.\n                    return callback.apply(thisArg, args);\n                } finally{\n                    currentContext = saved;\n                }\n            } else {\n                return callback.apply(thisArg, args);\n            }\n        };\n        return Slot;\n    }();\n};\nfunction maybe(fn) {\n    try {\n        return fn();\n    } catch (ignored) {}\n}\n// We store a single global implementation of the Slot class as a permanent\n// non-enumerable property of the globalThis object. This obfuscation does\n// nothing to prevent access to the Slot class, but at least it ensures the\n// implementation (i.e. currentContext) cannot be tampered with, and all copies\n// of the @wry/context package (hopefully just one) will share the same Slot\n// implementation. Since the first copy of the @wry/context package to be\n// imported wins, this technique imposes a steep cost for any future breaking\n// changes to the Slot class.\nvar globalKey = \"@wry/context:Slot\";\nvar host = // Prefer globalThis when available.\n// https://github.com/benjamn/wryware/issues/347\nmaybe(function() {\n    return globalThis;\n}) || // Fall back to global, which works in Node.js and may be converted by some\n// bundlers to the appropriate identifier (window, self, ...) depending on the\n// bundling target. https://github.com/endojs/endo/issues/576#issuecomment-1178515224\nmaybe(function() {\n    return global;\n}) || // Otherwise, use a dummy host that's local to this module. We used to fall\n// back to using the Array constructor as a namespace, but that was flagged in\n// https://github.com/benjamn/wryware/issues/347, and can be avoided.\nObject.create(null);\n// Whichever globalHost we're using, make TypeScript happy about the additional\n// globalKey property.\nvar globalHost = host;\nvar Slot = globalHost[globalKey] || // Earlier versions of this package stored the globalKey property on the Array\n// constructor, so we check there as well, to prevent Slot class duplication.\nArray[globalKey] || function(Slot) {\n    try {\n        Object.defineProperty(globalHost, globalKey, {\n            value: Slot,\n            enumerable: false,\n            writable: false,\n            // When it was possible for globalHost to be the Array constructor (a\n            // legacy Slot dedup strategy), it was important for the property to be\n            // configurable:true so it could be deleted. That does not seem to be as\n            // important when globalHost is the global object, but I don't want to\n            // cause similar problems again, and configurable:true seems safest.\n            // https://github.com/endojs/endo/issues/576#issuecomment-1178274008\n            configurable: true\n        });\n    } finally{\n        return Slot;\n    }\n}(makeSlotClass());\nvar bind = Slot.bind, noContext = Slot.noContext;\nfunction setTimeoutWithContext(callback, delay) {\n    return setTimeout(bind(callback), delay);\n}\n// Turn any generator function into an async function (using yield instead\n// of await), with context automatically preserved across yields.\nfunction asyncFromGen(genFn) {\n    return function() {\n        var gen = genFn.apply(this, arguments);\n        var boundNext = bind(gen.next);\n        var boundThrow = bind(gen.throw);\n        return new Promise(function(resolve, reject) {\n            function invoke(method, argument) {\n                try {\n                    var result = method.call(gen, argument);\n                } catch (error) {\n                    return reject(error);\n                }\n                var next = result.done ? resolve : invokeNext;\n                if (isPromiseLike(result.value)) {\n                    result.value.then(next, result.done ? reject : invokeThrow);\n                } else {\n                    next(result.value);\n                }\n            }\n            var invokeNext = function(value) {\n                return invoke(boundNext, value);\n            };\n            var invokeThrow = function(error) {\n                return invoke(boundThrow, error);\n            };\n            invokeNext();\n        });\n    };\n}\nfunction isPromiseLike(value) {\n    return value && typeof value.then === \"function\";\n}\n// If you use the fibers npm package to implement coroutines in Node.js,\n// you should call this function at least once to ensure context management\n// remains coherent across any yields.\nvar wrappedFibers = [];\nfunction wrapYieldingFiberMethods(Fiber) {\n    // There can be only one implementation of Fiber per process, so this array\n    // should never grow longer than one element.\n    if (wrappedFibers.indexOf(Fiber) < 0) {\n        var wrap = function(obj, method) {\n            var fn = obj[method];\n            obj[method] = function() {\n                return noContext(fn, arguments, this);\n            };\n        };\n        // These methods can yield, according to\n        // https://github.com/laverdet/node-fibers/blob/ddebed9b8ae3883e57f822e2108e6943e5c8d2a8/fibers.js#L97-L100\n        wrap(Fiber, \"yield\");\n        wrap(Fiber.prototype, \"run\");\n        wrap(Fiber.prototype, \"throwInto\");\n        wrappedFibers.push(Fiber);\n    }\n    return Fiber;\n}\nexports.Slot = Slot;\nexports.asyncFromGen = asyncFromGen;\nexports.bind = bind;\nexports.noContext = noContext;\nexports.setTimeout = setTimeoutWithContext;\nexports.wrapYieldingFiberMethods = wrapYieldingFiberMethods; //# sourceMappingURL=context.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHdyeS9jb250ZXh0L2xpYi9jb250ZXh0LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUFBLDhDQUE2QztJQUFFRyxPQUFPO0FBQUssQ0FBQyxFQUFDO0FBRTdELHNFQUFzRTtBQUN0RSwwRUFBMEU7QUFDMUUsdUNBQXVDO0FBQ3ZDLElBQUlDLGlCQUFpQjtBQUNyQix1RUFBdUU7QUFDdkUsMERBQTBEO0FBQzFELElBQUlDLGdCQUFnQixDQUFDO0FBQ3JCLElBQUlDLFlBQVk7QUFDaEIsdUVBQXVFO0FBQ3ZFLDRFQUE0RTtBQUM1RSxvRUFBb0U7QUFDcEUsSUFBSUMsZ0JBQWdCO0lBQWMsT0FBTyxXQUFXLEdBQUk7UUFDcEQsU0FBU0M7WUFDTCwwRUFBMEU7WUFDMUUsc0VBQXNFO1lBQ3RFLHFCQUFxQjtZQUNyQixJQUFJLENBQUNDLEVBQUUsR0FBRztnQkFDTjtnQkFDQUg7Z0JBQ0FJLEtBQUtDLEdBQUc7Z0JBQ1JDLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLEtBQUssQ0FBQzthQUNwQyxDQUFDQyxJQUFJLENBQUM7UUFDWDtRQUNBUixLQUFLUyxTQUFTLENBQUNDLFFBQVEsR0FBRztZQUN0QixJQUFLLElBQUlDLFlBQVlmLGdCQUFnQmUsV0FBV0EsWUFBWUEsVUFBVUMsTUFBTSxDQUFFO2dCQUMxRSxzRUFBc0U7Z0JBQ3RFLG1FQUFtRTtnQkFDbkUsSUFBSSxJQUFJLENBQUNYLEVBQUUsSUFBSVUsVUFBVUUsS0FBSyxFQUFFO29CQUM1QixJQUFJbEIsUUFBUWdCLFVBQVVFLEtBQUssQ0FBQyxJQUFJLENBQUNaLEVBQUUsQ0FBQztvQkFDcEMsSUFBSU4sVUFBVUUsZUFDVjtvQkFDSixJQUFJYyxjQUFjZixnQkFBZ0I7d0JBQzlCLGtFQUFrRTt3QkFDbEUsbUVBQW1FO3dCQUNuRSxtREFBbUQ7d0JBQ25EQSxlQUFlaUIsS0FBSyxDQUFDLElBQUksQ0FBQ1osRUFBRSxDQUFDLEdBQUdOO29CQUNwQztvQkFDQSxPQUFPO2dCQUNYO1lBQ0o7WUFDQSxJQUFJQyxnQkFBZ0I7Z0JBQ2hCLHVFQUF1RTtnQkFDdkUsb0VBQW9FO2dCQUNwRSxpQ0FBaUM7Z0JBQ2pDQSxlQUFlaUIsS0FBSyxDQUFDLElBQUksQ0FBQ1osRUFBRSxDQUFDLEdBQUdKO1lBQ3BDO1lBQ0EsT0FBTztRQUNYO1FBQ0FHLEtBQUtTLFNBQVMsQ0FBQ0ssUUFBUSxHQUFHO1lBQ3RCLElBQUksSUFBSSxDQUFDSixRQUFRLElBQUk7Z0JBQ2pCLE9BQU9kLGVBQWVpQixLQUFLLENBQUMsSUFBSSxDQUFDWixFQUFFLENBQUM7WUFDeEM7UUFDSjtRQUNBRCxLQUFLUyxTQUFTLENBQUNNLFNBQVMsR0FBRyxTQUFVcEIsS0FBSyxFQUFFcUIsUUFBUSxFQUNwRCwwRUFBMEU7UUFDMUUsc0VBQXNFO1FBQ3RFQyxJQUFJLEVBQUVDLE9BQU87WUFDVCxJQUFJQztZQUNKLElBQUlOLFFBQVNNLENBQUFBLEtBQUs7Z0JBQ1ZDLFdBQVc7WUFDZixHQUNBRCxFQUFFLENBQUMsSUFBSSxDQUFDbEIsRUFBRSxDQUFDLEdBQUdOLE9BQ2R3QixFQUFDO1lBQ0wsSUFBSVAsU0FBU2hCO1lBQ2JBLGlCQUFpQjtnQkFBRWdCLFFBQVFBO2dCQUFRQyxPQUFPQTtZQUFNO1lBQ2hELElBQUk7Z0JBQ0EscUVBQXFFO2dCQUNyRSwrQ0FBK0M7Z0JBQy9DLE9BQU9HLFNBQVNLLEtBQUssQ0FBQ0gsU0FBU0Q7WUFDbkMsU0FDUTtnQkFDSnJCLGlCQUFpQmdCO1lBQ3JCO1FBQ0o7UUFDQSxzRUFBc0U7UUFDdEUsa0RBQWtEO1FBQ2xEWixLQUFLc0IsSUFBSSxHQUFHLFNBQVVOLFFBQVE7WUFDMUIsSUFBSU8sVUFBVTNCO1lBQ2QsT0FBTztnQkFDSCxJQUFJNEIsUUFBUTVCO2dCQUNaLElBQUk7b0JBQ0FBLGlCQUFpQjJCO29CQUNqQixPQUFPUCxTQUFTSyxLQUFLLENBQUMsSUFBSSxFQUFFSTtnQkFDaEMsU0FDUTtvQkFDSjdCLGlCQUFpQjRCO2dCQUNyQjtZQUNKO1FBQ0o7UUFDQSxvRUFBb0U7UUFDcEV4QixLQUFLMEIsU0FBUyxHQUFHLFNBQVVWLFFBQVEsRUFDbkMsMEVBQTBFO1FBQzFFLHNFQUFzRTtRQUN0RUMsSUFBSSxFQUFFQyxPQUFPO1lBQ1QsSUFBSXRCLGdCQUFnQjtnQkFDaEIsSUFBSTRCLFFBQVE1QjtnQkFDWixJQUFJO29CQUNBQSxpQkFBaUI7b0JBQ2pCLHFFQUFxRTtvQkFDckUsK0NBQStDO29CQUMvQyxPQUFPb0IsU0FBU0ssS0FBSyxDQUFDSCxTQUFTRDtnQkFDbkMsU0FDUTtvQkFDSnJCLGlCQUFpQjRCO2dCQUNyQjtZQUNKLE9BQ0s7Z0JBQ0QsT0FBT1IsU0FBU0ssS0FBSyxDQUFDSCxTQUFTRDtZQUNuQztRQUNKO1FBQ0EsT0FBT2pCO0lBQ1g7QUFBTTtBQUNOLFNBQVMyQixNQUFNQyxFQUFFO0lBQ2IsSUFBSTtRQUNBLE9BQU9BO0lBQ1gsRUFDQSxPQUFPQyxTQUFTLENBQUU7QUFDdEI7QUFDQSwyRUFBMkU7QUFDM0UsMEVBQTBFO0FBQzFFLDJFQUEyRTtBQUMzRSwrRUFBK0U7QUFDL0UsNEVBQTRFO0FBQzVFLHlFQUF5RTtBQUN6RSw2RUFBNkU7QUFDN0UsNkJBQTZCO0FBQzdCLElBQUlDLFlBQVk7QUFDaEIsSUFBSUMsT0FDSixvQ0FBb0M7QUFDcEMsZ0RBQWdEO0FBQ2hESixNQUFNO0lBQWMsT0FBT0s7QUFBWSxNQUNuQywyRUFBMkU7QUFDM0UsOEVBQThFO0FBQzlFLHFGQUFxRjtBQUNyRkwsTUFBTTtJQUFjLE9BQU9NO0FBQVEsTUFDbkMsMkVBQTJFO0FBQzNFLDhFQUE4RTtBQUM5RSxxRUFBcUU7QUFDckV6QyxPQUFPMEMsTUFBTSxDQUFDO0FBQ2xCLCtFQUErRTtBQUMvRSxzQkFBc0I7QUFDdEIsSUFBSUMsYUFBYUo7QUFDakIsSUFBSS9CLE9BQU9tQyxVQUFVLENBQUNMLFVBQVUsSUFDNUIsOEVBQThFO0FBQzlFLDZFQUE2RTtBQUM3RU0sS0FBSyxDQUFDTixVQUFVLElBQ2hCLFNBQVc5QixJQUFJO0lBQ1gsSUFBSTtRQUNBUixPQUFPQyxjQUFjLENBQUMwQyxZQUFZTCxXQUFXO1lBQ3pDbkMsT0FBT0s7WUFDUHFDLFlBQVk7WUFDWkMsVUFBVTtZQUNWLHFFQUFxRTtZQUNyRSx1RUFBdUU7WUFDdkUsd0VBQXdFO1lBQ3hFLHNFQUFzRTtZQUN0RSxvRUFBb0U7WUFDcEUsb0VBQW9FO1lBQ3BFQyxjQUFjO1FBQ2xCO0lBQ0osU0FDUTtRQUNKLE9BQU92QztJQUNYO0FBQ0osRUFBR0Q7QUFFUCxJQUFJdUIsT0FBT3RCLEtBQUtzQixJQUFJLEVBQUVJLFlBQVkxQixLQUFLMEIsU0FBUztBQUNoRCxTQUFTYyxzQkFBc0J4QixRQUFRLEVBQUV5QixLQUFLO0lBQzFDLE9BQU9DLFdBQVdwQixLQUFLTixXQUFXeUI7QUFDdEM7QUFDQSwwRUFBMEU7QUFDMUUsaUVBQWlFO0FBQ2pFLFNBQVNFLGFBQWFDLEtBQUs7SUFDdkIsT0FBTztRQUNILElBQUlDLE1BQU1ELE1BQU12QixLQUFLLENBQUMsSUFBSSxFQUFFSTtRQUM1QixJQUFJcUIsWUFBWXhCLEtBQUt1QixJQUFJRSxJQUFJO1FBQzdCLElBQUlDLGFBQWExQixLQUFLdUIsSUFBSUksS0FBSztRQUMvQixPQUFPLElBQUlDLFFBQVEsU0FBVUMsT0FBTyxFQUFFQyxNQUFNO1lBQ3hDLFNBQVNDLE9BQU9DLE1BQU0sRUFBRUMsUUFBUTtnQkFDNUIsSUFBSTtvQkFDQSxJQUFJQyxTQUFTRixPQUFPRyxJQUFJLENBQUNaLEtBQUtVO2dCQUNsQyxFQUNBLE9BQU9HLE9BQU87b0JBQ1YsT0FBT04sT0FBT007Z0JBQ2xCO2dCQUNBLElBQUlYLE9BQU9TLE9BQU9HLElBQUksR0FBR1IsVUFBVVM7Z0JBQ25DLElBQUlDLGNBQWNMLE9BQU83RCxLQUFLLEdBQUc7b0JBQzdCNkQsT0FBTzdELEtBQUssQ0FBQ21FLElBQUksQ0FBQ2YsTUFBTVMsT0FBT0csSUFBSSxHQUFHUCxTQUFTVztnQkFDbkQsT0FDSztvQkFDRGhCLEtBQUtTLE9BQU83RCxLQUFLO2dCQUNyQjtZQUNKO1lBQ0EsSUFBSWlFLGFBQWEsU0FBVWpFLEtBQUs7Z0JBQUksT0FBTzBELE9BQU9QLFdBQVduRDtZQUFRO1lBQ3JFLElBQUlvRSxjQUFjLFNBQVVMLEtBQUs7Z0JBQUksT0FBT0wsT0FBT0wsWUFBWVU7WUFBUTtZQUN2RUU7UUFDSjtJQUNKO0FBQ0o7QUFDQSxTQUFTQyxjQUFjbEUsS0FBSztJQUN4QixPQUFPQSxTQUFTLE9BQU9BLE1BQU1tRSxJQUFJLEtBQUs7QUFDMUM7QUFDQSx3RUFBd0U7QUFDeEUsMkVBQTJFO0FBQzNFLHNDQUFzQztBQUN0QyxJQUFJRSxnQkFBZ0IsRUFBRTtBQUN0QixTQUFTQyx5QkFBeUJDLEtBQUs7SUFDbkMsMkVBQTJFO0lBQzNFLDZDQUE2QztJQUM3QyxJQUFJRixjQUFjRyxPQUFPLENBQUNELFNBQVMsR0FBRztRQUNsQyxJQUFJRSxPQUFPLFNBQVVDLEdBQUcsRUFBRWYsTUFBTTtZQUM1QixJQUFJMUIsS0FBS3lDLEdBQUcsQ0FBQ2YsT0FBTztZQUNwQmUsR0FBRyxDQUFDZixPQUFPLEdBQUc7Z0JBQ1YsT0FBTzVCLFVBQVVFLElBQUlILFdBQVcsSUFBSTtZQUN4QztRQUNKO1FBQ0Esd0NBQXdDO1FBQ3hDLDJHQUEyRztRQUMzRzJDLEtBQUtGLE9BQU87UUFDWkUsS0FBS0YsTUFBTXpELFNBQVMsRUFBRTtRQUN0QjJELEtBQUtGLE1BQU16RCxTQUFTLEVBQUU7UUFDdEJ1RCxjQUFjTSxJQUFJLENBQUNKO0lBQ3ZCO0lBQ0EsT0FBT0E7QUFDWDtBQUVBeEUsWUFBWSxHQUFHTTtBQUNmTixvQkFBb0IsR0FBR2lEO0FBQ3ZCakQsWUFBWSxHQUFHNEI7QUFDZjVCLGlCQUFpQixHQUFHZ0M7QUFDcEJoQyxrQkFBa0IsR0FBRzhDO0FBQ3JCOUMsZ0NBQWdDLEdBQUd1RSwwQkFDbkMsbUNBQW1DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHBzZy1uZXh0Ly4vbm9kZV9tb2R1bGVzL0B3cnkvY29udGV4dC9saWIvY29udGV4dC5qcz81OGJkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcblxuLy8gVGhpcyBjdXJyZW50Q29udGV4dCB2YXJpYWJsZSB3aWxsIG9ubHkgYmUgdXNlZCBpZiB0aGUgbWFrZVNsb3RDbGFzc1xyXG4vLyBmdW5jdGlvbiBpcyBjYWxsZWQsIHdoaWNoIGhhcHBlbnMgb25seSBpZiB0aGlzIGlzIHRoZSBmaXJzdCBjb3B5IG9mIHRoZVxyXG4vLyBAd3J5L2NvbnRleHQgcGFja2FnZSB0byBiZSBpbXBvcnRlZC5cclxudmFyIGN1cnJlbnRDb250ZXh0ID0gbnVsbDtcclxuLy8gVGhpcyB1bmlxdWUgaW50ZXJuYWwgb2JqZWN0IGlzIHVzZWQgdG8gZGVub3RlIHRoZSBhYnNlbmNlIG9mIGEgdmFsdWVcclxuLy8gZm9yIGEgZ2l2ZW4gU2xvdCwgYW5kIGlzIG5ldmVyIGV4cG9zZWQgdG8gb3V0c2lkZSBjb2RlLlxyXG52YXIgTUlTU0lOR19WQUxVRSA9IHt9O1xyXG52YXIgaWRDb3VudGVyID0gMTtcclxuLy8gQWx0aG91Z2ggd2UgY2FuJ3QgZG8gYW55dGhpbmcgYWJvdXQgdGhlIGNvc3Qgb2YgZHVwbGljYXRlZCBjb2RlIGZyb21cclxuLy8gYWNjaWRlbnRhbGx5IGJ1bmRsaW5nIG11bHRpcGxlIGNvcGllcyBvZiB0aGUgQHdyeS9jb250ZXh0IHBhY2thZ2UsIHdlIGNhblxyXG4vLyBhdm9pZCBjcmVhdGluZyB0aGUgU2xvdCBjbGFzcyBtb3JlIHRoYW4gb25jZSB1c2luZyBtYWtlU2xvdENsYXNzLlxyXG52YXIgbWFrZVNsb3RDbGFzcyA9IGZ1bmN0aW9uICgpIHsgcmV0dXJuIC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uICgpIHtcclxuICAgIGZ1bmN0aW9uIFNsb3QoKSB7XHJcbiAgICAgICAgLy8gSWYgeW91IGhhdmUgYSBTbG90IG9iamVjdCwgeW91IGNhbiBmaW5kIG91dCBpdHMgc2xvdC5pZCwgYnV0IHlvdSBjYW5ub3RcclxuICAgICAgICAvLyBndWVzcyB0aGUgc2xvdC5pZCBvZiBhIFNsb3QgeW91IGRvbid0IGhhdmUgYWNjZXNzIHRvLCB0aGFua3MgdG8gdGhlXHJcbiAgICAgICAgLy8gcmFuZG9taXplZCBzdWZmaXguXHJcbiAgICAgICAgdGhpcy5pZCA9IFtcclxuICAgICAgICAgICAgXCJzbG90XCIsXHJcbiAgICAgICAgICAgIGlkQ291bnRlcisrLFxyXG4gICAgICAgICAgICBEYXRlLm5vdygpLFxyXG4gICAgICAgICAgICBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zbGljZSgyKSxcclxuICAgICAgICBdLmpvaW4oXCI6XCIpO1xyXG4gICAgfVxyXG4gICAgU2xvdC5wcm90b3R5cGUuaGFzVmFsdWUgPSBmdW5jdGlvbiAoKSB7XHJcbiAgICAgICAgZm9yICh2YXIgY29udGV4dF8xID0gY3VycmVudENvbnRleHQ7IGNvbnRleHRfMTsgY29udGV4dF8xID0gY29udGV4dF8xLnBhcmVudCkge1xyXG4gICAgICAgICAgICAvLyBXZSB1c2UgdGhlIFNsb3Qgb2JqZWN0IGlzZWxmIGFzIGEga2V5IHRvIGl0cyB2YWx1ZSwgd2hpY2ggbWVhbnMgdGhlXHJcbiAgICAgICAgICAgIC8vIHZhbHVlIGNhbm5vdCBiZSBvYnRhaW5lZCB3aXRob3V0IGEgcmVmZXJlbmNlIHRvIHRoZSBTbG90IG9iamVjdC5cclxuICAgICAgICAgICAgaWYgKHRoaXMuaWQgaW4gY29udGV4dF8xLnNsb3RzKSB7XHJcbiAgICAgICAgICAgICAgICB2YXIgdmFsdWUgPSBjb250ZXh0XzEuc2xvdHNbdGhpcy5pZF07XHJcbiAgICAgICAgICAgICAgICBpZiAodmFsdWUgPT09IE1JU1NJTkdfVkFMVUUpXHJcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgICAgICBpZiAoY29udGV4dF8xICE9PSBjdXJyZW50Q29udGV4dCkge1xyXG4gICAgICAgICAgICAgICAgICAgIC8vIENhY2hlIHRoZSB2YWx1ZSBpbiBjdXJyZW50Q29udGV4dC5zbG90cyBzbyB0aGUgbmV4dCBsb29rdXAgd2lsbFxyXG4gICAgICAgICAgICAgICAgICAgIC8vIGJlIGZhc3Rlci4gVGhpcyBjYWNoaW5nIGlzIHNhZmUgYmVjYXVzZSB0aGUgdHJlZSBvZiBjb250ZXh0cyBhbmRcclxuICAgICAgICAgICAgICAgICAgICAvLyB0aGUgdmFsdWVzIG9mIHRoZSBzbG90cyBhcmUgbG9naWNhbGx5IGltbXV0YWJsZS5cclxuICAgICAgICAgICAgICAgICAgICBjdXJyZW50Q29udGV4dC5zbG90c1t0aGlzLmlkXSA9IHZhbHVlO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGN1cnJlbnRDb250ZXh0KSB7XHJcbiAgICAgICAgICAgIC8vIElmIGEgdmFsdWUgd2FzIG5vdCBmb3VuZCBmb3IgdGhpcyBTbG90LCBpdCdzIG5ldmVyIGdvaW5nIHRvIGJlIGZvdW5kXHJcbiAgICAgICAgICAgIC8vIG5vIG1hdHRlciBob3cgbWFueSB0aW1lcyB3ZSBsb29rIGl0IHVwLCBzbyB3ZSBtaWdodCBhcyB3ZWxsIGNhY2hlXHJcbiAgICAgICAgICAgIC8vIHRoZSBhYnNlbmNlIG9mIHRoZSB2YWx1ZSwgdG9vLlxyXG4gICAgICAgICAgICBjdXJyZW50Q29udGV4dC5zbG90c1t0aGlzLmlkXSA9IE1JU1NJTkdfVkFMVUU7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH07XHJcbiAgICBTbG90LnByb3RvdHlwZS5nZXRWYWx1ZSA9IGZ1bmN0aW9uICgpIHtcclxuICAgICAgICBpZiAodGhpcy5oYXNWYWx1ZSgpKSB7XHJcbiAgICAgICAgICAgIHJldHVybiBjdXJyZW50Q29udGV4dC5zbG90c1t0aGlzLmlkXTtcclxuICAgICAgICB9XHJcbiAgICB9O1xyXG4gICAgU2xvdC5wcm90b3R5cGUud2l0aFZhbHVlID0gZnVuY3Rpb24gKHZhbHVlLCBjYWxsYmFjaywgXHJcbiAgICAvLyBHaXZlbiB0aGUgcHJldmFsZW5jZSBvZiBhcnJvdyBmdW5jdGlvbnMsIHNwZWNpZnlpbmcgYXJndW1lbnRzIGlzIGxpa2VseVxyXG4gICAgLy8gdG8gYmUgbXVjaCBtb3JlIGNvbW1vbiB0aGFuIHNwZWNpZnlpbmcgYHRoaXNgLCBoZW5jZSB0aGlzIG9yZGVyaW5nOlxyXG4gICAgYXJncywgdGhpc0FyZykge1xyXG4gICAgICAgIHZhciBfYTtcclxuICAgICAgICB2YXIgc2xvdHMgPSAoX2EgPSB7XHJcbiAgICAgICAgICAgICAgICBfX3Byb3RvX186IG51bGxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgX2FbdGhpcy5pZF0gPSB2YWx1ZSxcclxuICAgICAgICAgICAgX2EpO1xyXG4gICAgICAgIHZhciBwYXJlbnQgPSBjdXJyZW50Q29udGV4dDtcclxuICAgICAgICBjdXJyZW50Q29udGV4dCA9IHsgcGFyZW50OiBwYXJlbnQsIHNsb3RzOiBzbG90cyB9O1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIC8vIEZ1bmN0aW9uLnByb3RvdHlwZS5hcHBseSBhbGxvd3MgdGhlIGFyZ3VtZW50cyBhcnJheSBhcmd1bWVudCB0byBiZVxyXG4gICAgICAgICAgICAvLyBvbWl0dGVkIG9yIHVuZGVmaW5lZCwgc28gYXJncyEgaXMgZmluZSBoZXJlLlxyXG4gICAgICAgICAgICByZXR1cm4gY2FsbGJhY2suYXBwbHkodGhpc0FyZywgYXJncyk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGZpbmFsbHkge1xyXG4gICAgICAgICAgICBjdXJyZW50Q29udGV4dCA9IHBhcmVudDtcclxuICAgICAgICB9XHJcbiAgICB9O1xyXG4gICAgLy8gQ2FwdHVyZSB0aGUgY3VycmVudCBjb250ZXh0IGFuZCB3cmFwIGEgY2FsbGJhY2sgZnVuY3Rpb24gc28gdGhhdCBpdFxyXG4gICAgLy8gcmVlc3RhYmxpc2hlcyB0aGUgY2FwdHVyZWQgY29udGV4dCB3aGVuIGNhbGxlZC5cclxuICAgIFNsb3QuYmluZCA9IGZ1bmN0aW9uIChjYWxsYmFjaykge1xyXG4gICAgICAgIHZhciBjb250ZXh0ID0gY3VycmVudENvbnRleHQ7XHJcbiAgICAgICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcclxuICAgICAgICAgICAgdmFyIHNhdmVkID0gY3VycmVudENvbnRleHQ7XHJcbiAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgICBjdXJyZW50Q29udGV4dCA9IGNvbnRleHQ7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gY2FsbGJhY2suYXBwbHkodGhpcywgYXJndW1lbnRzKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBmaW5hbGx5IHtcclxuICAgICAgICAgICAgICAgIGN1cnJlbnRDb250ZXh0ID0gc2F2ZWQ7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9O1xyXG4gICAgfTtcclxuICAgIC8vIEltbWVkaWF0ZWx5IHJ1biBhIGNhbGxiYWNrIGZ1bmN0aW9uIHdpdGhvdXQgYW55IGNhcHR1cmVkIGNvbnRleHQuXHJcbiAgICBTbG90Lm5vQ29udGV4dCA9IGZ1bmN0aW9uIChjYWxsYmFjaywgXHJcbiAgICAvLyBHaXZlbiB0aGUgcHJldmFsZW5jZSBvZiBhcnJvdyBmdW5jdGlvbnMsIHNwZWNpZnlpbmcgYXJndW1lbnRzIGlzIGxpa2VseVxyXG4gICAgLy8gdG8gYmUgbXVjaCBtb3JlIGNvbW1vbiB0aGFuIHNwZWNpZnlpbmcgYHRoaXNgLCBoZW5jZSB0aGlzIG9yZGVyaW5nOlxyXG4gICAgYXJncywgdGhpc0FyZykge1xyXG4gICAgICAgIGlmIChjdXJyZW50Q29udGV4dCkge1xyXG4gICAgICAgICAgICB2YXIgc2F2ZWQgPSBjdXJyZW50Q29udGV4dDtcclxuICAgICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgICAgIGN1cnJlbnRDb250ZXh0ID0gbnVsbDtcclxuICAgICAgICAgICAgICAgIC8vIEZ1bmN0aW9uLnByb3RvdHlwZS5hcHBseSBhbGxvd3MgdGhlIGFyZ3VtZW50cyBhcnJheSBhcmd1bWVudCB0byBiZVxyXG4gICAgICAgICAgICAgICAgLy8gb21pdHRlZCBvciB1bmRlZmluZWQsIHNvIGFyZ3MhIGlzIGZpbmUgaGVyZS5cclxuICAgICAgICAgICAgICAgIHJldHVybiBjYWxsYmFjay5hcHBseSh0aGlzQXJnLCBhcmdzKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBmaW5hbGx5IHtcclxuICAgICAgICAgICAgICAgIGN1cnJlbnRDb250ZXh0ID0gc2F2ZWQ7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICAgIHJldHVybiBjYWxsYmFjay5hcHBseSh0aGlzQXJnLCBhcmdzKTtcclxuICAgICAgICB9XHJcbiAgICB9O1xyXG4gICAgcmV0dXJuIFNsb3Q7XHJcbn0oKSk7IH07XHJcbmZ1bmN0aW9uIG1heWJlKGZuKSB7XHJcbiAgICB0cnkge1xyXG4gICAgICAgIHJldHVybiBmbigpO1xyXG4gICAgfVxyXG4gICAgY2F0Y2ggKGlnbm9yZWQpIHsgfVxyXG59XHJcbi8vIFdlIHN0b3JlIGEgc2luZ2xlIGdsb2JhbCBpbXBsZW1lbnRhdGlvbiBvZiB0aGUgU2xvdCBjbGFzcyBhcyBhIHBlcm1hbmVudFxyXG4vLyBub24tZW51bWVyYWJsZSBwcm9wZXJ0eSBvZiB0aGUgZ2xvYmFsVGhpcyBvYmplY3QuIFRoaXMgb2JmdXNjYXRpb24gZG9lc1xyXG4vLyBub3RoaW5nIHRvIHByZXZlbnQgYWNjZXNzIHRvIHRoZSBTbG90IGNsYXNzLCBidXQgYXQgbGVhc3QgaXQgZW5zdXJlcyB0aGVcclxuLy8gaW1wbGVtZW50YXRpb24gKGkuZS4gY3VycmVudENvbnRleHQpIGNhbm5vdCBiZSB0YW1wZXJlZCB3aXRoLCBhbmQgYWxsIGNvcGllc1xyXG4vLyBvZiB0aGUgQHdyeS9jb250ZXh0IHBhY2thZ2UgKGhvcGVmdWxseSBqdXN0IG9uZSkgd2lsbCBzaGFyZSB0aGUgc2FtZSBTbG90XHJcbi8vIGltcGxlbWVudGF0aW9uLiBTaW5jZSB0aGUgZmlyc3QgY29weSBvZiB0aGUgQHdyeS9jb250ZXh0IHBhY2thZ2UgdG8gYmVcclxuLy8gaW1wb3J0ZWQgd2lucywgdGhpcyB0ZWNobmlxdWUgaW1wb3NlcyBhIHN0ZWVwIGNvc3QgZm9yIGFueSBmdXR1cmUgYnJlYWtpbmdcclxuLy8gY2hhbmdlcyB0byB0aGUgU2xvdCBjbGFzcy5cclxudmFyIGdsb2JhbEtleSA9IFwiQHdyeS9jb250ZXh0OlNsb3RcIjtcclxudmFyIGhvc3QgPSBcclxuLy8gUHJlZmVyIGdsb2JhbFRoaXMgd2hlbiBhdmFpbGFibGUuXHJcbi8vIGh0dHBzOi8vZ2l0aHViLmNvbS9iZW5qYW1uL3dyeXdhcmUvaXNzdWVzLzM0N1xyXG5tYXliZShmdW5jdGlvbiAoKSB7IHJldHVybiBnbG9iYWxUaGlzOyB9KSB8fFxyXG4gICAgLy8gRmFsbCBiYWNrIHRvIGdsb2JhbCwgd2hpY2ggd29ya3MgaW4gTm9kZS5qcyBhbmQgbWF5IGJlIGNvbnZlcnRlZCBieSBzb21lXHJcbiAgICAvLyBidW5kbGVycyB0byB0aGUgYXBwcm9wcmlhdGUgaWRlbnRpZmllciAod2luZG93LCBzZWxmLCAuLi4pIGRlcGVuZGluZyBvbiB0aGVcclxuICAgIC8vIGJ1bmRsaW5nIHRhcmdldC4gaHR0cHM6Ly9naXRodWIuY29tL2VuZG9qcy9lbmRvL2lzc3Vlcy81NzYjaXNzdWVjb21tZW50LTExNzg1MTUyMjRcclxuICAgIG1heWJlKGZ1bmN0aW9uICgpIHsgcmV0dXJuIGdsb2JhbDsgfSkgfHxcclxuICAgIC8vIE90aGVyd2lzZSwgdXNlIGEgZHVtbXkgaG9zdCB0aGF0J3MgbG9jYWwgdG8gdGhpcyBtb2R1bGUuIFdlIHVzZWQgdG8gZmFsbFxyXG4gICAgLy8gYmFjayB0byB1c2luZyB0aGUgQXJyYXkgY29uc3RydWN0b3IgYXMgYSBuYW1lc3BhY2UsIGJ1dCB0aGF0IHdhcyBmbGFnZ2VkIGluXHJcbiAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vYmVuamFtbi93cnl3YXJlL2lzc3Vlcy8zNDcsIGFuZCBjYW4gYmUgYXZvaWRlZC5cclxuICAgIE9iamVjdC5jcmVhdGUobnVsbCk7XHJcbi8vIFdoaWNoZXZlciBnbG9iYWxIb3N0IHdlJ3JlIHVzaW5nLCBtYWtlIFR5cGVTY3JpcHQgaGFwcHkgYWJvdXQgdGhlIGFkZGl0aW9uYWxcclxuLy8gZ2xvYmFsS2V5IHByb3BlcnR5LlxyXG52YXIgZ2xvYmFsSG9zdCA9IGhvc3Q7XHJcbnZhciBTbG90ID0gZ2xvYmFsSG9zdFtnbG9iYWxLZXldIHx8XHJcbiAgICAvLyBFYXJsaWVyIHZlcnNpb25zIG9mIHRoaXMgcGFja2FnZSBzdG9yZWQgdGhlIGdsb2JhbEtleSBwcm9wZXJ0eSBvbiB0aGUgQXJyYXlcclxuICAgIC8vIGNvbnN0cnVjdG9yLCBzbyB3ZSBjaGVjayB0aGVyZSBhcyB3ZWxsLCB0byBwcmV2ZW50IFNsb3QgY2xhc3MgZHVwbGljYXRpb24uXHJcbiAgICBBcnJheVtnbG9iYWxLZXldIHx8XHJcbiAgICAoZnVuY3Rpb24gKFNsb3QpIHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoZ2xvYmFsSG9zdCwgZ2xvYmFsS2V5LCB7XHJcbiAgICAgICAgICAgICAgICB2YWx1ZTogU2xvdCxcclxuICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxyXG4gICAgICAgICAgICAgICAgd3JpdGFibGU6IGZhbHNlLFxyXG4gICAgICAgICAgICAgICAgLy8gV2hlbiBpdCB3YXMgcG9zc2libGUgZm9yIGdsb2JhbEhvc3QgdG8gYmUgdGhlIEFycmF5IGNvbnN0cnVjdG9yIChhXHJcbiAgICAgICAgICAgICAgICAvLyBsZWdhY3kgU2xvdCBkZWR1cCBzdHJhdGVneSksIGl0IHdhcyBpbXBvcnRhbnQgZm9yIHRoZSBwcm9wZXJ0eSB0byBiZVxyXG4gICAgICAgICAgICAgICAgLy8gY29uZmlndXJhYmxlOnRydWUgc28gaXQgY291bGQgYmUgZGVsZXRlZC4gVGhhdCBkb2VzIG5vdCBzZWVtIHRvIGJlIGFzXHJcbiAgICAgICAgICAgICAgICAvLyBpbXBvcnRhbnQgd2hlbiBnbG9iYWxIb3N0IGlzIHRoZSBnbG9iYWwgb2JqZWN0LCBidXQgSSBkb24ndCB3YW50IHRvXHJcbiAgICAgICAgICAgICAgICAvLyBjYXVzZSBzaW1pbGFyIHByb2JsZW1zIGFnYWluLCBhbmQgY29uZmlndXJhYmxlOnRydWUgc2VlbXMgc2FmZXN0LlxyXG4gICAgICAgICAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2VuZG9qcy9lbmRvL2lzc3Vlcy81NzYjaXNzdWVjb21tZW50LTExNzgyNzQwMDhcclxuICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICB9XHJcbiAgICAgICAgZmluYWxseSB7XHJcbiAgICAgICAgICAgIHJldHVybiBTbG90O1xyXG4gICAgICAgIH1cclxuICAgIH0pKG1ha2VTbG90Q2xhc3MoKSk7XG5cbnZhciBiaW5kID0gU2xvdC5iaW5kLCBub0NvbnRleHQgPSBTbG90Lm5vQ29udGV4dDtcclxuZnVuY3Rpb24gc2V0VGltZW91dFdpdGhDb250ZXh0KGNhbGxiYWNrLCBkZWxheSkge1xyXG4gICAgcmV0dXJuIHNldFRpbWVvdXQoYmluZChjYWxsYmFjayksIGRlbGF5KTtcclxufVxyXG4vLyBUdXJuIGFueSBnZW5lcmF0b3IgZnVuY3Rpb24gaW50byBhbiBhc3luYyBmdW5jdGlvbiAodXNpbmcgeWllbGQgaW5zdGVhZFxyXG4vLyBvZiBhd2FpdCksIHdpdGggY29udGV4dCBhdXRvbWF0aWNhbGx5IHByZXNlcnZlZCBhY3Jvc3MgeWllbGRzLlxyXG5mdW5jdGlvbiBhc3luY0Zyb21HZW4oZ2VuRm4pIHtcclxuICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XHJcbiAgICAgICAgdmFyIGdlbiA9IGdlbkZuLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XHJcbiAgICAgICAgdmFyIGJvdW5kTmV4dCA9IGJpbmQoZ2VuLm5leHQpO1xyXG4gICAgICAgIHZhciBib3VuZFRocm93ID0gYmluZChnZW4udGhyb3cpO1xyXG4gICAgICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7XHJcbiAgICAgICAgICAgIGZ1bmN0aW9uIGludm9rZShtZXRob2QsIGFyZ3VtZW50KSB7XHJcbiAgICAgICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgICAgICAgIHZhciByZXN1bHQgPSBtZXRob2QuY2FsbChnZW4sIGFyZ3VtZW50KTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiByZWplY3QoZXJyb3IpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgdmFyIG5leHQgPSByZXN1bHQuZG9uZSA/IHJlc29sdmUgOiBpbnZva2VOZXh0O1xyXG4gICAgICAgICAgICAgICAgaWYgKGlzUHJvbWlzZUxpa2UocmVzdWx0LnZhbHVlKSkge1xyXG4gICAgICAgICAgICAgICAgICAgIHJlc3VsdC52YWx1ZS50aGVuKG5leHQsIHJlc3VsdC5kb25lID8gcmVqZWN0IDogaW52b2tlVGhyb3cpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgbmV4dChyZXN1bHQudmFsdWUpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHZhciBpbnZva2VOZXh0ID0gZnVuY3Rpb24gKHZhbHVlKSB7IHJldHVybiBpbnZva2UoYm91bmROZXh0LCB2YWx1ZSk7IH07XHJcbiAgICAgICAgICAgIHZhciBpbnZva2VUaHJvdyA9IGZ1bmN0aW9uIChlcnJvcikgeyByZXR1cm4gaW52b2tlKGJvdW5kVGhyb3csIGVycm9yKTsgfTtcclxuICAgICAgICAgICAgaW52b2tlTmV4dCgpO1xyXG4gICAgICAgIH0pO1xyXG4gICAgfTtcclxufVxyXG5mdW5jdGlvbiBpc1Byb21pc2VMaWtlKHZhbHVlKSB7XHJcbiAgICByZXR1cm4gdmFsdWUgJiYgdHlwZW9mIHZhbHVlLnRoZW4gPT09IFwiZnVuY3Rpb25cIjtcclxufVxyXG4vLyBJZiB5b3UgdXNlIHRoZSBmaWJlcnMgbnBtIHBhY2thZ2UgdG8gaW1wbGVtZW50IGNvcm91dGluZXMgaW4gTm9kZS5qcyxcclxuLy8geW91IHNob3VsZCBjYWxsIHRoaXMgZnVuY3Rpb24gYXQgbGVhc3Qgb25jZSB0byBlbnN1cmUgY29udGV4dCBtYW5hZ2VtZW50XHJcbi8vIHJlbWFpbnMgY29oZXJlbnQgYWNyb3NzIGFueSB5aWVsZHMuXHJcbnZhciB3cmFwcGVkRmliZXJzID0gW107XHJcbmZ1bmN0aW9uIHdyYXBZaWVsZGluZ0ZpYmVyTWV0aG9kcyhGaWJlcikge1xyXG4gICAgLy8gVGhlcmUgY2FuIGJlIG9ubHkgb25lIGltcGxlbWVudGF0aW9uIG9mIEZpYmVyIHBlciBwcm9jZXNzLCBzbyB0aGlzIGFycmF5XHJcbiAgICAvLyBzaG91bGQgbmV2ZXIgZ3JvdyBsb25nZXIgdGhhbiBvbmUgZWxlbWVudC5cclxuICAgIGlmICh3cmFwcGVkRmliZXJzLmluZGV4T2YoRmliZXIpIDwgMCkge1xyXG4gICAgICAgIHZhciB3cmFwID0gZnVuY3Rpb24gKG9iaiwgbWV0aG9kKSB7XHJcbiAgICAgICAgICAgIHZhciBmbiA9IG9ialttZXRob2RdO1xyXG4gICAgICAgICAgICBvYmpbbWV0aG9kXSA9IGZ1bmN0aW9uICgpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBub0NvbnRleHQoZm4sIGFyZ3VtZW50cywgdGhpcyk7XHJcbiAgICAgICAgICAgIH07XHJcbiAgICAgICAgfTtcclxuICAgICAgICAvLyBUaGVzZSBtZXRob2RzIGNhbiB5aWVsZCwgYWNjb3JkaW5nIHRvXHJcbiAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2xhdmVyZGV0L25vZGUtZmliZXJzL2Jsb2IvZGRlYmVkOWI4YWUzODgzZTU3ZjgyMmUyMTA4ZTY5NDNlNWM4ZDJhOC9maWJlcnMuanMjTDk3LUwxMDBcclxuICAgICAgICB3cmFwKEZpYmVyLCBcInlpZWxkXCIpO1xyXG4gICAgICAgIHdyYXAoRmliZXIucHJvdG90eXBlLCBcInJ1blwiKTtcclxuICAgICAgICB3cmFwKEZpYmVyLnByb3RvdHlwZSwgXCJ0aHJvd0ludG9cIik7XHJcbiAgICAgICAgd3JhcHBlZEZpYmVycy5wdXNoKEZpYmVyKTtcclxuICAgIH1cclxuICAgIHJldHVybiBGaWJlcjtcclxufVxuXG5leHBvcnRzLlNsb3QgPSBTbG90O1xuZXhwb3J0cy5hc3luY0Zyb21HZW4gPSBhc3luY0Zyb21HZW47XG5leHBvcnRzLmJpbmQgPSBiaW5kO1xuZXhwb3J0cy5ub0NvbnRleHQgPSBub0NvbnRleHQ7XG5leHBvcnRzLnNldFRpbWVvdXQgPSBzZXRUaW1lb3V0V2l0aENvbnRleHQ7XG5leHBvcnRzLndyYXBZaWVsZGluZ0ZpYmVyTWV0aG9kcyA9IHdyYXBZaWVsZGluZ0ZpYmVyTWV0aG9kcztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnRleHQuanMubWFwXG4iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJjdXJyZW50Q29udGV4dCIsIk1JU1NJTkdfVkFMVUUiLCJpZENvdW50ZXIiLCJtYWtlU2xvdENsYXNzIiwiU2xvdCIsImlkIiwiRGF0ZSIsIm5vdyIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInNsaWNlIiwiam9pbiIsInByb3RvdHlwZSIsImhhc1ZhbHVlIiwiY29udGV4dF8xIiwicGFyZW50Iiwic2xvdHMiLCJnZXRWYWx1ZSIsIndpdGhWYWx1ZSIsImNhbGxiYWNrIiwiYXJncyIsInRoaXNBcmciLCJfYSIsIl9fcHJvdG9fXyIsImFwcGx5IiwiYmluZCIsImNvbnRleHQiLCJzYXZlZCIsImFyZ3VtZW50cyIsIm5vQ29udGV4dCIsIm1heWJlIiwiZm4iLCJpZ25vcmVkIiwiZ2xvYmFsS2V5IiwiaG9zdCIsImdsb2JhbFRoaXMiLCJnbG9iYWwiLCJjcmVhdGUiLCJnbG9iYWxIb3N0IiwiQXJyYXkiLCJlbnVtZXJhYmxlIiwid3JpdGFibGUiLCJjb25maWd1cmFibGUiLCJzZXRUaW1lb3V0V2l0aENvbnRleHQiLCJkZWxheSIsInNldFRpbWVvdXQiLCJhc3luY0Zyb21HZW4iLCJnZW5GbiIsImdlbiIsImJvdW5kTmV4dCIsIm5leHQiLCJib3VuZFRocm93IiwidGhyb3ciLCJQcm9taXNlIiwicmVzb2x2ZSIsInJlamVjdCIsImludm9rZSIsIm1ldGhvZCIsImFyZ3VtZW50IiwicmVzdWx0IiwiY2FsbCIsImVycm9yIiwiZG9uZSIsImludm9rZU5leHQiLCJpc1Byb21pc2VMaWtlIiwidGhlbiIsImludm9rZVRocm93Iiwid3JhcHBlZEZpYmVycyIsIndyYXBZaWVsZGluZ0ZpYmVyTWV0aG9kcyIsIkZpYmVyIiwiaW5kZXhPZiIsIndyYXAiLCJvYmoiLCJwdXNoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@wry/context/lib/context.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@wry/equality/lib/equality.js":
/*!****************************************************!*\
  !*** ./node_modules/@wry/equality/lib/equality.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _a = Object.prototype, toString = _a.toString, hasOwnProperty = _a.hasOwnProperty;\nvar fnToStr = Function.prototype.toString;\nvar previousComparisons = new Map();\n/**\r\n * Performs a deep equality check on two JavaScript values, tolerating cycles.\r\n */ function equal(a, b) {\n    try {\n        return check(a, b);\n    } finally{\n        previousComparisons.clear();\n    }\n}\nfunction check(a, b) {\n    // If the two values are strictly equal, our job is easy.\n    if (a === b) {\n        return true;\n    }\n    // Object.prototype.toString returns a representation of the runtime type of\n    // the given value that is considerably more precise than typeof.\n    var aTag = toString.call(a);\n    var bTag = toString.call(b);\n    // If the runtime types of a and b are different, they could maybe be equal\n    // under some interpretation of equality, but for simplicity and performance\n    // we just return false instead.\n    if (aTag !== bTag) {\n        return false;\n    }\n    switch(aTag){\n        case \"[object Array]\":\n            // Arrays are a lot like other objects, but we can cheaply compare their\n            // lengths as a short-cut before comparing their elements.\n            if (a.length !== b.length) return false;\n        // Fall through to object case...\n        case \"[object Object]\":\n            {\n                if (previouslyCompared(a, b)) return true;\n                var aKeys = definedKeys(a);\n                var bKeys = definedKeys(b);\n                // If `a` and `b` have a different number of enumerable keys, they\n                // must be different.\n                var keyCount = aKeys.length;\n                if (keyCount !== bKeys.length) return false;\n                // Now make sure they have the same keys.\n                for(var k = 0; k < keyCount; ++k){\n                    if (!hasOwnProperty.call(b, aKeys[k])) {\n                        return false;\n                    }\n                }\n                // Finally, check deep equality of all child properties.\n                for(var k = 0; k < keyCount; ++k){\n                    var key = aKeys[k];\n                    if (!check(a[key], b[key])) {\n                        return false;\n                    }\n                }\n                return true;\n            }\n        case \"[object Error]\":\n            return a.name === b.name && a.message === b.message;\n        case \"[object Number]\":\n            // Handle NaN, which is !== itself.\n            if (a !== a) return b !== b;\n        // Fall through to shared +a === +b case...\n        case \"[object Boolean]\":\n        case \"[object Date]\":\n            return +a === +b;\n        case \"[object RegExp]\":\n        case \"[object String]\":\n            return a == \"\".concat(b);\n        case \"[object Map]\":\n        case \"[object Set]\":\n            {\n                if (a.size !== b.size) return false;\n                if (previouslyCompared(a, b)) return true;\n                var aIterator = a.entries();\n                var isMap = aTag === \"[object Map]\";\n                while(true){\n                    var info = aIterator.next();\n                    if (info.done) break;\n                    // If a instanceof Set, aValue === aKey.\n                    var _a = info.value, aKey = _a[0], aValue = _a[1];\n                    // So this works the same way for both Set and Map.\n                    if (!b.has(aKey)) {\n                        return false;\n                    }\n                    // However, we care about deep equality of values only when dealing\n                    // with Map structures.\n                    if (isMap && !check(aValue, b.get(aKey))) {\n                        return false;\n                    }\n                }\n                return true;\n            }\n        case \"[object Uint16Array]\":\n        case \"[object Uint8Array]\":\n        case \"[object Uint32Array]\":\n        case \"[object Int32Array]\":\n        case \"[object Int8Array]\":\n        case \"[object Int16Array]\":\n        case \"[object ArrayBuffer]\":\n            // DataView doesn't need these conversions, but the equality check is\n            // otherwise the same.\n            a = new Uint8Array(a);\n            b = new Uint8Array(b);\n        // Fall through...\n        case \"[object DataView]\":\n            {\n                var len = a.byteLength;\n                if (len === b.byteLength) {\n                    while(len-- && a[len] === b[len]){\n                    // Keep looping as long as the bytes are equal.\n                    }\n                }\n                return len === -1;\n            }\n        case \"[object AsyncFunction]\":\n        case \"[object GeneratorFunction]\":\n        case \"[object AsyncGeneratorFunction]\":\n        case \"[object Function]\":\n            {\n                var aCode = fnToStr.call(a);\n                if (aCode !== fnToStr.call(b)) {\n                    return false;\n                }\n                // We consider non-native functions equal if they have the same code\n                // (native functions require === because their code is censored).\n                // Note that this behavior is not entirely sound, since !== function\n                // objects with the same code can behave differently depending on\n                // their closure scope. However, any function can behave differently\n                // depending on the values of its input arguments (including this)\n                // and its calling context (including its closure scope), even\n                // though the function object is === to itself; and it is entirely\n                // possible for functions that are not === to behave exactly the\n                // same under all conceivable circumstances. Because none of these\n                // factors are statically decidable in JavaScript, JS function\n                // equality is not well-defined. This ambiguity allows us to\n                // consider the best possible heuristic among various imperfect\n                // options, and equating non-native functions that have the same\n                // code has enormous practical benefits, such as when comparing\n                // functions that are repeatedly passed as fresh function\n                // expressions within objects that are otherwise deeply equal. Since\n                // any function created from the same syntactic expression (in the\n                // same code location) will always stringify to the same code\n                // according to fnToStr.call, we can reasonably expect these\n                // repeatedly passed function expressions to have the same code, and\n                // thus behave \"the same\" (with all the caveats mentioned above),\n                // even though the runtime function objects are !== to one another.\n                return !endsWith(aCode, nativeCodeSuffix);\n            }\n    }\n    // Otherwise the values are not equal.\n    return false;\n}\nfunction definedKeys(obj) {\n    // Remember that the second argument to Array.prototype.filter will be\n    // used as `this` within the callback function.\n    return Object.keys(obj).filter(isDefinedKey, obj);\n}\nfunction isDefinedKey(key) {\n    return this[key] !== void 0;\n}\nvar nativeCodeSuffix = \"{ [native code] }\";\nfunction endsWith(full, suffix) {\n    var fromIndex = full.length - suffix.length;\n    return fromIndex >= 0 && full.indexOf(suffix, fromIndex) === fromIndex;\n}\nfunction previouslyCompared(a, b) {\n    // Though cyclic references can make an object graph appear infinite from the\n    // perspective of a depth-first traversal, the graph still contains a finite\n    // number of distinct object references. We use the previousComparisons cache\n    // to avoid comparing the same pair of object references more than once, which\n    // guarantees termination (even if we end up comparing every object in one\n    // graph to every object in the other graph, which is extremely unlikely),\n    // while still allowing weird isomorphic structures (like rings with different\n    // lengths) a chance to pass the equality test.\n    var bSet = previousComparisons.get(a);\n    if (bSet) {\n        // Return true here because we can be sure false will be returned somewhere\n        // else if the objects are not equivalent.\n        if (bSet.has(b)) return true;\n    } else {\n        previousComparisons.set(a, bSet = new Set);\n    }\n    bSet.add(b);\n    return false;\n}\nexports[\"default\"] = equal;\nexports.equal = equal; //# sourceMappingURL=equality.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@wry/equality/lib/equality.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@wry/trie/lib/trie.js":
/*!********************************************!*\
  !*** ./node_modules/@wry/trie/lib/trie.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n// A [trie](https://en.wikipedia.org/wiki/Trie) data structure that holds\n// object keys weakly, yet can also hold non-object keys, unlike the\n// native `WeakMap`.\n// If no makeData function is supplied, the looked-up data will be an empty,\n// null-prototype Object.\nvar defaultMakeData = function() {\n    return Object.create(null);\n};\n// Useful for processing arguments objects as well as arrays.\nvar _a = Array.prototype, forEach = _a.forEach, slice = _a.slice;\nvar Trie = /** @class */ function() {\n    function Trie(weakness, makeData) {\n        if (weakness === void 0) {\n            weakness = true;\n        }\n        if (makeData === void 0) {\n            makeData = defaultMakeData;\n        }\n        this.weakness = weakness;\n        this.makeData = makeData;\n    }\n    Trie.prototype.lookup = function() {\n        var array = [];\n        for(var _i = 0; _i < arguments.length; _i++){\n            array[_i] = arguments[_i];\n        }\n        return this.lookupArray(array);\n    };\n    Trie.prototype.lookupArray = function(array) {\n        var node = this;\n        forEach.call(array, function(key) {\n            return node = node.getChildTrie(key);\n        });\n        return node.data || (node.data = this.makeData(slice.call(array)));\n    };\n    Trie.prototype.getChildTrie = function(key) {\n        var map = this.weakness && isObjRef(key) ? this.weak || (this.weak = new WeakMap()) : this.strong || (this.strong = new Map());\n        var child = map.get(key);\n        if (!child) map.set(key, child = new Trie(this.weakness, this.makeData));\n        return child;\n    };\n    return Trie;\n}();\nfunction isObjRef(value) {\n    switch(typeof value){\n        case \"object\":\n            if (value === null) break;\n        // Fall through to return true...\n        case \"function\":\n            return true;\n    }\n    return false;\n}\nexports.Trie = Trie; //# sourceMappingURL=trie.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@wry/trie/lib/trie.js\n");

/***/ })

};
;