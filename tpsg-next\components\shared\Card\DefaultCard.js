import styled from "styled-components";
import { device } from "styles/device";
import Image from "next/image";
import Link from "next/link";
import { dateForHumans } from "utils/date.utils";
import { getPostRoute } from "utils/posts.utils";
import RoundedLabel from "components/shared/atoms/rounded-label";
import CondImage from "components/shared/condimage";
import { AnimatedIcon } from "../atoms";
import { getDotColor } from "utils/color";
import { withRealSrc } from "../../../utils/image-utils";
import { removeHtml, removeMarkdown } from "../../../utils/string.utils";


/**
 *
 * @param post {object}
 * @param {object} options
 * @param {boolean} options.showLead
 * @param {boolean} options.showTopics
 * @param {boolean} options.showDate
 * @return {JSX.Element}
 * @constructor
 */
export default function DefaultPostCard({ post, options, showAnimatedIcon=false }) {

  let route = getPostRoute(post);
  if (!route) return <></>
  let author = undefined;
  if (post.author) {
    author = post.author.fullName ? post.author.fullName : post.author
  }
  const date = !post.date ? dateForHumans(post.published_at) : dateForHumans(post.date);
  const imgSrc = withRealSrc(post.image);

  return (
    <Link href={route}>
      <Wrapper>
        <div className={"dc-image-container"}>
          { imgSrc &&
              <Image
                src={imgSrc}
                fill
                style={{ objectFit: "cover" }}
                sizes={"240px"}
                alt={post.image?.alternativeText || ""}
              />
          }
          {showAnimatedIcon && (
            <AnimatedIcon type={post.type}  colors={getDotColor(post.type)}/>
          )}
        </div>

        <div className={"dc-text-container"}>
          <p className={"dc-author"}>{author}</p>
          <p className={"dc-title primary-hover"}>{post.title}</p>
          {/* <div className={"dc-topics mobile-hide"}>
            {options?.showTopics &&
                     post.topics &&
                     post.topics.slice(0, 2).map((topic, key) => <RoundedLabel key={key} text={topic.name || ""}/> )}
          </div> */}
          { options.showLead &&
            <p className={"dc-lead"}>{removeHtml(removeMarkdown(post.lead?.content || ""))}</p>
          }
        </div>
      </Wrapper>
    </Link>
  );
}

const Wrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: row-reverse;
  margin-bottom: 32px;
  padding-bottom: 32px;
  border-bottom: 1px solid #dcdcdc;

  .dc-text-container {
    width: 66%;
    margin-top: 0;
    .dc-author {
      font-family: "Lora", serif;
      font-style: italic;
      font-size: 14px;
      color: #888888;
      margin-top: 0;
      margin-bottom: 0;
    }

    .dc-date {
      font-size: 14px;
      color: #888888;
    }

    .dc-title {
      margin-top: 8px;
      margin-bottom: 0;
      margin-right: 16px;
      font-size: 22px;
      line-height: 120%;
      color: #161616;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .dc-image-container {
    position: relative;
    flex-shrink: 0;
    width: 28vw;
    //height: 28vw;
    aspect-ratio: 1/1;
    //&:after {
    //  content: "";
    //  display: block;
    //  padding-bottom: 100%;
    //}
  }

  @media ${device.tablet} {
    flex-direction: row;
    border-bottom: 1px solid #ffffff;
    cursor: pointer;
    .dc-text-container {
      //flex-grow: 1;
      .dc-title {
        font-size: 32px;
        margin-top: 12px;
      }
      .dc-author {
        font-size: 16px;
      }
    }
    .dc-image-container {
      margin-right: 32px;
      width: 30%;
      height: 100%;
      aspect-ratio: 1/1;
    }
    .dc-topics {
      margin-top: 16px;
      .card-label {
        margin-right: 8px;
      }
    }

  }
`;
