/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sax";
exports.ids = ["vendor-chunks/sax"];
exports.modules = {

/***/ "(rsc)/./node_modules/sax/lib/sax.js":
/*!*************************************!*\
  !*** ./node_modules/sax/lib/sax.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(";\n(function(sax) {\n    sax.parser = function(strict, opt) {\n        return new SAXParser(strict, opt);\n    };\n    sax.SAXParser = SAXParser;\n    sax.SAXStream = SAXStream;\n    sax.createStream = createStream;\n    // When we pass the MAX_BUFFER_LENGTH position, start checking for buffer overruns.\n    // When we check, schedule the next check for MAX_BUFFER_LENGTH - (max(buffer lengths)),\n    // since that's the earliest that a buffer overrun could occur.  This way, checks are\n    // as rare as required, but as often as necessary to ensure never crossing this bound.\n    // Furthermore, buffers are only tested at most once per write(), so passing a very\n    // large string into write() might have undesirable effects, but this is manageable by\n    // the caller, so it is assumed to be safe.  Thus, a call to write() may, in the extreme\n    // edge case, result in creating at most one complete copy of the string passed in.\n    // Set to Infinity to have unlimited buffers.\n    sax.MAX_BUFFER_LENGTH = 64 * 1024;\n    var buffers = [\n        \"comment\",\n        \"sgmlDecl\",\n        \"textNode\",\n        \"tagName\",\n        \"doctype\",\n        \"procInstName\",\n        \"procInstBody\",\n        \"entity\",\n        \"attribName\",\n        \"attribValue\",\n        \"cdata\",\n        \"script\"\n    ];\n    sax.EVENTS = [\n        \"text\",\n        \"processinginstruction\",\n        \"sgmldeclaration\",\n        \"doctype\",\n        \"comment\",\n        \"opentagstart\",\n        \"attribute\",\n        \"opentag\",\n        \"closetag\",\n        \"opencdata\",\n        \"cdata\",\n        \"closecdata\",\n        \"error\",\n        \"end\",\n        \"ready\",\n        \"script\",\n        \"opennamespace\",\n        \"closenamespace\"\n    ];\n    function SAXParser(strict, opt) {\n        if (!(this instanceof SAXParser)) {\n            return new SAXParser(strict, opt);\n        }\n        var parser = this;\n        clearBuffers(parser);\n        parser.q = parser.c = \"\";\n        parser.bufferCheckPosition = sax.MAX_BUFFER_LENGTH;\n        parser.opt = opt || {};\n        parser.opt.lowercase = parser.opt.lowercase || parser.opt.lowercasetags;\n        parser.looseCase = parser.opt.lowercase ? \"toLowerCase\" : \"toUpperCase\";\n        parser.tags = [];\n        parser.closed = parser.closedRoot = parser.sawRoot = false;\n        parser.tag = parser.error = null;\n        parser.strict = !!strict;\n        parser.noscript = !!(strict || parser.opt.noscript);\n        parser.state = S.BEGIN;\n        parser.strictEntities = parser.opt.strictEntities;\n        parser.ENTITIES = parser.strictEntities ? Object.create(sax.XML_ENTITIES) : Object.create(sax.ENTITIES);\n        parser.attribList = [];\n        // namespaces form a prototype chain.\n        // it always points at the current tag,\n        // which protos to its parent tag.\n        if (parser.opt.xmlns) {\n            parser.ns = Object.create(rootNS);\n        }\n        // mostly just for error reporting\n        parser.trackPosition = parser.opt.position !== false;\n        if (parser.trackPosition) {\n            parser.position = parser.line = parser.column = 0;\n        }\n        emit(parser, \"onready\");\n    }\n    if (!Object.create) {\n        Object.create = function(o) {\n            function F() {}\n            F.prototype = o;\n            var newf = new F();\n            return newf;\n        };\n    }\n    if (!Object.keys) {\n        Object.keys = function(o) {\n            var a = [];\n            for(var i in o)if (o.hasOwnProperty(i)) a.push(i);\n            return a;\n        };\n    }\n    function checkBufferLength(parser) {\n        var maxAllowed = Math.max(sax.MAX_BUFFER_LENGTH, 10);\n        var maxActual = 0;\n        for(var i = 0, l = buffers.length; i < l; i++){\n            var len = parser[buffers[i]].length;\n            if (len > maxAllowed) {\n                // Text/cdata nodes can get big, and since they're buffered,\n                // we can get here under normal conditions.\n                // Avoid issues by emitting the text node now,\n                // so at least it won't get any bigger.\n                switch(buffers[i]){\n                    case \"textNode\":\n                        closeText(parser);\n                        break;\n                    case \"cdata\":\n                        emitNode(parser, \"oncdata\", parser.cdata);\n                        parser.cdata = \"\";\n                        break;\n                    case \"script\":\n                        emitNode(parser, \"onscript\", parser.script);\n                        parser.script = \"\";\n                        break;\n                    default:\n                        error(parser, \"Max buffer length exceeded: \" + buffers[i]);\n                }\n            }\n            maxActual = Math.max(maxActual, len);\n        }\n        // schedule the next check for the earliest possible buffer overrun.\n        var m = sax.MAX_BUFFER_LENGTH - maxActual;\n        parser.bufferCheckPosition = m + parser.position;\n    }\n    function clearBuffers(parser) {\n        for(var i = 0, l = buffers.length; i < l; i++){\n            parser[buffers[i]] = \"\";\n        }\n    }\n    function flushBuffers(parser) {\n        closeText(parser);\n        if (parser.cdata !== \"\") {\n            emitNode(parser, \"oncdata\", parser.cdata);\n            parser.cdata = \"\";\n        }\n        if (parser.script !== \"\") {\n            emitNode(parser, \"onscript\", parser.script);\n            parser.script = \"\";\n        }\n    }\n    SAXParser.prototype = {\n        end: function() {\n            end(this);\n        },\n        write: write,\n        resume: function() {\n            this.error = null;\n            return this;\n        },\n        close: function() {\n            return this.write(null);\n        },\n        flush: function() {\n            flushBuffers(this);\n        }\n    };\n    var Stream;\n    try {\n        Stream = (__webpack_require__(/*! stream */ \"stream\").Stream);\n    } catch (ex) {\n        Stream = function() {};\n    }\n    var streamWraps = sax.EVENTS.filter(function(ev) {\n        return ev !== \"error\" && ev !== \"end\";\n    });\n    function createStream(strict, opt) {\n        return new SAXStream(strict, opt);\n    }\n    function SAXStream(strict, opt) {\n        if (!(this instanceof SAXStream)) {\n            return new SAXStream(strict, opt);\n        }\n        Stream.apply(this);\n        this._parser = new SAXParser(strict, opt);\n        this.writable = true;\n        this.readable = true;\n        var me = this;\n        this._parser.onend = function() {\n            me.emit(\"end\");\n        };\n        this._parser.onerror = function(er) {\n            me.emit(\"error\", er);\n            // if didn't throw, then means error was handled.\n            // go ahead and clear error, so we can write again.\n            me._parser.error = null;\n        };\n        this._decoder = null;\n        streamWraps.forEach(function(ev) {\n            Object.defineProperty(me, \"on\" + ev, {\n                get: function() {\n                    return me._parser[\"on\" + ev];\n                },\n                set: function(h) {\n                    if (!h) {\n                        me.removeAllListeners(ev);\n                        me._parser[\"on\" + ev] = h;\n                        return h;\n                    }\n                    me.on(ev, h);\n                },\n                enumerable: true,\n                configurable: false\n            });\n        });\n    }\n    SAXStream.prototype = Object.create(Stream.prototype, {\n        constructor: {\n            value: SAXStream\n        }\n    });\n    SAXStream.prototype.write = function(data) {\n        if (typeof Buffer === \"function\" && typeof Buffer.isBuffer === \"function\" && Buffer.isBuffer(data)) {\n            if (!this._decoder) {\n                var SD = (__webpack_require__(/*! string_decoder */ \"string_decoder\").StringDecoder);\n                this._decoder = new SD(\"utf8\");\n            }\n            data = this._decoder.write(data);\n        }\n        this._parser.write(data.toString());\n        this.emit(\"data\", data);\n        return true;\n    };\n    SAXStream.prototype.end = function(chunk) {\n        if (chunk && chunk.length) {\n            this.write(chunk);\n        }\n        this._parser.end();\n        return true;\n    };\n    SAXStream.prototype.on = function(ev, handler) {\n        var me = this;\n        if (!me._parser[\"on\" + ev] && streamWraps.indexOf(ev) !== -1) {\n            me._parser[\"on\" + ev] = function() {\n                var args = arguments.length === 1 ? [\n                    arguments[0]\n                ] : Array.apply(null, arguments);\n                args.splice(0, 0, ev);\n                me.emit.apply(me, args);\n            };\n        }\n        return Stream.prototype.on.call(me, ev, handler);\n    };\n    // this really needs to be replaced with character classes.\n    // XML allows all manner of ridiculous numbers and digits.\n    var CDATA = \"[CDATA[\";\n    var DOCTYPE = \"DOCTYPE\";\n    var XML_NAMESPACE = \"http://www.w3.org/XML/1998/namespace\";\n    var XMLNS_NAMESPACE = \"http://www.w3.org/2000/xmlns/\";\n    var rootNS = {\n        xml: XML_NAMESPACE,\n        xmlns: XMLNS_NAMESPACE\n    };\n    // http://www.w3.org/TR/REC-xml/#NT-NameStartChar\n    // This implementation works on strings, a single character at a time\n    // as such, it cannot ever support astral-plane characters (10000-EFFFF)\n    // without a significant breaking change to either this  parser, or the\n    // JavaScript language.  Implementation of an emoji-capable xml parser\n    // is left as an exercise for the reader.\n    var nameStart = /[:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/;\n    var nameBody = /[:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\u00B7\\u0300-\\u036F\\u203F-\\u2040.\\d-]/;\n    var entityStart = /[#:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/;\n    var entityBody = /[#:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\u00B7\\u0300-\\u036F\\u203F-\\u2040.\\d-]/;\n    function isWhitespace(c) {\n        return c === \" \" || c === \"\\n\" || c === \"\\r\" || c === \"\t\";\n    }\n    function isQuote(c) {\n        return c === '\"' || c === \"'\";\n    }\n    function isAttribEnd(c) {\n        return c === \">\" || isWhitespace(c);\n    }\n    function isMatch(regex, c) {\n        return regex.test(c);\n    }\n    function notMatch(regex, c) {\n        return !isMatch(regex, c);\n    }\n    var S = 0;\n    sax.STATE = {\n        BEGIN: S++,\n        BEGIN_WHITESPACE: S++,\n        TEXT: S++,\n        TEXT_ENTITY: S++,\n        OPEN_WAKA: S++,\n        SGML_DECL: S++,\n        SGML_DECL_QUOTED: S++,\n        DOCTYPE: S++,\n        DOCTYPE_QUOTED: S++,\n        DOCTYPE_DTD: S++,\n        DOCTYPE_DTD_QUOTED: S++,\n        COMMENT_STARTING: S++,\n        COMMENT: S++,\n        COMMENT_ENDING: S++,\n        COMMENT_ENDED: S++,\n        CDATA: S++,\n        CDATA_ENDING: S++,\n        CDATA_ENDING_2: S++,\n        PROC_INST: S++,\n        PROC_INST_BODY: S++,\n        PROC_INST_ENDING: S++,\n        OPEN_TAG: S++,\n        OPEN_TAG_SLASH: S++,\n        ATTRIB: S++,\n        ATTRIB_NAME: S++,\n        ATTRIB_NAME_SAW_WHITE: S++,\n        ATTRIB_VALUE: S++,\n        ATTRIB_VALUE_QUOTED: S++,\n        ATTRIB_VALUE_CLOSED: S++,\n        ATTRIB_VALUE_UNQUOTED: S++,\n        ATTRIB_VALUE_ENTITY_Q: S++,\n        ATTRIB_VALUE_ENTITY_U: S++,\n        CLOSE_TAG: S++,\n        CLOSE_TAG_SAW_WHITE: S++,\n        SCRIPT: S++,\n        SCRIPT_ENDING: S++ // <script> ... <\n    };\n    sax.XML_ENTITIES = {\n        \"amp\": \"&\",\n        \"gt\": \">\",\n        \"lt\": \"<\",\n        \"quot\": '\"',\n        \"apos\": \"'\"\n    };\n    sax.ENTITIES = {\n        \"amp\": \"&\",\n        \"gt\": \">\",\n        \"lt\": \"<\",\n        \"quot\": '\"',\n        \"apos\": \"'\",\n        \"AElig\": 198,\n        \"Aacute\": 193,\n        \"Acirc\": 194,\n        \"Agrave\": 192,\n        \"Aring\": 197,\n        \"Atilde\": 195,\n        \"Auml\": 196,\n        \"Ccedil\": 199,\n        \"ETH\": 208,\n        \"Eacute\": 201,\n        \"Ecirc\": 202,\n        \"Egrave\": 200,\n        \"Euml\": 203,\n        \"Iacute\": 205,\n        \"Icirc\": 206,\n        \"Igrave\": 204,\n        \"Iuml\": 207,\n        \"Ntilde\": 209,\n        \"Oacute\": 211,\n        \"Ocirc\": 212,\n        \"Ograve\": 210,\n        \"Oslash\": 216,\n        \"Otilde\": 213,\n        \"Ouml\": 214,\n        \"THORN\": 222,\n        \"Uacute\": 218,\n        \"Ucirc\": 219,\n        \"Ugrave\": 217,\n        \"Uuml\": 220,\n        \"Yacute\": 221,\n        \"aacute\": 225,\n        \"acirc\": 226,\n        \"aelig\": 230,\n        \"agrave\": 224,\n        \"aring\": 229,\n        \"atilde\": 227,\n        \"auml\": 228,\n        \"ccedil\": 231,\n        \"eacute\": 233,\n        \"ecirc\": 234,\n        \"egrave\": 232,\n        \"eth\": 240,\n        \"euml\": 235,\n        \"iacute\": 237,\n        \"icirc\": 238,\n        \"igrave\": 236,\n        \"iuml\": 239,\n        \"ntilde\": 241,\n        \"oacute\": 243,\n        \"ocirc\": 244,\n        \"ograve\": 242,\n        \"oslash\": 248,\n        \"otilde\": 245,\n        \"ouml\": 246,\n        \"szlig\": 223,\n        \"thorn\": 254,\n        \"uacute\": 250,\n        \"ucirc\": 251,\n        \"ugrave\": 249,\n        \"uuml\": 252,\n        \"yacute\": 253,\n        \"yuml\": 255,\n        \"copy\": 169,\n        \"reg\": 174,\n        \"nbsp\": 160,\n        \"iexcl\": 161,\n        \"cent\": 162,\n        \"pound\": 163,\n        \"curren\": 164,\n        \"yen\": 165,\n        \"brvbar\": 166,\n        \"sect\": 167,\n        \"uml\": 168,\n        \"ordf\": 170,\n        \"laquo\": 171,\n        \"not\": 172,\n        \"shy\": 173,\n        \"macr\": 175,\n        \"deg\": 176,\n        \"plusmn\": 177,\n        \"sup1\": 185,\n        \"sup2\": 178,\n        \"sup3\": 179,\n        \"acute\": 180,\n        \"micro\": 181,\n        \"para\": 182,\n        \"middot\": 183,\n        \"cedil\": 184,\n        \"ordm\": 186,\n        \"raquo\": 187,\n        \"frac14\": 188,\n        \"frac12\": 189,\n        \"frac34\": 190,\n        \"iquest\": 191,\n        \"times\": 215,\n        \"divide\": 247,\n        \"OElig\": 338,\n        \"oelig\": 339,\n        \"Scaron\": 352,\n        \"scaron\": 353,\n        \"Yuml\": 376,\n        \"fnof\": 402,\n        \"circ\": 710,\n        \"tilde\": 732,\n        \"Alpha\": 913,\n        \"Beta\": 914,\n        \"Gamma\": 915,\n        \"Delta\": 916,\n        \"Epsilon\": 917,\n        \"Zeta\": 918,\n        \"Eta\": 919,\n        \"Theta\": 920,\n        \"Iota\": 921,\n        \"Kappa\": 922,\n        \"Lambda\": 923,\n        \"Mu\": 924,\n        \"Nu\": 925,\n        \"Xi\": 926,\n        \"Omicron\": 927,\n        \"Pi\": 928,\n        \"Rho\": 929,\n        \"Sigma\": 931,\n        \"Tau\": 932,\n        \"Upsilon\": 933,\n        \"Phi\": 934,\n        \"Chi\": 935,\n        \"Psi\": 936,\n        \"Omega\": 937,\n        \"alpha\": 945,\n        \"beta\": 946,\n        \"gamma\": 947,\n        \"delta\": 948,\n        \"epsilon\": 949,\n        \"zeta\": 950,\n        \"eta\": 951,\n        \"theta\": 952,\n        \"iota\": 953,\n        \"kappa\": 954,\n        \"lambda\": 955,\n        \"mu\": 956,\n        \"nu\": 957,\n        \"xi\": 958,\n        \"omicron\": 959,\n        \"pi\": 960,\n        \"rho\": 961,\n        \"sigmaf\": 962,\n        \"sigma\": 963,\n        \"tau\": 964,\n        \"upsilon\": 965,\n        \"phi\": 966,\n        \"chi\": 967,\n        \"psi\": 968,\n        \"omega\": 969,\n        \"thetasym\": 977,\n        \"upsih\": 978,\n        \"piv\": 982,\n        \"ensp\": 8194,\n        \"emsp\": 8195,\n        \"thinsp\": 8201,\n        \"zwnj\": 8204,\n        \"zwj\": 8205,\n        \"lrm\": 8206,\n        \"rlm\": 8207,\n        \"ndash\": 8211,\n        \"mdash\": 8212,\n        \"lsquo\": 8216,\n        \"rsquo\": 8217,\n        \"sbquo\": 8218,\n        \"ldquo\": 8220,\n        \"rdquo\": 8221,\n        \"bdquo\": 8222,\n        \"dagger\": 8224,\n        \"Dagger\": 8225,\n        \"bull\": 8226,\n        \"hellip\": 8230,\n        \"permil\": 8240,\n        \"prime\": 8242,\n        \"Prime\": 8243,\n        \"lsaquo\": 8249,\n        \"rsaquo\": 8250,\n        \"oline\": 8254,\n        \"frasl\": 8260,\n        \"euro\": 8364,\n        \"image\": 8465,\n        \"weierp\": 8472,\n        \"real\": 8476,\n        \"trade\": 8482,\n        \"alefsym\": 8501,\n        \"larr\": 8592,\n        \"uarr\": 8593,\n        \"rarr\": 8594,\n        \"darr\": 8595,\n        \"harr\": 8596,\n        \"crarr\": 8629,\n        \"lArr\": 8656,\n        \"uArr\": 8657,\n        \"rArr\": 8658,\n        \"dArr\": 8659,\n        \"hArr\": 8660,\n        \"forall\": 8704,\n        \"part\": 8706,\n        \"exist\": 8707,\n        \"empty\": 8709,\n        \"nabla\": 8711,\n        \"isin\": 8712,\n        \"notin\": 8713,\n        \"ni\": 8715,\n        \"prod\": 8719,\n        \"sum\": 8721,\n        \"minus\": 8722,\n        \"lowast\": 8727,\n        \"radic\": 8730,\n        \"prop\": 8733,\n        \"infin\": 8734,\n        \"ang\": 8736,\n        \"and\": 8743,\n        \"or\": 8744,\n        \"cap\": 8745,\n        \"cup\": 8746,\n        \"int\": 8747,\n        \"there4\": 8756,\n        \"sim\": 8764,\n        \"cong\": 8773,\n        \"asymp\": 8776,\n        \"ne\": 8800,\n        \"equiv\": 8801,\n        \"le\": 8804,\n        \"ge\": 8805,\n        \"sub\": 8834,\n        \"sup\": 8835,\n        \"nsub\": 8836,\n        \"sube\": 8838,\n        \"supe\": 8839,\n        \"oplus\": 8853,\n        \"otimes\": 8855,\n        \"perp\": 8869,\n        \"sdot\": 8901,\n        \"lceil\": 8968,\n        \"rceil\": 8969,\n        \"lfloor\": 8970,\n        \"rfloor\": 8971,\n        \"lang\": 9001,\n        \"rang\": 9002,\n        \"loz\": 9674,\n        \"spades\": 9824,\n        \"clubs\": 9827,\n        \"hearts\": 9829,\n        \"diams\": 9830\n    };\n    Object.keys(sax.ENTITIES).forEach(function(key) {\n        var e = sax.ENTITIES[key];\n        var s = typeof e === \"number\" ? String.fromCharCode(e) : e;\n        sax.ENTITIES[key] = s;\n    });\n    for(var s in sax.STATE){\n        sax.STATE[sax.STATE[s]] = s;\n    }\n    // shorthand\n    S = sax.STATE;\n    function emit(parser, event, data) {\n        parser[event] && parser[event](data);\n    }\n    function emitNode(parser, nodeType, data) {\n        if (parser.textNode) closeText(parser);\n        emit(parser, nodeType, data);\n    }\n    function closeText(parser) {\n        parser.textNode = textopts(parser.opt, parser.textNode);\n        if (parser.textNode) emit(parser, \"ontext\", parser.textNode);\n        parser.textNode = \"\";\n    }\n    function textopts(opt, text) {\n        if (opt.trim) text = text.trim();\n        if (opt.normalize) text = text.replace(/\\s+/g, \" \");\n        return text;\n    }\n    function error(parser, er) {\n        closeText(parser);\n        if (parser.trackPosition) {\n            er += \"\\nLine: \" + parser.line + \"\\nColumn: \" + parser.column + \"\\nChar: \" + parser.c;\n        }\n        er = new Error(er);\n        parser.error = er;\n        emit(parser, \"onerror\", er);\n        return parser;\n    }\n    function end(parser) {\n        if (parser.sawRoot && !parser.closedRoot) strictFail(parser, \"Unclosed root tag\");\n        if (parser.state !== S.BEGIN && parser.state !== S.BEGIN_WHITESPACE && parser.state !== S.TEXT) {\n            error(parser, \"Unexpected end\");\n        }\n        closeText(parser);\n        parser.c = \"\";\n        parser.closed = true;\n        emit(parser, \"onend\");\n        SAXParser.call(parser, parser.strict, parser.opt);\n        return parser;\n    }\n    function strictFail(parser, message) {\n        if (typeof parser !== \"object\" || !(parser instanceof SAXParser)) {\n            throw new Error(\"bad call to strictFail\");\n        }\n        if (parser.strict) {\n            error(parser, message);\n        }\n    }\n    function newTag(parser) {\n        if (!parser.strict) parser.tagName = parser.tagName[parser.looseCase]();\n        var parent = parser.tags[parser.tags.length - 1] || parser;\n        var tag = parser.tag = {\n            name: parser.tagName,\n            attributes: {}\n        };\n        // will be overridden if tag contails an xmlns=\"foo\" or xmlns:foo=\"bar\"\n        if (parser.opt.xmlns) {\n            tag.ns = parent.ns;\n        }\n        parser.attribList.length = 0;\n        emitNode(parser, \"onopentagstart\", tag);\n    }\n    function qname(name, attribute) {\n        var i = name.indexOf(\":\");\n        var qualName = i < 0 ? [\n            \"\",\n            name\n        ] : name.split(\":\");\n        var prefix = qualName[0];\n        var local = qualName[1];\n        // <x \"xmlns\"=\"http://foo\">\n        if (attribute && name === \"xmlns\") {\n            prefix = \"xmlns\";\n            local = \"\";\n        }\n        return {\n            prefix: prefix,\n            local: local\n        };\n    }\n    function attrib(parser) {\n        if (!parser.strict) {\n            parser.attribName = parser.attribName[parser.looseCase]();\n        }\n        if (parser.attribList.indexOf(parser.attribName) !== -1 || parser.tag.attributes.hasOwnProperty(parser.attribName)) {\n            parser.attribName = parser.attribValue = \"\";\n            return;\n        }\n        if (parser.opt.xmlns) {\n            var qn = qname(parser.attribName, true);\n            var prefix = qn.prefix;\n            var local = qn.local;\n            if (prefix === \"xmlns\") {\n                // namespace binding attribute. push the binding into scope\n                if (local === \"xml\" && parser.attribValue !== XML_NAMESPACE) {\n                    strictFail(parser, \"xml: prefix must be bound to \" + XML_NAMESPACE + \"\\n\" + \"Actual: \" + parser.attribValue);\n                } else if (local === \"xmlns\" && parser.attribValue !== XMLNS_NAMESPACE) {\n                    strictFail(parser, \"xmlns: prefix must be bound to \" + XMLNS_NAMESPACE + \"\\n\" + \"Actual: \" + parser.attribValue);\n                } else {\n                    var tag = parser.tag;\n                    var parent = parser.tags[parser.tags.length - 1] || parser;\n                    if (tag.ns === parent.ns) {\n                        tag.ns = Object.create(parent.ns);\n                    }\n                    tag.ns[local] = parser.attribValue;\n                }\n            }\n            // defer onattribute events until all attributes have been seen\n            // so any new bindings can take effect. preserve attribute order\n            // so deferred events can be emitted in document order\n            parser.attribList.push([\n                parser.attribName,\n                parser.attribValue\n            ]);\n        } else {\n            // in non-xmlns mode, we can emit the event right away\n            parser.tag.attributes[parser.attribName] = parser.attribValue;\n            emitNode(parser, \"onattribute\", {\n                name: parser.attribName,\n                value: parser.attribValue\n            });\n        }\n        parser.attribName = parser.attribValue = \"\";\n    }\n    function openTag(parser, selfClosing) {\n        if (parser.opt.xmlns) {\n            // emit namespace binding events\n            var tag = parser.tag;\n            // add namespace info to tag\n            var qn = qname(parser.tagName);\n            tag.prefix = qn.prefix;\n            tag.local = qn.local;\n            tag.uri = tag.ns[qn.prefix] || \"\";\n            if (tag.prefix && !tag.uri) {\n                strictFail(parser, \"Unbound namespace prefix: \" + JSON.stringify(parser.tagName));\n                tag.uri = qn.prefix;\n            }\n            var parent = parser.tags[parser.tags.length - 1] || parser;\n            if (tag.ns && parent.ns !== tag.ns) {\n                Object.keys(tag.ns).forEach(function(p) {\n                    emitNode(parser, \"onopennamespace\", {\n                        prefix: p,\n                        uri: tag.ns[p]\n                    });\n                });\n            }\n            // handle deferred onattribute events\n            // Note: do not apply default ns to attributes:\n            //   http://www.w3.org/TR/REC-xml-names/#defaulting\n            for(var i = 0, l = parser.attribList.length; i < l; i++){\n                var nv = parser.attribList[i];\n                var name = nv[0];\n                var value = nv[1];\n                var qualName = qname(name, true);\n                var prefix = qualName.prefix;\n                var local = qualName.local;\n                var uri = prefix === \"\" ? \"\" : tag.ns[prefix] || \"\";\n                var a = {\n                    name: name,\n                    value: value,\n                    prefix: prefix,\n                    local: local,\n                    uri: uri\n                };\n                // if there's any attributes with an undefined namespace,\n                // then fail on them now.\n                if (prefix && prefix !== \"xmlns\" && !uri) {\n                    strictFail(parser, \"Unbound namespace prefix: \" + JSON.stringify(prefix));\n                    a.uri = prefix;\n                }\n                parser.tag.attributes[name] = a;\n                emitNode(parser, \"onattribute\", a);\n            }\n            parser.attribList.length = 0;\n        }\n        parser.tag.isSelfClosing = !!selfClosing;\n        // process the tag\n        parser.sawRoot = true;\n        parser.tags.push(parser.tag);\n        emitNode(parser, \"onopentag\", parser.tag);\n        if (!selfClosing) {\n            // special case for <script> in non-strict mode.\n            if (!parser.noscript && parser.tagName.toLowerCase() === \"script\") {\n                parser.state = S.SCRIPT;\n            } else {\n                parser.state = S.TEXT;\n            }\n            parser.tag = null;\n            parser.tagName = \"\";\n        }\n        parser.attribName = parser.attribValue = \"\";\n        parser.attribList.length = 0;\n    }\n    function closeTag(parser) {\n        if (!parser.tagName) {\n            strictFail(parser, \"Weird empty close tag.\");\n            parser.textNode += \"</>\";\n            parser.state = S.TEXT;\n            return;\n        }\n        if (parser.script) {\n            if (parser.tagName !== \"script\") {\n                parser.script += \"</\" + parser.tagName + \">\";\n                parser.tagName = \"\";\n                parser.state = S.SCRIPT;\n                return;\n            }\n            emitNode(parser, \"onscript\", parser.script);\n            parser.script = \"\";\n        }\n        // first make sure that the closing tag actually exists.\n        // <a><b></c></b></a> will close everything, otherwise.\n        var t = parser.tags.length;\n        var tagName = parser.tagName;\n        if (!parser.strict) {\n            tagName = tagName[parser.looseCase]();\n        }\n        var closeTo = tagName;\n        while(t--){\n            var close = parser.tags[t];\n            if (close.name !== closeTo) {\n                // fail the first time in strict mode\n                strictFail(parser, \"Unexpected close tag\");\n            } else {\n                break;\n            }\n        }\n        // didn't find it.  we already failed for strict, so just abort.\n        if (t < 0) {\n            strictFail(parser, \"Unmatched closing tag: \" + parser.tagName);\n            parser.textNode += \"</\" + parser.tagName + \">\";\n            parser.state = S.TEXT;\n            return;\n        }\n        parser.tagName = tagName;\n        var s = parser.tags.length;\n        while(s-- > t){\n            var tag = parser.tag = parser.tags.pop();\n            parser.tagName = parser.tag.name;\n            emitNode(parser, \"onclosetag\", parser.tagName);\n            var x = {};\n            for(var i in tag.ns){\n                x[i] = tag.ns[i];\n            }\n            var parent = parser.tags[parser.tags.length - 1] || parser;\n            if (parser.opt.xmlns && tag.ns !== parent.ns) {\n                // remove namespace bindings introduced by tag\n                Object.keys(tag.ns).forEach(function(p) {\n                    var n = tag.ns[p];\n                    emitNode(parser, \"onclosenamespace\", {\n                        prefix: p,\n                        uri: n\n                    });\n                });\n            }\n        }\n        if (t === 0) parser.closedRoot = true;\n        parser.tagName = parser.attribValue = parser.attribName = \"\";\n        parser.attribList.length = 0;\n        parser.state = S.TEXT;\n    }\n    function parseEntity(parser) {\n        var entity = parser.entity;\n        var entityLC = entity.toLowerCase();\n        var num;\n        var numStr = \"\";\n        if (parser.ENTITIES[entity]) {\n            return parser.ENTITIES[entity];\n        }\n        if (parser.ENTITIES[entityLC]) {\n            return parser.ENTITIES[entityLC];\n        }\n        entity = entityLC;\n        if (entity.charAt(0) === \"#\") {\n            if (entity.charAt(1) === \"x\") {\n                entity = entity.slice(2);\n                num = parseInt(entity, 16);\n                numStr = num.toString(16);\n            } else {\n                entity = entity.slice(1);\n                num = parseInt(entity, 10);\n                numStr = num.toString(10);\n            }\n        }\n        entity = entity.replace(/^0+/, \"\");\n        if (isNaN(num) || numStr.toLowerCase() !== entity) {\n            strictFail(parser, \"Invalid character entity\");\n            return \"&\" + parser.entity + \";\";\n        }\n        return String.fromCodePoint(num);\n    }\n    function beginWhiteSpace(parser, c) {\n        if (c === \"<\") {\n            parser.state = S.OPEN_WAKA;\n            parser.startTagPosition = parser.position;\n        } else if (!isWhitespace(c)) {\n            // have to process this as a text node.\n            // weird, but happens.\n            strictFail(parser, \"Non-whitespace before first tag.\");\n            parser.textNode = c;\n            parser.state = S.TEXT;\n        }\n    }\n    function charAt(chunk, i) {\n        var result = \"\";\n        if (i < chunk.length) {\n            result = chunk.charAt(i);\n        }\n        return result;\n    }\n    function write(chunk) {\n        var parser = this;\n        if (this.error) {\n            throw this.error;\n        }\n        if (parser.closed) {\n            return error(parser, \"Cannot write after close. Assign an onready handler.\");\n        }\n        if (chunk === null) {\n            return end(parser);\n        }\n        if (typeof chunk === \"object\") {\n            chunk = chunk.toString();\n        }\n        var i = 0;\n        var c = \"\";\n        while(true){\n            c = charAt(chunk, i++);\n            parser.c = c;\n            if (!c) {\n                break;\n            }\n            if (parser.trackPosition) {\n                parser.position++;\n                if (c === \"\\n\") {\n                    parser.line++;\n                    parser.column = 0;\n                } else {\n                    parser.column++;\n                }\n            }\n            switch(parser.state){\n                case S.BEGIN:\n                    parser.state = S.BEGIN_WHITESPACE;\n                    if (c === \"\\uFEFF\") {\n                        continue;\n                    }\n                    beginWhiteSpace(parser, c);\n                    continue;\n                case S.BEGIN_WHITESPACE:\n                    beginWhiteSpace(parser, c);\n                    continue;\n                case S.TEXT:\n                    if (parser.sawRoot && !parser.closedRoot) {\n                        var starti = i - 1;\n                        while(c && c !== \"<\" && c !== \"&\"){\n                            c = charAt(chunk, i++);\n                            if (c && parser.trackPosition) {\n                                parser.position++;\n                                if (c === \"\\n\") {\n                                    parser.line++;\n                                    parser.column = 0;\n                                } else {\n                                    parser.column++;\n                                }\n                            }\n                        }\n                        parser.textNode += chunk.substring(starti, i - 1);\n                    }\n                    if (c === \"<\" && !(parser.sawRoot && parser.closedRoot && !parser.strict)) {\n                        parser.state = S.OPEN_WAKA;\n                        parser.startTagPosition = parser.position;\n                    } else {\n                        if (!isWhitespace(c) && (!parser.sawRoot || parser.closedRoot)) {\n                            strictFail(parser, \"Text data outside of root node.\");\n                        }\n                        if (c === \"&\") {\n                            parser.state = S.TEXT_ENTITY;\n                        } else {\n                            parser.textNode += c;\n                        }\n                    }\n                    continue;\n                case S.SCRIPT:\n                    // only non-strict\n                    if (c === \"<\") {\n                        parser.state = S.SCRIPT_ENDING;\n                    } else {\n                        parser.script += c;\n                    }\n                    continue;\n                case S.SCRIPT_ENDING:\n                    if (c === \"/\") {\n                        parser.state = S.CLOSE_TAG;\n                    } else {\n                        parser.script += \"<\" + c;\n                        parser.state = S.SCRIPT;\n                    }\n                    continue;\n                case S.OPEN_WAKA:\n                    // either a /, ?, !, or text is coming next.\n                    if (c === \"!\") {\n                        parser.state = S.SGML_DECL;\n                        parser.sgmlDecl = \"\";\n                    } else if (isWhitespace(c)) {\n                    // wait for it...\n                    } else if (isMatch(nameStart, c)) {\n                        parser.state = S.OPEN_TAG;\n                        parser.tagName = c;\n                    } else if (c === \"/\") {\n                        parser.state = S.CLOSE_TAG;\n                        parser.tagName = \"\";\n                    } else if (c === \"?\") {\n                        parser.state = S.PROC_INST;\n                        parser.procInstName = parser.procInstBody = \"\";\n                    } else {\n                        strictFail(parser, \"Unencoded <\");\n                        // if there was some whitespace, then add that in.\n                        if (parser.startTagPosition + 1 < parser.position) {\n                            var pad = parser.position - parser.startTagPosition;\n                            c = new Array(pad).join(\" \") + c;\n                        }\n                        parser.textNode += \"<\" + c;\n                        parser.state = S.TEXT;\n                    }\n                    continue;\n                case S.SGML_DECL:\n                    if ((parser.sgmlDecl + c).toUpperCase() === CDATA) {\n                        emitNode(parser, \"onopencdata\");\n                        parser.state = S.CDATA;\n                        parser.sgmlDecl = \"\";\n                        parser.cdata = \"\";\n                    } else if (parser.sgmlDecl + c === \"--\") {\n                        parser.state = S.COMMENT;\n                        parser.comment = \"\";\n                        parser.sgmlDecl = \"\";\n                    } else if ((parser.sgmlDecl + c).toUpperCase() === DOCTYPE) {\n                        parser.state = S.DOCTYPE;\n                        if (parser.doctype || parser.sawRoot) {\n                            strictFail(parser, \"Inappropriately located doctype declaration\");\n                        }\n                        parser.doctype = \"\";\n                        parser.sgmlDecl = \"\";\n                    } else if (c === \">\") {\n                        emitNode(parser, \"onsgmldeclaration\", parser.sgmlDecl);\n                        parser.sgmlDecl = \"\";\n                        parser.state = S.TEXT;\n                    } else if (isQuote(c)) {\n                        parser.state = S.SGML_DECL_QUOTED;\n                        parser.sgmlDecl += c;\n                    } else {\n                        parser.sgmlDecl += c;\n                    }\n                    continue;\n                case S.SGML_DECL_QUOTED:\n                    if (c === parser.q) {\n                        parser.state = S.SGML_DECL;\n                        parser.q = \"\";\n                    }\n                    parser.sgmlDecl += c;\n                    continue;\n                case S.DOCTYPE:\n                    if (c === \">\") {\n                        parser.state = S.TEXT;\n                        emitNode(parser, \"ondoctype\", parser.doctype);\n                        parser.doctype = true // just remember that we saw it.\n                        ;\n                    } else {\n                        parser.doctype += c;\n                        if (c === \"[\") {\n                            parser.state = S.DOCTYPE_DTD;\n                        } else if (isQuote(c)) {\n                            parser.state = S.DOCTYPE_QUOTED;\n                            parser.q = c;\n                        }\n                    }\n                    continue;\n                case S.DOCTYPE_QUOTED:\n                    parser.doctype += c;\n                    if (c === parser.q) {\n                        parser.q = \"\";\n                        parser.state = S.DOCTYPE;\n                    }\n                    continue;\n                case S.DOCTYPE_DTD:\n                    parser.doctype += c;\n                    if (c === \"]\") {\n                        parser.state = S.DOCTYPE;\n                    } else if (isQuote(c)) {\n                        parser.state = S.DOCTYPE_DTD_QUOTED;\n                        parser.q = c;\n                    }\n                    continue;\n                case S.DOCTYPE_DTD_QUOTED:\n                    parser.doctype += c;\n                    if (c === parser.q) {\n                        parser.state = S.DOCTYPE_DTD;\n                        parser.q = \"\";\n                    }\n                    continue;\n                case S.COMMENT:\n                    if (c === \"-\") {\n                        parser.state = S.COMMENT_ENDING;\n                    } else {\n                        parser.comment += c;\n                    }\n                    continue;\n                case S.COMMENT_ENDING:\n                    if (c === \"-\") {\n                        parser.state = S.COMMENT_ENDED;\n                        parser.comment = textopts(parser.opt, parser.comment);\n                        if (parser.comment) {\n                            emitNode(parser, \"oncomment\", parser.comment);\n                        }\n                        parser.comment = \"\";\n                    } else {\n                        parser.comment += \"-\" + c;\n                        parser.state = S.COMMENT;\n                    }\n                    continue;\n                case S.COMMENT_ENDED:\n                    if (c !== \">\") {\n                        strictFail(parser, \"Malformed comment\");\n                        // allow <!-- blah -- bloo --> in non-strict mode,\n                        // which is a comment of \" blah -- bloo \"\n                        parser.comment += \"--\" + c;\n                        parser.state = S.COMMENT;\n                    } else {\n                        parser.state = S.TEXT;\n                    }\n                    continue;\n                case S.CDATA:\n                    if (c === \"]\") {\n                        parser.state = S.CDATA_ENDING;\n                    } else {\n                        parser.cdata += c;\n                    }\n                    continue;\n                case S.CDATA_ENDING:\n                    if (c === \"]\") {\n                        parser.state = S.CDATA_ENDING_2;\n                    } else {\n                        parser.cdata += \"]\" + c;\n                        parser.state = S.CDATA;\n                    }\n                    continue;\n                case S.CDATA_ENDING_2:\n                    if (c === \">\") {\n                        if (parser.cdata) {\n                            emitNode(parser, \"oncdata\", parser.cdata);\n                        }\n                        emitNode(parser, \"onclosecdata\");\n                        parser.cdata = \"\";\n                        parser.state = S.TEXT;\n                    } else if (c === \"]\") {\n                        parser.cdata += \"]\";\n                    } else {\n                        parser.cdata += \"]]\" + c;\n                        parser.state = S.CDATA;\n                    }\n                    continue;\n                case S.PROC_INST:\n                    if (c === \"?\") {\n                        parser.state = S.PROC_INST_ENDING;\n                    } else if (isWhitespace(c)) {\n                        parser.state = S.PROC_INST_BODY;\n                    } else {\n                        parser.procInstName += c;\n                    }\n                    continue;\n                case S.PROC_INST_BODY:\n                    if (!parser.procInstBody && isWhitespace(c)) {\n                        continue;\n                    } else if (c === \"?\") {\n                        parser.state = S.PROC_INST_ENDING;\n                    } else {\n                        parser.procInstBody += c;\n                    }\n                    continue;\n                case S.PROC_INST_ENDING:\n                    if (c === \">\") {\n                        emitNode(parser, \"onprocessinginstruction\", {\n                            name: parser.procInstName,\n                            body: parser.procInstBody\n                        });\n                        parser.procInstName = parser.procInstBody = \"\";\n                        parser.state = S.TEXT;\n                    } else {\n                        parser.procInstBody += \"?\" + c;\n                        parser.state = S.PROC_INST_BODY;\n                    }\n                    continue;\n                case S.OPEN_TAG:\n                    if (isMatch(nameBody, c)) {\n                        parser.tagName += c;\n                    } else {\n                        newTag(parser);\n                        if (c === \">\") {\n                            openTag(parser);\n                        } else if (c === \"/\") {\n                            parser.state = S.OPEN_TAG_SLASH;\n                        } else {\n                            if (!isWhitespace(c)) {\n                                strictFail(parser, \"Invalid character in tag name\");\n                            }\n                            parser.state = S.ATTRIB;\n                        }\n                    }\n                    continue;\n                case S.OPEN_TAG_SLASH:\n                    if (c === \">\") {\n                        openTag(parser, true);\n                        closeTag(parser);\n                    } else {\n                        strictFail(parser, \"Forward-slash in opening tag not followed by >\");\n                        parser.state = S.ATTRIB;\n                    }\n                    continue;\n                case S.ATTRIB:\n                    // haven't read the attribute name yet.\n                    if (isWhitespace(c)) {\n                        continue;\n                    } else if (c === \">\") {\n                        openTag(parser);\n                    } else if (c === \"/\") {\n                        parser.state = S.OPEN_TAG_SLASH;\n                    } else if (isMatch(nameStart, c)) {\n                        parser.attribName = c;\n                        parser.attribValue = \"\";\n                        parser.state = S.ATTRIB_NAME;\n                    } else {\n                        strictFail(parser, \"Invalid attribute name\");\n                    }\n                    continue;\n                case S.ATTRIB_NAME:\n                    if (c === \"=\") {\n                        parser.state = S.ATTRIB_VALUE;\n                    } else if (c === \">\") {\n                        strictFail(parser, \"Attribute without value\");\n                        parser.attribValue = parser.attribName;\n                        attrib(parser);\n                        openTag(parser);\n                    } else if (isWhitespace(c)) {\n                        parser.state = S.ATTRIB_NAME_SAW_WHITE;\n                    } else if (isMatch(nameBody, c)) {\n                        parser.attribName += c;\n                    } else {\n                        strictFail(parser, \"Invalid attribute name\");\n                    }\n                    continue;\n                case S.ATTRIB_NAME_SAW_WHITE:\n                    if (c === \"=\") {\n                        parser.state = S.ATTRIB_VALUE;\n                    } else if (isWhitespace(c)) {\n                        continue;\n                    } else {\n                        strictFail(parser, \"Attribute without value\");\n                        parser.tag.attributes[parser.attribName] = \"\";\n                        parser.attribValue = \"\";\n                        emitNode(parser, \"onattribute\", {\n                            name: parser.attribName,\n                            value: \"\"\n                        });\n                        parser.attribName = \"\";\n                        if (c === \">\") {\n                            openTag(parser);\n                        } else if (isMatch(nameStart, c)) {\n                            parser.attribName = c;\n                            parser.state = S.ATTRIB_NAME;\n                        } else {\n                            strictFail(parser, \"Invalid attribute name\");\n                            parser.state = S.ATTRIB;\n                        }\n                    }\n                    continue;\n                case S.ATTRIB_VALUE:\n                    if (isWhitespace(c)) {\n                        continue;\n                    } else if (isQuote(c)) {\n                        parser.q = c;\n                        parser.state = S.ATTRIB_VALUE_QUOTED;\n                    } else {\n                        strictFail(parser, \"Unquoted attribute value\");\n                        parser.state = S.ATTRIB_VALUE_UNQUOTED;\n                        parser.attribValue = c;\n                    }\n                    continue;\n                case S.ATTRIB_VALUE_QUOTED:\n                    if (c !== parser.q) {\n                        if (c === \"&\") {\n                            parser.state = S.ATTRIB_VALUE_ENTITY_Q;\n                        } else {\n                            parser.attribValue += c;\n                        }\n                        continue;\n                    }\n                    attrib(parser);\n                    parser.q = \"\";\n                    parser.state = S.ATTRIB_VALUE_CLOSED;\n                    continue;\n                case S.ATTRIB_VALUE_CLOSED:\n                    if (isWhitespace(c)) {\n                        parser.state = S.ATTRIB;\n                    } else if (c === \">\") {\n                        openTag(parser);\n                    } else if (c === \"/\") {\n                        parser.state = S.OPEN_TAG_SLASH;\n                    } else if (isMatch(nameStart, c)) {\n                        strictFail(parser, \"No whitespace between attributes\");\n                        parser.attribName = c;\n                        parser.attribValue = \"\";\n                        parser.state = S.ATTRIB_NAME;\n                    } else {\n                        strictFail(parser, \"Invalid attribute name\");\n                    }\n                    continue;\n                case S.ATTRIB_VALUE_UNQUOTED:\n                    if (!isAttribEnd(c)) {\n                        if (c === \"&\") {\n                            parser.state = S.ATTRIB_VALUE_ENTITY_U;\n                        } else {\n                            parser.attribValue += c;\n                        }\n                        continue;\n                    }\n                    attrib(parser);\n                    if (c === \">\") {\n                        openTag(parser);\n                    } else {\n                        parser.state = S.ATTRIB;\n                    }\n                    continue;\n                case S.CLOSE_TAG:\n                    if (!parser.tagName) {\n                        if (isWhitespace(c)) {\n                            continue;\n                        } else if (notMatch(nameStart, c)) {\n                            if (parser.script) {\n                                parser.script += \"</\" + c;\n                                parser.state = S.SCRIPT;\n                            } else {\n                                strictFail(parser, \"Invalid tagname in closing tag.\");\n                            }\n                        } else {\n                            parser.tagName = c;\n                        }\n                    } else if (c === \">\") {\n                        closeTag(parser);\n                    } else if (isMatch(nameBody, c)) {\n                        parser.tagName += c;\n                    } else if (parser.script) {\n                        parser.script += \"</\" + parser.tagName;\n                        parser.tagName = \"\";\n                        parser.state = S.SCRIPT;\n                    } else {\n                        if (!isWhitespace(c)) {\n                            strictFail(parser, \"Invalid tagname in closing tag\");\n                        }\n                        parser.state = S.CLOSE_TAG_SAW_WHITE;\n                    }\n                    continue;\n                case S.CLOSE_TAG_SAW_WHITE:\n                    if (isWhitespace(c)) {\n                        continue;\n                    }\n                    if (c === \">\") {\n                        closeTag(parser);\n                    } else {\n                        strictFail(parser, \"Invalid characters in closing tag\");\n                    }\n                    continue;\n                case S.TEXT_ENTITY:\n                case S.ATTRIB_VALUE_ENTITY_Q:\n                case S.ATTRIB_VALUE_ENTITY_U:\n                    var returnState;\n                    var buffer;\n                    switch(parser.state){\n                        case S.TEXT_ENTITY:\n                            returnState = S.TEXT;\n                            buffer = \"textNode\";\n                            break;\n                        case S.ATTRIB_VALUE_ENTITY_Q:\n                            returnState = S.ATTRIB_VALUE_QUOTED;\n                            buffer = \"attribValue\";\n                            break;\n                        case S.ATTRIB_VALUE_ENTITY_U:\n                            returnState = S.ATTRIB_VALUE_UNQUOTED;\n                            buffer = \"attribValue\";\n                            break;\n                    }\n                    if (c === \";\") {\n                        parser[buffer] += parseEntity(parser);\n                        parser.entity = \"\";\n                        parser.state = returnState;\n                    } else if (isMatch(parser.entity.length ? entityBody : entityStart, c)) {\n                        parser.entity += c;\n                    } else {\n                        strictFail(parser, \"Invalid character in entity name\");\n                        parser[buffer] += \"&\" + parser.entity + c;\n                        parser.entity = \"\";\n                        parser.state = returnState;\n                    }\n                    continue;\n                default:\n                    throw new Error(parser, \"Unknown state: \" + parser.state);\n            }\n        } // while\n        if (parser.position >= parser.bufferCheckPosition) {\n            checkBufferLength(parser);\n        }\n        return parser;\n    }\n    /*! http://mths.be/fromcodepoint v0.1.0 by @mathias */ /* istanbul ignore next */ if (!String.fromCodePoint) {\n        (function() {\n            var stringFromCharCode = String.fromCharCode;\n            var floor = Math.floor;\n            var fromCodePoint = function() {\n                var MAX_SIZE = 0x4000;\n                var codeUnits = [];\n                var highSurrogate;\n                var lowSurrogate;\n                var index = -1;\n                var length = arguments.length;\n                if (!length) {\n                    return \"\";\n                }\n                var result = \"\";\n                while(++index < length){\n                    var codePoint = Number(arguments[index]);\n                    if (!isFinite(codePoint) || // `NaN`, `+Infinity`, or `-Infinity`\n                    codePoint < 0 || // not a valid Unicode code point\n                    codePoint > 0x10FFFF || // not a valid Unicode code point\n                    floor(codePoint) !== codePoint // not an integer\n                    ) {\n                        throw RangeError(\"Invalid code point: \" + codePoint);\n                    }\n                    if (codePoint <= 0xFFFF) {\n                        codeUnits.push(codePoint);\n                    } else {\n                        // http://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n                        codePoint -= 0x10000;\n                        highSurrogate = (codePoint >> 10) + 0xD800;\n                        lowSurrogate = codePoint % 0x400 + 0xDC00;\n                        codeUnits.push(highSurrogate, lowSurrogate);\n                    }\n                    if (index + 1 === length || codeUnits.length > MAX_SIZE) {\n                        result += stringFromCharCode.apply(null, codeUnits);\n                        codeUnits.length = 0;\n                    }\n                }\n                return result;\n            };\n            /* istanbul ignore next */ if (Object.defineProperty) {\n                Object.defineProperty(String, \"fromCodePoint\", {\n                    value: fromCodePoint,\n                    configurable: true,\n                    writable: true\n                });\n            } else {\n                String.fromCodePoint = fromCodePoint;\n            }\n        })();\n    }\n})( false ? 0 : exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/sax/lib/sax.js\n");

/***/ })

};
;