{"kind": "FETCH", "data": {"headers": {"connection": "keep-alive", "content-length": "215678", "content-type": "application/json", "date": "Wed, 28 May 2025 11:34:04 GMT", "keep-alive": "timeout=5", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Origin", "x-frame-options": "SAMEORIGIN", "x-powered-by": "Strapi <strapi.io>", "x-response-time": "263ms"}, "body": "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", "status": 200, "url": "http://127.0.0.1:1337/graphql"}, "revalidate": 3600, "tags": []}