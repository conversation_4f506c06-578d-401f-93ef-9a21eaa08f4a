(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[512],{9970:function(e,n,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/parcours-emails/[parcours]",function(){return t(4485)}])},4485:function(e,n,t){"use strict";t.r(n),t.d(n,{__N_SSG:function(){return _},default:function(){return SingleParcours}});var i=t(2729),r=t(5893),a=t(211),l=t(9521),o=t(1664),s=t.n(o),c=t(7421),d=t(785),u=t(6268),p=t(9340),m=t(4218),x=t(1304),h=t(279),f=t(6368),j=t(3617),g=t(1809),b=t(2962);function _templateObject(){let e=(0,i._)(["\n  margin-top: 70px;\n  padding-bottom: 70px;\n  display: block;\n\n  @media "," {\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: space-between;\n    align-items: flex-start; /* Pour que les \xe9l\xe9ments commencent au haut */\n    min-height: 100vh; /* Pour s'assurer que la hauteur est suffisante pour l'effet sticky */\n  }\n"]);return _templateObject=function(){return e},e}function _templateObject1(){let e=(0,i._)(["\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n\n  @media "," {\n    width: 50%;\n  }\n"]);return _templateObject1=function(){return e},e}function _templateObject2(){let e=(0,i._)(["\n  width: 100%;\n\n  @media "," {\n    padding-left: 40px;\n    border-left: 1px solid #dddddd;\n    width: 40%;\n    position: sticky;\n    top: 20px; /* Distance depuis le haut de l'\xe9cran */\n    align-self: flex-start; /* Important pour sticky dans un conteneur flex */\n    max-height: calc(100vh - 40px); /* Hauteur maximale pour \xe9viter de d\xe9passer l'\xe9cran */\n    // overflow-y: auto; /* Permet le d\xe9filement si le formulaire est trop grand */\n  }\n"]);return _templateObject2=function(){return e},e}function _templateObject3(){let e=(0,i._)(["\n  header {\n    display: flex;\n    flex-direction: column;\n    margin-bottom: 48px;\n  }\n\n  .card-label {\n    display: none;\n    margin: 8px 8px 0px 0px;\n  }\n\n  .header-img-container {\n    position: relative;\n    width: 100%;\n    aspect-ratio: 16 / 10;\n  }\n\n  @media "," {\n    .card-label {\n      display: inline-block;\n    }\n\n    .header-img-container {\n      margin-top: 48px;\n    }\n  }\n  @media "," {\n    header {\n      margin-top: 90px;\n      margin-bottom: 70px;\n      flex-direction: row;\n      justify-content: space-between;\n    }\n\n    .header-text-container {\n      position: relative;\n      width: 50%;\n      aspect-ratio: 16 / 10;\n      padding-top: 24px;\n      padding-left: 16px;\n    }\n\n    .header-img-container {\n      width: calc(50% - 32px);\n      margin-top: 0px;\n    }\n  }\n"]);return _templateObject3=function(){return e},e}var _=!0;function SingleParcours(e){var n,t,i;let{post:l,relatedPosts:o}=e;if(!l)return null;let c=(0,d.fw)(l.modules),_=(0,m.S$)(l.published_at),k=(0,d.MS)(l.modules,"lead");return(0,r.jsxs)(O,{children:[(0,r.jsx)(b.PB,{title:(null==c?void 0:null===(n=c.seo)||void 0===n?void 0:n.metaTitle)||null,description:(null==c?void 0:null===(t=c.seo)||void 0===t?void 0:t.metaDescription)||null}),(0,r.jsxs)("div",{className:"site-padding",children:[(0,r.jsx)(u.Z,{hexLight:"#f5e3df",hexDark:"#62187d"}),(0,r.jsxs)("header",{children:[(0,r.jsxs)("div",{className:"header-text-container",children:[(0,r.jsx)(f.hQ,{children:(0,r.jsx)(s(),{href:"/parcours-emails",children:"PARCOURS E-MAILS"})}),(0,r.jsx)(f.DZ,{children:l.title}),(0,r.jsx)("div",{className:"post-topics",children:l.topics&&l.topics.map((e,n)=>(0,r.jsx)(h.Z,{text:e.name},n))})]}),(0,r.jsx)("div",{className:"header-img-container",children:(0,r.jsx)(x.Z,{imageData:l.image,priority:!0})})]}),(0,r.jsxs)(p.Z,{children:[(0,r.jsx)(p.Z.Text,{label:"Publi\xe9 le",content:_,addClass:""}),(0,r.jsx)(p.Z.Authors,{label:"Auteur(s)",authors:[l.author],addClass:""}),(0,r.jsx)(p.Z.Social,{url:"https://toutpoursagloire.com/parcours-emails/".concat(l.slug),addClass:"mobile-hide_flex"})]}),(0,r.jsxs)(v,{children:[(0,r.jsxs)(w,{children:[(null==k?void 0:k.content)&&(0,r.jsx)("section",{children:(0,r.jsx)(a.oJ,{content:k.content})}),(0,r.jsx)("section",{children:(0,r.jsx)(a.oJ,{content:l.body})})]}),(0,r.jsx)(y,{children:(null==c?void 0:null===(i=c.journey)||void 0===i?void 0:i.embedForm)&&(0,r.jsx)(g.Z,{title:"Inscription",formString:c.journey.embedForm})})]})]}),(0,r.jsx)(j.Z,{items:o})]})}let v=l.ZP.main.withConfig({componentId:"sc-615ef80c-0"})(_templateObject(),c.U.desktop),w=l.ZP.article.withConfig({componentId:"sc-615ef80c-1"})(_templateObject1(),c.U.desktop),y=l.ZP.div.withConfig({componentId:"sc-615ef80c-2"})(_templateObject2(),c.U.desktop),O=l.ZP.div.withConfig({componentId:"sc-615ef80c-3"})(_templateObject3(),c.U.tablet,c.U.desktop)}},function(e){e.O(0,[755,764,962,676,291,915,211,621,774,888,179],function(){return e(e.s=9970)}),_N_E=e.O()}]);