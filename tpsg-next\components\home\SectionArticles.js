import styled from "styled-components";
import { device } from "styles/device";
import Image from "next/image";
import { withRealSrc } from "utils/image-utils";
import RoundedLabel from "components/shared/atoms/rounded-label";
import { modulesAsObj } from "utils/components.utils";
import { DefaultPostCard } from "components/shared/Card";
import { getPostRoute } from "../../utils/posts.utils";
import Link from "next/link";
import { dateForHumans } from "../../utils/date.utils";
import { AnimatedTextButton } from "../shared/Buttons";
import { removeMarkdown, removeHtml } from "../../utils/string.utils";

export default function SectionArticles({ data }) {
  const { posts } = data;
  const { lead } = modulesAsObj(posts[0].modules);

  const fpImage = posts[0].image && withRealSrc(posts[0].image);

  if (!posts.length) {
    return null;
  }

  return (
    <Wrapper>
      <FirstPost>
        <Link href={getPostRoute(posts[0])}>
          <div className={"fp-cover"}>
            {fpImage && (
              <Image
                src={fpImage}
                fill
                priority={true}
                style={objStyles.fpImage}
                sizes={"80vw"}
                alt={fpImage?.alternativeText || ""}
              />
            )}
          </div>
        </Link>
        <p className={"fp-author"}>{posts[0].author.fullName}</p>
        <Link href={getPostRoute(posts[0])}>
          <h3 className={"fp-title primary-hover"}>{posts[0].title}</h3>
        </Link>
        {lead && <p className={"fp-lead"}>{removeHtml(removeMarkdown(lead.content))}</p>}
      </FirstPost>

      <PostList>
        {posts.slice(1, 10).map((post, key) => (
          <DefaultPostCard
            key={key}
            post={post}
            options={{
              showLead: false,
              showDate: false,
              showTopics: true,
            }}
          />
        ))}
        <AnimatedTextButton text={"Tous nos articles"} link={"/recherche"} />
      </PostList>
    </Wrapper>
  );
}

const objStyles = {
  fpImage: {
    objectFit: "cover",
  },
};

const Wrapper = styled.section`
  padding: 0 var(--border-space);
  position: relative;
  margin-top: 40px;
  margin-bottom: 80px;
  display: flex;
  flex-direction: column;

  @media ${device.desktop} {
    flex-direction: row;
    margin-bottom: 164px;
  }
`;

const FirstPost = styled.div`
  position: relative;
  top: 0;
  display: block;

  .fp-date {
    position: relative;
    font-family: "Lora", serif;
    font-size: 14px;
    font-weight: 400;
    color: #888888;
    margin-top: 0;
    margin-bottom: 16px;
  }

  .fp-cover {
    position: relative;
    height: 220px;
    background-color: black;
  }

  .fp-author {
    color: #888888;
    font-family: "Novela", serif;
    font-style: italic;
    font-weight: 400;
    font-size: 14px;
    margin-top: 12px;
  }

  .fp-title {
    font-family: "Stelvio", serif;
    line-height: 110%;
    font-weight: 400;
    font-size: 30px;
    margin-top: 12px;
    margin-bottom: 0;
    width: 95%;
    color: #161616;
  }

  .fp-topics {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: start;
    justify-content: start;
    flex-wrap: wrap;
    margin-top: 16px;
    height: 32px;
    width: 100%;
    overflow: hidden;
    gap: 8px;
  }

  .fp-lead {
    font-size: 15px;
    color: #888;
    margin-top: 24px;
  }

  @media ${device.desktop} {
    position: sticky;
    width: 50%;
    height: 100vh;
    margin-right: 32px;

    .fp-date {
      font-size: 18px;
    }

    .fp-cover {
      height: 430px;
    }

    .fp-author {
      margin-top: 24px;
      font-size: 18px;
    }

    .fp-title {
      font-size: 46px;
      margin-top: 24px;
    }

    .fp-lead {
      max-width: 90%;
      font-weight: 400;
      margin-top: 24px;
      font-size: 17px;
      color: #888;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      -webkit-box-orient: vertical;
    }
  }
`;

const PostList = styled.div`
  display: inline;
  @media ${device.tablet} {
    //margin-left: 16px;
    //width: 50%;
  }
  @media ${device.desktop} {
    width: 50%;
  }
`;
