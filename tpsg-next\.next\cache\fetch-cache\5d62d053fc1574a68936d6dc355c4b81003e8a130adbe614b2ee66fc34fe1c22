{"kind": "FETCH", "data": {"headers": {"connection": "keep-alive", "content-length": "78559", "content-type": "application/json", "date": "Wed, 28 May 2025 09:54:01 GMT", "keep-alive": "timeout=5", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Origin", "x-frame-options": "SAMEORIGIN", "x-powered-by": "Strapi <strapi.io>", "x-response-time": "713ms"}, "body": "eyJkYXRhIjp7InBvc3RzIjpbeyJpZCI6IjQxMjUiLCJ0aXRsZSI6IlBvdXJxdW9pIHZvdXMgZGV2cmlleiBsaXJlIDEwIHF1ZXN0aW9ucyBxdWUgdHUgZGV2cmFpcyB0ZSBwb3NlciBzdXIgbGUgY2hyaXN0aWFuaXNtZSAgcGFyIFJlYmVjY2EgTWNMYXVnaGxpbiIsInNsdWciOiJwb3VycXVvaS1saXJlLTEwLXF1ZXN0aW9ucy1xdWUtdHUtZGV2cmFpcy10ZS1wb3Nlci1zdXItbGUtY2hyaXN0aWFuaXNtZS1yZWJlY2NhLW1jLWxhdWdobGluIiwidHlwZSI6ImFydGljbGUiLCJwdWJsaXNoZWRfYXQiOiIyMDI0LTEwLTAzVDAzOjQ1OjAwLjAwMFoiLCJib2R5IjoiIyMgUGFybGVyIGF1eCBhZG9sZXNjZW50cyDDoCBsZXVyIG5pdmVhdVxuXG5EZXB1aXMgcXVlbHF1ZSB0ZW1wcywgbm90cmUgZmFtaWxsZSBsaXQgKlsxMCBxdWVzdGlvbnMgcXVlIHR1IGRldnJhaXMgdGUgcG9zZXIgc3VyIGxlIGNocmlzdGlhbmlzbWVdKGh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzLzEwLXF1ZXN0aW9ucy1xdWUtdHUtZGV2cmFpcy10ZS1wb3Nlci1zdXItbGUtY2hyaXN0aWFuaXNtZT9zcnNsdGlkPUFmbUJPb29RZ0FJV0xLb245aDFBb3l2MzRQaTM5UkY2U3pxV1F4WGlUT3o0QlVELVB4eTMxUzN0KSogYXV0b3VyIGR1IHJlcGFzIGR1IHNvaXIgKG5vcyBmaWxsZXMgb250IDExIGV0IDE1IGFucykuIENvbW1lIGxlIHRpdHJlIGwnaW5kaXF1ZSwgY2UgbGl2cmUgYWJvcmRlIGRpeCBxdWVzdGlvbnMgc3VyIG5vdHJlIGZvaSBldCBkb25uZSBhdXggYWRvbGVzY2VudHMgbGVzIG1veWVucyBkJ3kgcsOpcG9uZHJlIHBhciBldXgtbcOqbWVzLiBDb21tZSBiZWF1Y291cCBkZSB2b3MgZW5mYW50cywgbGVzIG7DtHRyZXMgZnLDqXF1ZW50ZW50IGRlcyDDqWNvbGVzIHB1YmxpcXVlcyBvw7kgZWxsZXMgbidvbnQgcGFzIGQnYW1pcyBjaHLDqXRpZW5zLiBMZXVyIGZvaSBlc3QgbWlzZSDDoCBsJ8OpcHJldXZlIMOgIGwnaW50w6lyaWV1ciBldCDDoCBsJ2V4dMOpcmlldXIgZGUgbGEgc2FsbGUgZGUgY2xhc3NlLCBldCBub3VzIHNvdWhhaXRvbnMgZmFpcmUgbm90cmUgcGFydCBwb3VyIGxldXIgcGVybWV0dHJlIGRlIFwiZMOpZmVuZHJlIGF2ZWMgZG91Y2V1ciBldCByZXNwZWN0LCBkZXZhbnQgcXVpY29ucXVlIHZvdXMgZGVtYW5kZSBsYSByYWlzb24gZGUgbOKAmWVzcMOpcmFuY2UgcXVpIGVzdCBlbiB2b3VzXCIgKDEgUCAzOjE1KS4gRWxsZS1tw6ptZSBtw6hyZSBkZSB0cm9pcyBlbmZhbnRzLCBNbWUgTWNMYXVnaGxpbiBlbnRyZSBkYW5zIGxlIG1vbmRlIGRlIG5vcyBhZG9sZXNjZW50cyBldCBzJ2FkcmVzc2Ugw6AgZXV4LCDDoCBsZXVyIG5pdmVhdSwgc2FucyBqYW1haXMgbGVzIHByZW5kcmUgZGUgaGF1dC4gRWxsZSBjb25zaWTDqHJlIGxldXJzIHByw6lvY2N1cGF0aW9ucyBhdmVjIHPDqXJpZXV4IGV0IGF0dGVuZCBkJ2V1eCBxdSdpbHMgZXhhbWluZW50IGxlcyBhcmd1bWVudHMgcXUnZWxsZSBhbnRpY2lwZSBwb3VyIGV1eC5cblxuIyMgUGFybGVyIGF1eCBhZG9sZXNjZW50cyBkZSBsZXVyIGlkZW50aXTDqVxuXG5Ob3MgZW5mYW50cyBvbnQgZMOpc2VzcMOpcsOpbWVudCBiZXNvaW4gZGUgY2UgZ2VucmUgZGUgY29udGVudSEgRW4gcmFpc29uIGRlIGxhIHNpdHVhdGlvbiBjdWx0dXJlbGxlIGFjdHVlbGxlLCBpbHMgc29udCBjb25mcm9udMOpcyDDoCBkZXMgZMOpZmlzIGVuIG1hdGnDqHJlIGRlIGZvaSBiaWVuIHBsdXMgdMO0dCBxdWUgbm91cyBuZSBsJ8OpdGlvbnMgbG9yc3F1ZSBub3VzIMOpdGlvbnMgYWRvcy4gQWluc2ksIHBhciBleGVtcGxlLCBzb24gY2hhcGl0cmUgXCJHYXLDp29uIG91IGZpbGxlLCBxdWVsbGUgaW1wb3J0YW5jZT9cIiBuZSBwb3V2YWl0IHBhcyBtaWV1eCB0b21iZXIuIEFsb3JzIHF1J2VsbGUgYWJvcmRlIGRlcyBzdWpldHMgc2ltaWxhaXJlcyBkYW5zICpbMTIgcmFpc29ucyBkZSBuZSBwbHVzIGNyb2lyZSBhdSBjaHJpc3RpYW5pc21lXShodHRwczovL2JsZnN0b3JlLmNvbS9jb2xsZWN0aW9ucy9hbGwvcHJvZHVjdHMvMTItcmFpc29ucy1kZS1uZS1wbHVzLWNyb2lyZS1hdS1jaHJpc3RpYW5pc21lKSwqIE1jTGF1Z2hsaW4gcmVjb25uYcOudCBxdWUgcG91ciBsYSBwbHVwYXJ0IGRlcyBhZHVsdGVzLCBsYSBiYXRhaWxsZSBzZSBqb3VlIHN1ciBsZXMgaWTDqWVzLiBQb3VyIGxlcyBhZG9sZXNjZW50cywgZW4gcmV2YW5jaGUsIGMnZXN0IGxhIGNvbXByw6loZW5zaW9uIGRlIGxldXIgcHJvcHJlIGlkZW50aXTDqSBxdWkgZXN0IGVuIGpldSFcblxuIyMgUGFybGVyIGF1eCBhZG9sZXNjZW50cyBkZSBsZXVyIHNleHVhbGl0w6lcblxuRGFucyBzb24gY2hhcGl0cmUgXCJQb3VycXVvaSBuZSBwYXMganVzdGUgYWNjZXB0ZXIgcXVlIGwnYW1vdXIsIGPigJllc3QgbCdhbW91clwiLCBNY0xhdWdobGluIHBhcmxlIGZyYW5jaGVtZW50IGRlIGxhIHNleHVhbGl0w6kuIE5vbiBwYXMgZCd1bmUgbWFuacOocmUgaW5hcHByb3ByacOpZSwgbWFpcyBkYW5zIGRlcyB0ZXJtZXMgcXVlIHZvdXMgdm91ZHJleiBwZXV0LcOqdHJlIHLDqXNlcnZlciBwb3VyIGRlcyBtb21lbnRzIGVuIHTDqnRlLcOgLXTDqnRlIGF2ZWMgdm9zIGFkb2xlc2NlbnRzLiAoRW4gZCdhdXRyZXMgdGVybWVzLCBuZSBsaXNleiBwYXMgY2UgY2hhcGl0cmUgcGVuZGFudCBsZXMgZMOpdm90aW9ucyBmYW1pbGlhbGVzIGF2ZWMgbGV1cnMgamV1bmVzIGZyw6hyZXMgZXQgc8WTdXJzKS4gTCdhdXRldXJlIHBhcmxlIG91dmVydGVtZW50IGRlIHNhIGx1dHRlIGNvbnRyZSBsJ2F0dGlyYW5jZSBwb3VyIGxlIG3Dqm1lIHNleGUgZXQgYWlkZSBsZXMgcGFyZW50cyBldCBsZXMgYWRvbGVzY2VudHMgw6AgY29tcHJlbmRyZSBxdWUgbm91cyBzb21tZXMgdG91cyBzZXh1ZWxsZW1lbnQgYnJpc8Opcy4gRWxsZSBwbGFpZGUgcG91ciBxdWUgbGVzIMOJZ2xpc2VzIGV0IGxlcyBmb3llcnMgZGV2aWVubmVudCBkZXMgc2FuY3R1YWlyZXMgb8O5IGxlcyBwZXJzb25uZXMgYXV4IHByaXNlcyBhdmVjIHVuZSB0ZWxsZSBhdHRpcmFuY2UgcHVpc3NlbnQgY2hlcmNoZXIgZHUgc291dGllbiBzYW5zIGNyYWludGUgbmkganVnZW1lbnQsIGZhdm9yaXNhbnQgYWluc2kgZGVzIGVudmlyb25uZW1lbnRzIGRlIGNvbXBhc3Npb24gZXQgZGUgZ3XDqXJpc29uLlxuXG4jIyBQYXJsZXIgYXV4IGFkb2xlc2NlbnRzIGRlcyBtw6ptZXMgcXVlc3Rpb25zIHF1ZSBub3VzIGF2b25zIHBvc8OpZXNcblxuU2kgbm9zIGpldW5lcyBzb250IGNvbmZyb250w6lzIMOgIGRlcyBxdWVzdGlvbnMgcHJvcHJlcyDDoCBsZXVyIGfDqW7DqXJhdGlvbiwgZCdhdXRyZXMgY2VwZW5kYW50IGRlbWV1cmVudCBsZXMgbcOqbWVzIGF1eHF1ZWxsZXMgbm91cyBhdm9ucyBkw7sgZmFpcmUgZmFjZS4gQ29tbWVudCBmYWlyZSBjb25maWFuY2Ugw6AgbGEgQmlibGU/IE/DuSBlc3QgRGlldSBxdWFuZCBqZSBzb3VmZnJlPyBMYSBzY2llbmNlIHLDqWZ1dGUtdC1lbGxlIGxlIGNocmlzdGlhbmlzbWU/IE91aSwgaWwgcydhZ2l0IHBldXQtw6p0cmUgZGVzIG3Dqm1lcyBxdWVzdGlvbnMgcXVlIG5vdXMgYXZvbnMgZXVlcyAgbG9yc3F1ZSBub3VzIHNvbW1lcyB2ZW51cyDDoCBsYSBmb2kgZW4gQ2hyaXN0LiBDZSBxdWkgYSBjaGFuZ8OpLCBjJ2VzdCBsJ2F0dGl0dWRlIGR1IG1vbmRlIMOgIGwnw6lnYXJkIGR1IGNocmlzdGlhbmlzbWUuIExvcnNxdWUgaifDqXR1ZGlhaXMgbGVzIGVuc2VpZ25lbWVudHMgZGUgQ2hyaXN0IGVuIHRhbnQgcXVlIGx5Y8OpZW5uZSwgbGVzIGNocsOpdGllbnMgw6l0YWllbnQgY29uc2lkw6lyw6lzIGNvbW1lIGRlcyBhZ2VudHMgZHUgYmllbiBkYW5zIGxhIHNvY2nDqXTDqS4gQ2Ugbidlc3QgcGx1cyBsZSBjYXMgYXVqb3VyZCdodWkuIExlIG3DqXByaXMgw6AgbCfDqWdhcmQgZGUgbGEgcmVsaWdpb24gZXN0IHBhbHBhYmxlLCBkdSBtb2lucyBkYW5zIGwnZXhww6lyaWVuY2UgZGUgbm90cmUgZmlsbGUgYWRvLiBSYWlzb24gZGUgcGx1cyBwb3VyIGRvdGVyIG5vcyBqZXVuZXMgZGVzIGFybWVzIHNwaXJpdHVlbGxlcyBkb250IGlscyBhdXJvbnQgYmVzb2luIHBvdXIgZMOpZmVuZHJlIGxldXIgZm9pLlxuXG4jIyBTJ2FkcmVzc2VyIGF1eCBhZG9sZXNjZW50cyBlbiB0YW50IHF1ZSBmZW1tZSBkZSBsZXR0cmVzXG5cblZvaWNpIHVuIGF0b3V0IHN1cHBsw6ltZW50YWlyZTogc2kgYXBwcmVuZHJlIGQndW5lIGZlbW1lIHRpdHVsYWlyZSBkJ3VuIGRvY3RvcmF0IGRlIGwndW5pdmVyc2l0w6kgZGUgQ2FtYnJpZGdlIG4naW1wcmVzc2lvbm5lIHBldXQtw6p0cmUgcGFzIHRvdXMgbGVzIGFkb3MsIGMnZXN0IGJpZW4gbGUgY2FzIHBvdXIgbGEgbWllbm5lLiBNYSBmaWxsZSBhZG9sZXNjZW50ZSBlc3Qgc291dmVudCBleHBvc8OpZSBhdXggcHLDqWRpY2F0aW9ucyBkZXMgYW5jaWVucyBkZSBub3RyZSDDiWdsaXNlLCBhaW5zaSBxdSdhdXggZW5zZWlnbmVtZW50cyBkZXMgZnLDqHJlcyBxdWkgZGlyaWdlbnQgbGUgZ3JvdXBlIGRlIGpldW5lcy4gRMOpY291dnJpciBxdWUgbGVzIGZlbW1lcyBwZXV2ZW50IMOqdHJlIGRlcyDDqXJ1ZGl0ZXMgZXQgZGVzIGV4cGVydGVzIGVuIGFwb2xvZ8OpdGlxdWUgYSBpbnNwaXLDqSDDoCBtZXMgZmlsbGVzIGwnaWTDqWUgcXUnZWxsZXMgcG91dmFpZW50LCBlbGxlcyBhdXNzaSwgc2VydmlyIERpZXUgYXZlYyBsZXVyIGludGVsbGVjdC5cblxuIyMgUsOpZmxleGlvbnMgZmluYWxlc1xuXG5NY0xhdWdobGluIGNvbmNsdXQgY2hhcXVlIGNoYXBpdHJlIHBhciB1biByw6ljYXBpdHVsYXRpZiBkZSBjaGFjdW4gZGVzIHBvaW50cy4gU2kgdm91cyByw6l1c3Npc3NleiDDoCBjb252YWluY3JlIHZvdHJlIGFkb2xlc2NlbnQgZGUgbGlyZSBjZSBsaXZyZSB0b3V0IHNldWwsIGJyYXZvISBTaSwgYXUgY29udHJhaXJlLCB2b3VzIGxpc2V6IMOgIHZvaXggaGF1dGUgw6Agdm9zIGVuZmFudHMgY29tbWUgamUgbGUgZmFpcywgY2VzIHLDqXN1bcOpcyBkZSBjaGFwaXRyZSBhaWRlbnQgbCdhZG9sZXNjZW50IGRpc3RyYWl0IChldCBsZSBwYXJlbnQpIMOgIHNlIHJhcHBlbGVyIGwnZXNzZW50aWVsIGRlIGNlIHF1aSBhIMOpdMOpIGFib3Jkw6kgZGFucyBsZSBjaGFwaXRyZS4gTWFpcyBxdWUgdm91cyBsaXNpZXogw6Agdm9zIGVuZmFudHMgb3UgcXUnaWxzIGxpc2VudCBldXgtbcOqbWVzLCB2b3VzIHBvdXZleiBhY2PDqWRlciDDoCAqWzEwIHF1ZXN0aW9ucyBxdWUgdHUgZGV2cmFpcyB0ZSBwb3NlciBzdXIgbGUgY2hyaXN0aWFuaXNtZV0oaHR0cHM6Ly9ibGZzdG9yZS5jb20vcHJvZHVjdHMvMTAtcXVlc3Rpb25zLXF1ZS10dS1kZXZyYWlzLXRlLXBvc2VyLXN1ci1sZS1jaHJpc3RpYW5pc21lP3ZhcmlhbnQ9NDI1ODE5MjQ5Mzc5MjYpKlsgXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy8xMC1xdWVzdGlvbnMtcXVlLXR1LWRldnJhaXMtdGUtcG9zZXItc3VyLWxlLWNocmlzdGlhbmlzbWU/dmFyaWFudD00MjU4MTkyNDkzNzkyNilwYXIgbGUgYmlhaXMgZCd1biBbbGl2cmUgYXVkaW8sXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9hdWRpby0xMC1xdWVzdGlvbnMtcXVlLXR1LWRldnJhaXMtdGUtcG9zZXItc3VyLWxlLWNocmlzdGlhbmlzbWUpIGQndW4gW2xpdnJlIG51bcOpcmlxdWVdKGh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzL2Vib29rLTEwLXF1ZXN0aW9ucy1xdWUtdHUtZGV2cmFpcy10ZS1wb3Nlci1zdXItbGUtY2hyaXN0aWFuaXNtZSkgb3UgZCd1biBbbGl2cmUgYnJvY2jDqV0oaHR0cHM6Ly9ibGZzdG9yZS5jb20vcHJvZHVjdHMvMTAtcXVlc3Rpb25zLXF1ZS10dS1kZXZyYWlzLXRlLXBvc2VyLXN1ci1sZS1jaHJpc3RpYW5pc21lKS4gVHJvdXZleiBsZSBtb3llbiBxdWkgY29udmllbnQgw6Agdm90cmUgZmFtaWxsZSBwb3VyIG1ldHRyZSBjZSBsaXZyZSBlbnRyZSBsZXMgbWFpbnMgZGUgdm9zIGpldW5lcy4gVm91cyBuZSBsZSByZWdyZXR0ZXJleiBwYXMuIiwiYXV0aG9yIjp7ImZ1bGxOYW1lIjoiQW5naWUgVmVsYXNxdWV6IFRob3JudG9uIiwicGljdHVyZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L0lNR18yNzI1XzRhN2RmZDA4NzMuanBlZyIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn19LCJpbWFnZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L0NPVkVSXzEwcXVldHVkZXZyYWlzdGVwb3NlcnN1cmxlY2hyaXN0aWFuaXNtZTlfQkFUXzNfODI3eDEyNDBfYjNkNDRjZmQzMy53ZWJwIiwiaGVpZ2h0IjoxMjQwLCJ3aWR0aCI6ODI3LCJhbHRlcm5hdGl2ZVRleHQiOiIiLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9LCJ0b3BpY3MiOlt7Im5hbWUiOiJUaMOpb2xvZ2llIFByYXRpcXVlIn0seyJuYW1lIjoiRW5mYW5jZSBldCBqZXVuZXNzZSJ9LHsibmFtZSI6IlJlY2Vuc2lvbiBkZSBsaXZyZSJ9XSwibW9kdWxlcyI6W3siX190eXBlbmFtZSI6IkNvbXBvbmVudE1vZHVsZUxlYWQiLCJjb250ZW50IjoiQXUgY2FzIG/DuSBjZWxhIG4nYXVyYWl0IHBhcyBlbmNvcmUgw6l0w6kgZMOpbW9udHLDqSwgamUgc3VpcyB1bmUgZ3JhbmRlIGZhbiBkZXMgw6ljcml0cyBkZSBSZWJlY2NhIE1jTGF1Z2hsaW4uIFNvbiBzdHlsZSBjaGFsZXVyZXV4IGV0IHNhZ2UgcydhZHJlc3NlIGF1c3NpIGJpZW4gYXV4IGNyb3lhbnRzIHF1J2F1eCBub24tY3JveWFudHMsIGF1eCBqZXVuZXMgcXUnYXV4IG1vaW5zIGpldW5lcy4gTGEgdmVyc2lvbiBwb3VyIGFkb2xlc2NlbnRzIGRlIHNvbiBsaXZyZSAqWzEyIHJhaXNvbnMgZGUgbmUgcGx1cyBjcm9pcmUgYXUgY2hyaXN0aWFuaXNtZSBldCBwb3VycXVvaSB5IGNyb2lyZSBlbmNvcmUsXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy8xMi1yYWlzb25zLWRlLW5lLXBsdXMtY3JvaXJlLWF1LWNocmlzdGlhbmlzbWU/dmFyaWFudD00MjQ3NDE5NjYzMTc1MCkqIHF1aSBhIHJlw6d1IGxlcyDDqWxvZ2VzIGRlIGxhIHByZXNzZSwgZW4gZXN0IHVuIGJvbiBleGVtcGxlLiBDZSBkZXJuaWVyIGVzdCBkZXN0aW7DqSDDoCB1biBwdWJsaWMgYWR1bHRlIGV0IGNvbXBvcnRlIGNlcnRhaW5lcyBpbGx1c3RyYXRpb25zIHF1aSBuZSBjb252aWVuZHJhaWVudCBwYXMgw6AgZGVzIGpldW5lcyAoY29tbWUgbGUgdmlvbCBjb2xsZWN0aWYgZGUgTmFkaWEgTXVyYWQgcGFyIGwnSVNJUykuIExhIHByZW1pw6hyZSBzJ2FwcHVpZSBmb3J0ZW1lbnQgc3VyIGRlcyBpbGx1c3RyYXRpb25zIGRlIERpc25leSwgZHUgTW9uZGUgZGUgTmFybmlhLCBkdSBTZWlnbmV1ciBkZXMgQW5uZWF1eCBldCBkZSBIYXJyeSBQb3R0ZXIuIFJpZW4gcXVlIHBvdXIgY2VsYSwgbWVzIGZpbGxlcyBvbnQgw6l0w6kgZW1iYWxsw6llcyBkw6hzIGxlcyBwcmVtacOocmVzIHBhZ2VzIGRlIGwnb3V2cmFnZS4ifV19LHsiaWQiOiI0NDEiLCJ0aXRsZSI6IlBvdXJxdW9pIERpZXUgbm91cyBhLXQtaWwgZG9ubsOpIHNhIExvaT8iLCJzbHVnIjoiY29tbWVudC1jcm95YW50LWNvbXByZW5kcmUtZXhvZGUtMiIsInR5cGUiOiJhcnRpY2xlIiwicHVibGlzaGVkX2F0IjoiMjAyNC0wOS0xMlQwMzo0NTowMC4wMDBaIiwiYm9keSI6IiMjIExhIExvaSBkb25uw6llIGF1eCBwaWVkcyBkdSBNb250IFNpbmHDrzogRXhvZGUgMTktMzFcblxuSWwgZXN0IGltcG9ydGFudCBkZSBjb21wcmVuZHJlLCBkw6hzIGxlIGTDqWJ1dCwgcXVlIGxhIExvaSBu4oCZZXN0IHBhcyB1bmUgc2ltcGxlIGxpc3RlIGRlIHLDqGdsZXMsIG3Dqm1lIHNpIGVsbGUgZW4gY29udGllbnQgcGFzIG1hbC4gQ2FyIHVuZSBsb25ndWUgbGlzdGUgZGUgY2hvc2VzIMOgIGZhaXJlIGV0IMOgIG5lIHBhcyBmYWlyZSBu4oCZYXBwb3J0ZSBwYXMgbGEgam9pZSwgbuKAmWVzdC1jZSBwYXM/IEzigJnDiXRlcm5lbCBhdmFpdCB1biBvYmplY3RpZiBiaWVuIHBsdXMgZ3JhbmQgcG91ciBzYSBMb2kuIERpZXUgbOKAmWEgY29uw6d1ZSBwb3VyIG1vbnRyZXIgw6Agc29uIHBldXBsZSBsYSBqdXN0ZSBmYcOnb24gZGUgdml2cmUgZXQgZGUgbGUgc2VydmlyIGFmaW4gZGUgam91aXIgZGUgbGEgY29tbXVuaW9uIGF2ZWMgbHVpLlxuXG5O4oCZZXN0LWNlIHBhcyBwYXJlaWwgcG91ciBub3VzPyBEaWV1IG5lIG5vdXMgYSBwYXMgZG9ubsOpIGxlcyDDiWNyaXR1cmVzIGNvbW1lIHVuIGZhcmRlYXUgcMOpbmlibGUgw6AgcG9ydGVyLiBTYSBQYXJvbGUgZXN0IHBsdXTDtHQgdW4gZG9uIGRlIG5vdHJlIFDDqHJlIGPDqWxlc3RlIHF1aSBtYW5pZmVzdGUgc29uIGFtb3VyIGVuIHNlIHLDqXbDqWxhbnQgw6Agbm91cywgdG91dCBlbiBub3VzIG1vbnRyYW50IGNvbW1lbnQgdml2cmUgZOKAmXVuZSBtYW5pw6hyZSBxdWkgbGUgZ2xvcmlmaWUuXG5cblBlbnNvbnMgYXV4IGxvaXMgc2FuaXRhaXJlczogYXZhbnQgbGEgcGFuZMOpbWllLCBlbGxlcyBub3VzIHNlbWJsYWllbnQgcGV1dC3DqnRyZSBkdXJlcyBldCBhcmNoYcOvcXVlcy4gTWFpcyBtYWludGVuYW50IHF1ZSBub3VzIHZveW9ucyDDoCBxdWVsIHBvaW50IGlsIGVzdCBmYWNpbGUgcG91ciBsZXMgbWFsYWRpZXMgZGUgc2UgcHJvcGFnZXIsIG5vdXMgY29tcHJlbm9ucyBsYSBzYWdlc3NlIGRlIERpZXUgbG9yc3F14oCZaWwgb3Jkb25uZSBsYSBxdWFyYW50YWluZSBkZXMgbWFsYWRlcywgZXQgbGEgcHVyaWZpY2F0aW9uIGRlcyBtYWlzb25zIGV0IGRlcyB1c3RlbnNpbGVzIGluZmVjdMOpcy5cblxuIyMjIExlIGRvbiBkdSBzYWx1dCBwcsOpY8OoZGUgbGUgZG9uIGRlIGxhIExvaVxuXG5EYW5zIGxlIGNoYXBpdHJlIDE5IGRlIGzigJlFeG9kZSwgbm91cyB2b3lvbnMgcXVlIERpZXUgc2NlbGxlIGzigJlhZG9wdGlvbiBkZXMgZW5mYW50cyBk4oCZSXNyYcOrbCBwYXIgdW5lIGFsbGlhbmNlIGF1IFNpbmHDry4gTGUgcmVzdGUgZHUgbGl2cmUgYSBsaWV1IGzDoCwgYXV4IHBpZWRzIGRlIGxhIG1vbnRhZ25lIHNhaW50ZS4gRXQgdm9pY2kgY2UgcXVpIGVzdCBleHRyYW9yZGluYWlyZSwgY2UgcXVpIHJldGllbnQgbm90cmUgc291ZmZsZTogRGlldSBkw6lsaXZyZSBzb24gcGV1cGxlIGRlIGzigJllc2NsYXZhZ2UsIERpZXUgYWRvcHRlIHNvbiBwZXVwbGUsIERpZXUgZmFpdCBhbGxpYW5jZSBhdmVjIHNvbiBwZXVwbGUgKmF2YW50KiBkZSBsZXVyIGRvbm5lciBsYSBMb2khIFNvbiBzYWx1dCBlc3QgaW5jb25kaXRpb25uZWwgZXQgcGFyIHNvbiBpbml0aWF0aXZlISBMYSBzZXVsZSBjaG9zZSBxdeKAmWlsIGFpdCBleGlnw6llIGTigJlldXgsIGPigJllc3QgbGUgc2FuZyBkZSBs4oCZYWduZWF1LiBD4oCZZXN0IHNldWxlbWVudCBhcHLDqHMgY2V0IGFjY29yZCBlbiBFeG9kZSAxOSBxdWUgRGlldSBkb25uZSBzYSBMb2kgYXUgY2hhcGl0cmUgMjAuXG5cblF14oCZZXN0LWNlIHF1ZSBjZWxhIGltcGxpcXVlIHBvdXIgbm91cz8gRGlldSBuZSBub3VzIGEtdC1pbCBwYXMgw6lnYWxlbWVudCBzYXV2w6lzIGRlIHNhIHByb3ByZSBpbml0aWF0aXZlIGV0IGRlIGZhw6dvbiBpbmNvbmRpdGlvbm5lbGxlPyBDZSBu4oCZZXN0IHF14oCZYXByw6hzIG5vdHJlIGNvbnZlcnNpb24gcXXigJlpbCBub3VzIGEgYXBwZWzDqXMgw6AgbGEgc2FpbnRldMOpLCBjYXIgaWwgbm91cyDDqXRhaXQgaW1wb3NzaWJsZSBkZSB2aXZyZSBkZXMgdmllcyBhc3NleiBzYWludGVzIHBvdXIgcGxhaXJlIMOgIERpZXUgcGFyIG5vcyBwcm9wcmVzIGVmZm9ydHMuIElsIG5vdXMgYSBmYWxsdSB1biBzdWJzdGl0dXQsIGzigJlBZ25lYXUgZGUgRGlldSwgcG91ciBwcmVuZHJlIGxlIGp1Z2VtZW50IGRlIERpZXUgw6Agbm90cmUgcGxhY2UuIENlIG7igJllc3QgcXXigJlhcHLDqHMgYXZvaXIgZmFpdCBkZSBub3VzIHNlcyBlbmZhbnRzIHF14oCZaWwgbm91cyBhIGRvbm7DqSBzYSBMb2kgZXQgbm91cyBhIGFwcGVsw6kgw6AgdW5lIG5vdXZlbGxlIHZpZSBk4oCZb2LDqWlzc2FuY2UuXG5cbiMjIyBMZXMgMTAgcGFyb2xlc1xuXG5EYW5zIGxlIGNoYXBpdHJlIDIwLCBEaWV1IGRvbm5lIMOgIHNvbiBwZXVwbGUgbGVzIDEwIGNvbW1hbmRlbWVudHMuIEVuIGjDqWJyZXV4LCBpbHMgc29udCBhcHBlbMOpcyBcImxlcyAxMCBwYXJvbGVzXCIuIFBvdXIgc2UgY29tcHJlbmRyZSwgb24gdXRpbGlzZSBsYSBwYXJvbGUuIExhIGZvbmN0aW9uIHByZW1pw6hyZSBkZXMgMTAgY29tbWFuZGVtZW50cyBlc3QgZGUgZmFpcmUgY29ubmHDrnRyZSBhdSBwZXVwbGUgcXVpIGVzdCBjZSBEaWV1IHF1aSBsZXMgYSBzYXV2w6lzLiBD4oCZZXN0IHBvdXJxdW9pIEV4b2RlIDIwLjEgZXN0IGludHJvZHVpdCBhaW5zaTogKuKAnERpZXUgcHJvbm9uw6dhIHRvdXRlcyBjZXMgcGFyb2xlcy7igJ0qIENlbGEgbmUgZGl0IHBhczogKuKAnERpZXUgcHJvbm9uw6dhIHRvdXRlcyBjZXMgbG9pcy9jZXMgY29tbWFuZGVtZW50cy7igJ0qIFNvbiBidXQgw6l0YWl0IHF14oCZSXNyYcOrbCBjb25uYWlzc2Ugc29uIExpYsOpcmF0ZXVyLlxuXG5EZSBwbHVzLCBsYSBMb2kgbmUgcGFybGUgcGFzIGRlIG5vdXM6IGVsbGUgY29tbWVuY2UgcGFyIERpZXUuIEVsbGUgZXN0IHN1cnRvdXQgbGEgcsOpdsOpbGF0aW9uIGRlIHF1aSAqaWwgZXN0Ki4gQ+KAmWVzdCBsZSBtb3llbiBkZSBsZSBjb25uYcOudHJlLCBsdWksIGV0IGRlIGNvbXByZW5kcmUgY29tbWVudCBsYSB2aWUgZHUgcGV1cGxlIHJlZmzDqHRlIGxhIG5hdHVyZSwgbGUgY2FyYWN0w6hyZSwgZXQgbGVzIHZhbGV1cnMgZGUgRGlldS48c3VwPjE8L3N1cD5cblxuTGUgYnV0IHN1cHLDqm1lIGR1IGxpdnJlIGTigJlFeG9kZSwgZHUgUGVudGF0ZXVxdWUsIGV0IGRlIHRvdXRlIGzigJnDiWNyaXR1cmUgZXN0IGRvbmMgZGUgZmFpcmUgY29ubmHDrnRyZSBEaWV1LiBVbmUgZm9pcyBxdWUgbGVzIFwiZGl4IHBhcm9sZXNcIiBzb250IGRvbm7DqWVzLCBEaWV1IMOpbGFyZ2l0IGVuc3VpdGUgbGV1ciBzaWduaWZpY2F0aW9uIGVuIGVudHJhbnQgZGFucyBsZXMgZMOpdGFpbHMgZGUgbWFuacOocmUgcHJhdGlxdWUuXG5cbiMjIExhIGzDqWdpc2xhdGlvbiBzb2NpYWxlOiBFeG9kZSAyMS0yNFxuXG5EYW5zIGxlcyBjaGFwaXRyZXMgMjEtMjQsIG5vdXMgdHJvdXZvbnMgdW5lIHZhcmnDqXTDqSBkZSBsb2lzIG1vcmFsZXMsIHJlbGF0aXZlcyDDoCBsYSB2aWUgZGFucyBsYSBjb21tdW5hdXTDqSBkZSBmb2kuIFBhciBleGVtcGxlLCBsZSB0cmFpdGVtZW50IGFkw6lxdWF0IGRlcyBlc2NsYXZlcywgZGVzIMOpdHJhbmdlcnMsIGV0IG3Dqm1lIGRlcyBjcmltaW5lbHMuIFNpIGNlcnRhaW5lcyBkZSBjZXMgbG9pcyBwZXV2ZW50IHNlbWJsZXIgbXlzdMOpcmlldXNlcyBwb3VyIHVuIGxlY3RldXIgbW9kZXJuZSwgZWxsZXMgb250IMOpdMOpIGNvbsOndWVzIHBvdXIgcHJvdMOpZ2VyIGxlcyBwbHVzIHZ1bG7DqXJhYmxlcyDDoCB1bmUgw6lwb3F1ZSBkYW5nZXJldXNlLCBkdXJhbnQgbGFxdWVsbGUgY2hhcXVlIGhvbW1lIMOpdGFibGlzc2FpdCBzYSBwcm9wcmUgbG9pLiBBaW5zaSwgbGUgcHJpbmNpcGUgXCLFk2lsIHBvdXIgxZNpbFwiIMOpdGFpdCBjb27Dp3UgY29tbWUgdW5lIG1pc8Opcmljb3JkZSwgcG91ciBlbXDDqmNoZXIgdW4gdXNhZ2UgZXhjZXNzaWYgZGUgbGEgZm9yY2Ugw6AgbOKAmcOpZ2FyZCBkdSBjb3VwYWJsZS4gTOKAmcOJdGVybmVsIGludHJvZHVpc2l0IHVuZSBsw6lnaXNsYXRpb24gc29jaWFsZSBxdWkgbHVpIGVzdCBwcm9wcmUgZXQgcXXigJlpbCBkw6l2ZWxvcHBlcmEgZGFucyBsZSByZXN0ZSBkdSBQZW50YXRldXF1ZS5cblxuIyMgRGVzIGluc3RydWN0aW9ucyBwb3VyIGxhIGNvbnN0cnVjdGlvbiBkdSB0YWJlcm5hY2xlOiBFeG9kZSAyNS0zMVxuXG5EYW5zIGxlcyBjaGFwaXRyZXMgMjUtMzEsIERpZXUgZG9ubmUgZGVzIGluc3RydWN0aW9ucyBwb3VyIGxhIDxhIGhyZWY9XCIvYXJ0aWNsZS90YWJlcm5hY2xlLWNocmV0aWVuLWV4b2RlLTQwL1wiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj5jb25zdHJ1Y3Rpb24gZHUgdGFiZXJuYWNsZTwvYT4sIGFpbnNpIHF1ZSBkZXMgZMOpdGFpbHMgbWludXRpZXV4IHN1ciBsZXMgdsOqdGVtZW50cyBxdeKAmUFhcm9uLCBsZSBncmFuZCBwcsOqdHJlLCBldCBzZXMgZmlscyBkZXZhaWVudCBwb3J0ZXIuIENvbW1lbnQgcG91dm9ucy1ub3VzIGNvbXByZW5kcmUgY2VsYSDDoCBsYSBsdW1pw6hyZSBkZSBsYSBub3V2ZWxsZSBhbGxpYW5jZT9cblxuIyMjIErDqXN1czogbm90cmUgR3JhbmQgUHLDqnRyZVxuXG5QZW5zb25zIGF1eCB2w6p0ZW1lbnRzIHNhY2VyZG90YXV4IGTigJlBYXJvbiBlbiBFeG9kZSAyODogbGUgcGVjdG9yYWwsIGzigJnDqXBob2QsIGxhIHJvYmUsIGxhIHR1bmlxdWUgYnJvZMOpZSwgbGEgdGlhcmUsIGV0IGxhIGNlaW50dXJlLiBFbiBIw6licmV1eCAyLjE3IGV0IDQuMTQsIErDqXN1cyBlc3QgYXBwZWzDqSBub3RyZSBHcmFuZCBQcsOqdHJlLiBEYW5zIGNlcyBwYXNzYWdlcywgSsOpc3VzIGVzdCBjb21wYXLDqSDDoCBBYXJvbiBldCDDoCBzZXMgZGVzY2VuZGFudHMuIEzigJlhcmd1bWVudCBkZSBjZXMgcGFzc2FnZXMsIGV0IGRlIHRvdXQgbGUgbGl2cmUgZGVzIEjDqWJyZXV4LCBlc3QgcXVlIErDqXN1cyBlc3QgZGUgbG9pbiBzdXDDqXJpZXVyIMOgIHRvdXMgY2V1eCBxdWkgbOKAmW9udCBwcsOpY8OpZMOpIVxuXG5Ob3VzIHBvdXZvbnMgw6lnYWxlbWVudCBwZW5zZXIgYXUgcGVjdG9yYWwgZHUganVnZW1lbnQsIGF2ZWMgc2VzIGRvdXplIHBpZXJyZXMgcXVpIHJlcHLDqXNlbnRlbnQgbGVzIGRvdXplIHRyaWJ1cyBk4oCZSXNyYcOrbC4gTG9yc3F1ZSBsZSBncmFuZCBwcsOqdHJlIGxlIHBvcnRhaXQgcHLDqHMgZGUgc29uIGPFk3VyLCBjZWxhIHN5bWJvbGlzYWl0IHNvbiBpbnRlcmNlc3Npb24gcG91ciBsZXMgZG91emUgdHJpYnVzIGF1cHLDqHMgZGUgbOKAmcOJdGVybmVsLiBIw6licmV1eCA3LjI1IGTDqWNsYXJlLCBlbiBjZSBxdWkgY29uY2VybmUgbGUgcsO0bGUgZGUgSsOpc3VzIGNvbW1lIGdyYW5kIHByw6p0cmUsIHF14oCZaWwgdml0IHRvdWpvdXJzIHBvdXIgaW50ZXJjw6lkZXIgZW4gbm90cmUgZmF2ZXVyLiBKw6lzdXMgbm91cyBnYXJkZSBhdXNzaSBwcsOocyBkZSBzb24gY8WTdXIhXG5cbiMjIyBMZXMgY3JveWFudHM6IHVuIHNhY2VyZG9jZSByb3lhbFxuXG5FbmZpbiwgbG9yc3F1ZSBs4oCZb24gY2hlcmNoZSBDaHJpc3QgYXZhbnQgdG91dCwgbm91cyBwb3V2b25zIGFsb3JzIG5vdXMgaWRlbnRpZmllciBkYW5zIGNlIHBhc3NhZ2UuIEVuIEV4b2RlIDE5LjYsIGzigJnDiXRlcm5lbCBhcHBlbGxlIHRvdXRlIGxhIG5hdGlvbiDDoCDDqnRyZSBcInVuIHNhY2VyZG9jZSByb3lhbFwiLCBldCBwYXMgc2V1bGVtZW50IGxlcyBmaWxzIGTigJlBYXJvbi4gQ2VsYSBzaWduaWZpZSBxdeKAmUlzcmHDq2wgZGV2YWl0IHJlcHLDqXNlbnRlciBs4oCZw4l0ZXJuZWwgYXVwcsOocyBkZXMgbmF0aW9ucyBldCB0ZW50ZXIgZGUgbGVzIGFtZW5lciDDoCBsdWkuIDEgUGllcnJlIDIuOSByZXByZW5kIGNlcyBtb3RzIGRlIGzigJlFeG9kZSBwb3VyIGTDqWNyaXJlIGzigJnDiWdsaXNlLiBcblxuQWxvcnMsIGNvbW1lbnQgc2UgbGVzIGFwcGxpcXVlciBhdWpvdXJk4oCZaHVpP8KgRW4gcmVjb25uYWlzc2FudCBxdWUgbm91cywgbOKAmcOJZ2xpc2UsIGRhbnMgbGEgcHVpc3NhbmNlIGRlIGzigJlFc3ByaXQsIGRldm9ucyDDqnRyZSBhdXNzaSB1bmUgbHVtacOocmUgcG91ciBsZXMgbmF0aW9ucy4gTm91cyBkZXZvbnMgaW50ZXJjw6lkZXIgcG91ciBldXggY29tbWUgQ2hyaXN0IGludGVyY8OoZGUgcG91ciBub3VzLlxuXG48YnI+XG5cbiMjIFBvdXIgYWxsZXIgcGx1cyBsb2luXG5cbiogRMOpY291dnJleiBsZSBkZXV4acOobWUgw6lwaXNvZGUgZGUgbm90cmUgbm91dmVsbGUgc8OpcmllICpDaHLDqXRpZW5uZSosIGVuIGxpZW4gYXZlYyBjZXQgYXJ0aWNsZTogPGEgaHJlZj1cIi9wb2RjYXN0cy9jaHJldGllbm5lL2ludHJvZHVjdGlvbi1leG9kZS1wZXVwbGUtYWRvcHRlXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPlwiSW50cm9kdWN0aW9uIMOgIGwnRXhvZGU6IFVuIHBldXBsZSBhZG9wdMOpIChFeG9kZSAxOS00MClcIjwvYT4uXG4qIFZvaWNpIGxlIHByZW1pZXIgYXJ0aWNsZSBkZSBjZXR0ZSBzw6lyaWU6IDxhIGhyZWY9XCIvYXJ0aWNsZS9jb21tZW50LWNyb3lhbnQtY29tcHJlbmRyZS1leG9kZS0xXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPlwiTCdFeG9kZTogcsOpY2l0IGRlIGxhIG5haXNzYW5jZSBsYSBwbHVzIGltcG9ydGFudGUgZGUgbCdBbmNpZW4gVGVzdGFtZW50XCI8L2E+XG4qIEplIHN1aXMgcmVkZXZhYmxlIGVudmVycyBKb25hdGhhbiBTcGVuY2VyIGV0IHNvbiBlbnNlaWduZW1lbnQgc3VyIGxlIGxpdnJlIGRlIGzigJlFeG9kZS4gRMOpY291dnJleiBzb24gd2ViaW5haXJlOiA8YSBocmVmPVwiL3dlYmluYWlyZXMvY29tcHJlbmRyZS1leG9kZS9cIiB0YXJnZXQ9XCJfYmxhbmtcIiByZWw9XCJub3JlZmVycmVyIG5vb3BlbmVyXCI+MWggcG91ciBjb21wcmVuZHJlIEV4b2RlPC9hPlxuKiBgPGRpdj5Ew6ljb3V2cmV6IMOpZ2FsZW1lbnQgc3VyIG1hIGNoYWluZSBZb3VUdWJlLCA8YSBocmVmPVwiaHR0cHM6Ly95b3V0dS5iZS9lV0cxbFY2dnE3Z1wiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj51bmUgw6l0dWRlIGJpYmxpcXVlPC9hPiBxdWUgauKAmWFpIGFwcG9ydMOpZSBsb3JzIGTigJl1bmUgcmV0cmFpdGUgcG91ciBmZW1tZXMgYXUgUXXDqWJlYy48L2Rpdj5gXG5cbjxzdWI+QXJ0aWNsZSBwdWJsacOpIHBvdXIgbGEgcHJlbWnDqHJlIGZvaXMgbGUgMTggZsOpdnJpZXIgMjAyMS4gSmUgbOKAmWFpIHJlbWlzIGVuIGF2YW50IGxlIDEyIHNlcHRlbWJyZSAyMDI0IMOgIGzigJlvY2Nhc2lvbiBkdSBsYW5jZW1lbnQgZGUgbGEgPGEgaHJlZj1cIi9hcnRpY2xlL3NvbW1haXJlLXBvZGNhc3QtY2hyZXRpZW5uZSNleG9kZVwiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj5ub3V2ZWxsZSBzw6lyaWUgZGUgbm90cmUgcG9kY2FzdCA8ZW0+Q2hyw6l0aWVubmU8L2VtPjwvYT4uPC9zdWI+IiwiYXV0aG9yIjp7ImZ1bGxOYW1lIjoiQW5naWUgVmVsYXNxdWV6IFRob3JudG9uIiwicGljdHVyZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L0lNR18yNzI1XzRhN2RmZDA4NzMuanBlZyIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn19LCJpbWFnZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L3RpbmdleV9pbmp1cnlfbGF3X2Zpcm1fRF9acGM0X1VZXzhfWnRfWV91bnNwbGFzaF9zY2FsZWRfZTE2MTM1OTQxNjgzNTVfZDgxZGFmMTM0Ny5qcGciLCJoZWlnaHQiOjczMywid2lkdGgiOjExMDAsImFsdGVybmF0aXZlVGV4dCI6bnVsbCwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifSwidG9waWNzIjpbeyJuYW1lIjoiSGVybcOpbmV1dGlxdWUifSx7Im5hbWUiOiJQZW50YXRldXF1ZSJ9LHsibmFtZSI6IkxvaSJ9XSwibW9kdWxlcyI6W3t9LHsiX190eXBlbmFtZSI6IkNvbXBvbmVudE1vZHVsZUxlYWQiLCJjb250ZW50IjoiRGFucyBbbOKAmWFydGljbGUgcHLDqWPDqWRlbnRdKC9jb21tZW50LWNyb3lhbnQtY29tcHJlbmRyZS1leG9kZS0xLyksIG5vdXMgYXZvbnMgYXNzaXN0w6kgw6AgbGEgcGx1cyBncmFuZGUgbmFpc3NhbmNlIGRlIGzigJlhbmNpZW5uZSBhbGxpYW5jZSwgY2VsbGUgZGUgbGEgbmF0aW9uIGTigJlJc3Jhw6tsIGRhbnMgbOKAmUV4b2RlLiBEYW5zIGxhIGRldXhpw6htZSBtb2l0acOpIGR1IGxpdnJlLCBub3VzIHZveW9ucyBjb21tZW50IGxlIHRlbXBzIGV0IGxlIG1vdXZlbWVudCBzJ2FycsOqdGVudCBhdXggcGllZHMgZHUgTW9udCBTaW5hw68gYWxvcnMgcXVlIERpZXUgZG9ubmUgw6Agc29uIHBldXBsZSBzYSBzYWludGUgTG9pLiJ9XX0seyJpZCI6IjQ0NyIsInRpdGxlIjoiTCdFeG9kZTogcsOpY2l0IGRlIGxhIG5haXNzYW5jZSBsYSBwbHVzIGltcG9ydGFudGUgZGUgbCdBbmNpZW4gVGVzdGFtZW50Iiwic2x1ZyI6ImNvbW1lbnQtY3JveWFudC1jb21wcmVuZHJlLWV4b2RlLTEiLCJ0eXBlIjoiYXJ0aWNsZSIsInB1Ymxpc2hlZF9hdCI6IjIwMjQtMDktMDVUMDM6NDU6MDAuMDAwWiIsImJvZHkiOiLDgCB0cmF2ZXJzIGxlcyBmbMOpYXV4IGRlIGwnw4lneXB0ZSwgbm91cyB2b3lvbnMgdW5lIGltYWdlIHZpdmFudGUgZGVzIGRvdWxldXJzIGRlIGwnZW5mYW50ZW1lbnQuIER1cmFudCBsJ2V4b2RlLCBtYXJxdcOpIHBhciBsYSB0cmF2ZXJzw6llIGRlIGxhIG1lciBSb3VnZSwgbm91cyBhc3Npc3RvbnMgYXUgbW9tZW50IG3Dqm1lIG/DuSBsYSBcImJhbmRlIGTigJlow6licmV1eFwiIGRldmllbnQgdW5lIG5hdGlvbiBzaW5ndWxpw6hyZS5cblxuUXVlbGxlIGVzdCBs4oCZaW1wb3J0YW5jZSBkZSBjZXMgw6l2w6luZW1lbnRzIGV0IGRlIGNldXggcXVpIHN1aXZlbnQgZGFucyBsZSBsaXZyZSBkZSBs4oCZRXhvZGU/IFBhdWwgcsOpcG9uZCDDoCBjZXR0ZSBxdWVzdGlvbiBkYW5zIDEgQ29yaW50aGllbnM6XG5cbj4gKkNlcyBjaG9zZXMgbGV1ciBzb250IGFycml2w6llcyBwb3VyIHNlcnZpciBk4oCZZXhlbXBsZXMsIGV0IGVsbGVzIG9udCDDqXTDqSDDqWNyaXRlcyBwb3VyIG5vdHJlIGluc3RydWN0aW9uLCDDoCBub3VzIHF1aSBzb21tZXMgcGFydmVudXMgw6AgbGEgZmluIGRlcyBzacOoY2xlcy4gQWluc2kgZG9uYywgcXVlIGNlbHVpIHF1aSBjcm9pdCDDqnRyZSBkZWJvdXQgcHJlbm5lIGdhcmRlIGRlIHRvbWJlciEqXG4+XG4+IDxzdWI+PGNpdGU+MSBDb3JpbnRoaWVucyAxMC4xMS0xMjwvY2l0ZT48L3N1Yj5cblxuQWluc2ksIGNlcyByw6ljaXRzIGRlIGzigJlBbmNpZW4gVGVzdGFtZW50IG5lIHNvbnQgcGFzIGRlcyBoaXN0b2lyZXMgZMOpcGFzc8OpZXMgc2FucyBwZXJ0aW5lbmNlIHBvdXIgbm9zIHZpZXMgYXVqb3VyZOKAmWh1aS4gQXUgY29udHJhaXJlLCBpbHMgb250IGJlYXVjb3VwIMOgIG5vdXMgYXBwcmVuZHJlLCBzaSBub3VzIHByZW5vbnMgbGUgdGVtcHMgZGUgY3JldXNlciBlbiBwcm9mb25kZXVyIGV0IGRlIGNvbXByZW5kcmUgbGV1ciBtZXNzYWdlIHVuaXZlcnNlbC5cblxuIyMgTGEgZMOpbGl2cmFuY2U6IEV4b2RlIDEtMTVcblxuUGVuc29ucyDDoCBFeG9kZSAxLiBPw7kgY29tbWVuY2UgbGUgbGl2cmU/IERhbnMgcXVlbGxlcyBjaXJjb25zdGFuY2VzwqBzZSB0cm91dmUgbGUgcGV1cGxlIGRlIERpZXUgw6AgY2UgbW9tZW50PyBPw7kgZXN0IERpZXU/IFF1ZSBz4oCZZXN0LWlsIHBhc3PDqSBhdmVjIGxlcyBwcm9tZXNzZXMgZmFpdGVzIMOgIEFicmFoYW0gZXQgc2EgcG9zdMOpcml0w6kgdG91dCBhdSBsb25nIGR1IGxpdnJlIGRlIGxhIEdlbsOoc2U/XG5cbkxlIGxpdnJlIGRlIGzigJlFeG9kZSBz4oCZb3V2cmUgbMOgIG/DuSBsZSBsaXZyZSBkZSBsYSBHZW7DqHNlIHNlIGNsw7R0dXJlLCBhdmVjIGxhIGfDqW7DqWFsb2dpZSBkZXMgZGVzY2VuZGFudHMgZGUgSmFjb2IgYXJyaXbDqXMgZGUgQ2FuYWFuIGVuIMOJZ3lwdGUgw6AgbOKAmcOpcG9xdWUgZGUgSm9zZXBoLiBFbiBxdWVscXVlcyBnw6luw6lyYXRpb25zLCBsZXMgZmlscyBk4oCZQWJyYWhhbSBwYXNzZW50IGTigJllbnZpcm9uIDcwIMOgIHVuZSBtdWx0aXR1ZGUgc2kgaW1wb3J0YW50ZSBxdeKAmWVsbGUgZGV2aWVudCB1bmUgbWVuYWNlIHBvdXIgbGUgcGhhcmFvbi4gSWxzIG5lIHNvbnQgcGx1cyBjb25zaWTDqXLDqXMgZmF2b3JhYmxlbWVudCwgY29tbWUgw6AgbOKAmcOpcG9xdWUgZGUgSm9zZXBoLCBtYWlzIGRldmllbm5lbnQgbGVzIGVzY2xhdmVzIGRlIFBoYXJhb24uIE1hbGdyw6kgbGV1ciBjcnVlbGxlIG9wcHJlc3Npb24sIGlscyBjb250aW51ZW50IGRlIGNyb8OudHJlIGVuIG5vbWJyZSwgY2UgcXVpIGNvbmR1aXQgUGhhcmFvbiDDoCBvcmRvbm5lciBsZSBtZXVydHJlIGRlIHRvdXMgbGVzIGZpbHMgbcOibGVzIG7DqXMgZGVzIGjDqWJyZXV4LlxuXG5EYW5zIGxlcyBwcmVtaWVycyBjaGFwaXRyZXMsIG5vdXMgdm95b25zIGNvbW1lbnQgRGlldSByw6lwb25kIGF1eCBjcmlzIGRlIHNvbiBwZXVwbGUuIElsIGNob2lzaXQgdW4gcG9ydGUtcGFyb2xlIHLDqXRpY2VudCBub21tw6kgTW/Dr3NlLCBxdeKAmWlsIGVudm9pZSDDoCBQaGFyYW9uIGF2ZWMgdW4gc2ltcGxlIG1lc3NhZ2U6XG5cbj4gTGFpc3NlIGFsbGVyIG1vbiBwZXVwbGUsIGFmaW4gcXXigJlpbCBtZSBzZXJ2ZS5cbj5cbj4gPHN1Yj48Y2l0ZT5FeG9kZSA5LjE8L2NpdGU+PC9zdWI+XG5cbiMjIFNhdXbDqXMgcG91ciBzZXJ2aXJcblxuXCJTZXJ2aXJcIiBlc3QgbOKAmXVuIGRlcyBtb3RzLWNsw6lzIGR1IGxpdnJlIGRlIGzigJlFeG9kZS4gTm91cyB2b3lvbnMgcXVlIGPigJllc3QgbGUgYnV0IHBvdXIgbGVxdWVsIERpZXUgZmFpdCBzb3J0aXIgc29uIHBldXBsZSBkZSBs4oCZZXNjbGF2YWdlLiBJbCBwcmVuZCBjZXV4IHF1aSAqc2VydmFpZW50KiB1biByb2kgcGHDr2VuIHBvdXIgZW4gZmFpcmUgc2VzIGVuZmFudHMgYWZpbiBxdeKAmWlscyBsZSAqc2VydmVudCouIE1haXMgZW4gcXVvaSBjb25zaXN0ZSBjZSBzZXJ2aWNlPyBDb25zdHJ1aXJlIGRlcyB2aWxsZXMgcG91ciBEaWV1IGNvbW1lIGlscyBs4oCZYXZhaWVudCBmYWl0IHBvdXIgUGhhcmFvbj8gQ2VydGFpbmVtZW50IHBhcyEgRW4gZmFpdCwgRGlldSBwcm9tZXQgZGUgbGVzIGZhaXJlICplbnRyZXIgZGFucyB1biBwYXlzIG/DuSBjb3VsZW50IGxlIGxhaXQgZXQgbGUgbWllbCosIGF2ZWMgZGVzIHZpbGxlcyBjb25zdHJ1aXRlcyBwYXIgbGVzIG1haW5zIGRlcyBhdXRyZXMgKER0LiA2LjEwLTEyKS4gRGlldSBuZSBjaGVyY2hhaXQgcGFzIHVuZSBtYWluLWTigJnFk3V2cmUuIElsIGNoZXJjaGFpdCBkZXMgYWRvcmF0ZXVycy5cblxuSWwgZW4gZXN0IGRlIG3Dqm1lIGF1am91cmTigJlodWkuIE5vdXMgYXVzc2ksIG5vdXMgYXZvbnMgw6l0w6kgZMOpbGl2csOpcyBkZSBs4oCZZXNjbGF2YWdlIGTigJl1biBtYcOudHJlIGR1ciBldCBjcnVlbC4gU2Vsb24gMSBKZWFuIDMuMTAsIG5vdXMgw6l0aW9ucyBhdXRyZWZvaXMgZW5mYW50cyBkdSBkaWFibGUsIHNlcnZhbnQgbOKAmWVubmVtaSBkZSBub3Mgw6JtZXMgZXQgbGVzIGNvbnZvaXRpc2VzIGRlIGxhIGNoYWlyLCBhdSBsaWV1IGRlIHNlcnZpciBEaWV1LiBNYWlzIENocmlzdCBub3VzIGEgcmFjaGV0w6lzIHBvdXIgcXVlIG5vdXMgbGUgc2VydmlvbnMsIG5vbiBwbHVzIHNvdXMgdW4gam91ZyBkZSBww6ljaMOpLCBtYWlzIGNvbW1lIGVzY2xhdmVzIGRlIGp1c3RpY2UhIERhbnMgMiBDb3JpbnRoaWVucyA2LjE0LTcuMSwgUGF1bCBleGhvcnRlIGzigJnDiWdsaXNlIGRlIENvcmludGhlIMOgIHZpdnJlIGVuIHZ1ZSBkZXMgcHJvbWVzc2VzIGRlIERpZXUsIGZhaXRlcyBhdSBwZXVwbGUgZOKAmUlzcmHDq2wgZHVyYW50IGzigJlleG9kZSEgSWwgY2l0ZSBlbiBwYXJ0aWUgRXhvZGUgMjk6XG5cbj4gKkrigJloYWJpdGVyYWkgYXUgbWlsaWV1IGRlcyBlbmZhbnRzIGTigJlJc3Jhw6tsLCBldCBqZSBzZXJhaSBsZXVyIERpZXUuIElscyBjb25uYcOudHJvbnQgcXVlIGplIHN1aXMgbOKAmcOJdGVybmVsLCBsZXVyIERpZXUsIHF1aSBsZXMgYSBmYWl0IHNvcnRpciBkdSBwYXlzIGTigJnDiWd5cHRlLCBwb3VyIGhhYml0ZXIgYXUgbWlsaWV1IGTigJlldXguIEplIHN1aXMgbOKAmcOJdGVybmVsLCBsZXVyIERpZXUuKlxuPlxuPiA8c3ViIGlkPWZsZWF1eD48Y2l0ZT5FeG9kZSAyOS40NS00NjwvY2l0ZT48L3N1Yj5cblxuUXVlbCBwbHVzIGdyYW5kIHNlcnZpY2UgcXVlIGTigJlhZG9yZXIgbm90cmUgZ3JhbmQgRGlldSBldCBSb2k/IFF1ZWxsZSBncsOiY2UhXG5cbiMjIExlcyBkaXggZmzDqWF1eFxuXG5BaW5zaSwgcGFyIHVuZSBtYWluIHB1aXNzYW50ZSBldCB1biBicmFzIMOpdGVuZHUsIERpZXUgZMOpbGl2cmUgc29uIHBldXBsZSBkZSBs4oCZZXNjbGF2YWdlIGVuIMOJZ3lwdGUgKFBzIDEzNi4xMikuIENvbW1lbnQ/IFBhciB1bmUgc8OpcmllIGRlIGRpeCBmbMOpYXV4LlxuXG7DgCBwbHVzaWV1cnMgcmVwcmlzZXMsIFBoYXJhb24gcHJvbWV0IGRlIGxpYsOpcmVyIGxlIHBldXBsZSwgbWFpcyBpbCBjaGFuZ2UgZOKAmWF2aXMgZMOocyBxdWUgRGlldSBmYWl0IHByZXV2ZSBkZSBjb21wYXNzaW9uIGVuIG1ldHRhbnQgZmluIGF1IGZsw6lhdS4gRmluYWxlbWVudCwgY+KAmWVzdCBzdWl0ZSBhdSBkZXJuaWVyIGZsw6lhdXggcXVlIGxlIHBoYXJhb24gY8OoZGUuXG5cblZvdXMgc291dmVuZXotdm91cyBkZSBxdW9pIGlsIHPigJlhZ2lzc2FpdD8gTGEgbW9ydCBkZXMgcHJlbWllcnMtbsOpcyBkYW5zIHRvdXRlIGzigJnDiWd5cHRlLiBSYXBwZWxlei12b3VzLCBzb24gcHLDqWTDqWNlc3NldXJlIGF2YWl0IHLDqWR1aXQgw6AgbOKAmWVzY2xhdmFnZSBsZXMgaMOpYnJldXggZXQgb3Jkb25uw6kgbGUgbWV1cnRyZSBkZSBsZXVycyBmaWxzIG5vdXZlYXV4LW7DqXMgKEV4IDEuOC0xNiksIG1haXMgbGUgbm91dmVhdSBwaGFyYW9uIG7igJnDqXRhaXQgcGFzIHBsdXMgY2zDqW1lbnQgZW52ZXJzIGxlIHBldXBsZSBxdWkgc291ZmZyYWllbnQgZGFucyBsYSBzZXJ2aXR1ZGUgKEV4IDIuMjMpLiBD4oCZZXN0IGFsb3JzIHF1ZSBEaWV1IGx1aSDDtHRlIHNvbiBwcm9wcmUgZmlsczogc29uIGjDqXJpdGllci4gUGhhcmFvbiBhdmFpdCByZcOndSBuZXVmIGF2ZXJ0aXNzZW1lbnRzIGRvdWxvdXJldXgsIG1haXMgaWwgbGVzIGEgdG91cyBpZ25vcsOpcy5cblxuSWwgZW4gdmEgZGUgbcOqbWUgYXVqb3VyZOKAmWh1aS4gRGlldSB2ZWlsbGUgc3VyIHNlcyBlbmZhbnRzIGZhY2Ugw6AgbOKAmW9wcG9zaXRpb24sIMOgIGxhIHBlcnPDqWN1dGlvbiwgZXQgYXV4IGF0dGFxdWVzIHNwaXJpdHVlbGxlcy4gTm91cyBwb3V2b25zIG5vdXMgcmFwcGVsZXIgc2EgZmlkw6lsaXTDqSBlbnZlcnMgbGVzIGfDqW7DqXJhdGlvbnMgcGFzc8OpZXMgZXQgw6p0cmUgYXNzdXLDqXMgZGUgc2EgYmllbnZlaWxsYW5jZSBlbnZlcnMgbGEgbsO0dHJlLlxuXG4jIyBMYSBzdWJzdGl0dXRpb24gcMOpbmFsZVxuXG5RdeKAmWFycml2ZS10LWlsIGF1eCBmaWxzIHByZW1pZXJzLW7DqXMgZHUgcGV1cGxlIGRlIERpZXU/IElscyBzb250IMOpcGFyZ27DqXMuIENvbW1lbnQ/IFBhciAqKnN1YnN0aXR1dGlvbioqLiBOb3VzIHZveW9ucyBpY2kgdW4gcHJpbmNpcGUgcXVpIHRyYXZlcnNlIHRvdXRlIGzigJnDiWNyaXR1cmUgY29tbWUgdW4gZmlsIGNvbmR1Y3RldXIgw6ljYXJsYXRlLlxuXG5Pw7ksIGRhbnMgbGVzIMOJY3JpdHVyZXMsIHZvaXQtb24gbGEgcHJlbWnDqHJlIG1vcnQgc3Vic3RpdHV0aXZlPyDDgCBsYSBjaHV0ZS4gQWRhbSBldCDDiHZlIHByZW5uZW50IGR1IGZydWl0IGTDqWZlbmR1LiBMZXVycyB5ZXV4IHPigJlvdXZyZW50LCBpbHMgdm9pZW50IGxldXIgcHJvcHJlIG51ZGl0w6kgZXQgc2UgY2FjaGVudCBkZSBEaWV1LiBEaWV1IGxlcyBjaGFzc2UgZHUgamFyZGluLCBtYWlzIHBhcyBhdmFudCBkZSBsZXMgcmV2w6p0aXIgZGUgdsOqdGVtZW50cyBlbiBwZWF1IHBvdXIgY291dnJpciBsZXVyIG51ZGl0w6ksIGxldXIgaG9udGUuXG5cblF1ZWxsZSBncsOiY2UhIFBsdXTDtHQgcXVlIGxlIGp1Z2VtZW50IGV0IGxhIG1vcnQgaW1tw6lkaWF0ZSwgQWRhbSBldCDDiHZlIHJlw6dvaXZlbnQgZ3LDomNlIGV0IG1pc8Opcmljb3JkZS4gTm91cyB2b3lvbnMgY2VsYSBzZSByw6lww6l0ZXIgdG91dCBhdSBsb25nIGRlcyDDiWNyaXR1cmVzLiBDaGFxdWUgZm9pcyBxdWUgbGUgcGV1cGxlIGRlIERpZXUgb2ZmcmUgdW4gYW5pbWFsIGVuIHNhY3JpZmljZSwgY+KAmWVzdCBsZSBzdWJzdGl0dXQgcXVpIHZlcnNlIHNvbiBzYW5nIHBvdXIgZGVzIHDDqWNoZXVycy4gQ2VsYSBuZSBjb21tZW5jZSBwYXMgZGFucyBs4oCZRXhvZGUsIGNhciBOb8OpLCBBYnJhaGFtIGV0IHNlcyBmaWxzIG9mZnJlbnQgZMOpasOgIGRlcyBzYWNyaWZpY2VzLCBtYWlzIGPigJllc3Qgc2V1bGVtZW50IGRhbnMgbOKAmUV4b2RlIHF1ZSBsZSBzeXN0w6htZSBzYWNyaWZpY2llbCBlc3QgY29kaWZpw6kuXG5cbiMjIEzigJlhZ25lYXUgcGFzY2FsXG5cbkRpZXUgYXZlcnRpdCBsZXMgZW5mYW50cyBk4oCZSXNyYcOrbCBlbiDDiWd5cHRlIHF1ZSBsZSBqdWdlbWVudCBhcHByb2NoZS4gSWwgbGV1ciBkb25uZSDDqWdhbGVtZW50IGxlIHJlbcOoZGUgcG91ciDDqWNoYXBwZXIgw6AgY2UganVnZW1lbnQ6IGzigJlhZ25lYXUgcGFzY2FsIGRvaXQgw6p0cmUgdHXDqSwgZXQgc29uIHNhbmcgYXBwbGlxdcOpIHN1ciBsZXMgbGludGVhdXggZGVzIHBvcnRlcyBkZSBjaGFxdWUgZm95ZXIuIFNhbnMgZXhjZXB0aW9uLiBUb3V0IGZveWVyIHF1aSBuZSBwcmVuZHJhaXQgcGFzIGNlcyBpbnN0cnVjdGlvbnMgYXUgc8OpcmlldXggbmUgc2VyYWl0IHBhcyDDqXBhcmduw6kuXG5cbkPigJllc3QgdG91am91cnMgdmFsYWJsZSDDoCBub3RyZSDDqXBvcXVlLiBM4oCZQWduZWF1IFBhc2NhbCwgSsOpc3VzLUNocmlzdCwgYSB2ZXJzw6kgc29uIHNhbmcgcG91ciBzYXV2ZXIgZGVzIHDDqWNoZXVycy4gTWFpcyBzaSBub3VzIG7igJlhcHBsaXF1b25zIHBhcyBzb24gc2FjcmlmaWNlIGF1eCBsaW50ZWF1eCBkZSBub3MgY8WTdXJzLCBub3VzIG5lIHNlcm9ucyBwYXMgw6lwYXJnbsOpcyBkdSBqdWdlbWVudCDDoCB2ZW5pci5cblxuQWluc2ksIGxlIHNhbmcgZOKAmXVuIGFnbmVhdSBpbm5vY2VudCBzZXJ0IGRlIHN1YnN0aXR1dCBldCBjb3V2cmUgY2hhcXVlIG1haXNvbiBkZXMgZW5mYW50cyBk4oCZSXNyYcOrbC4gQ2VwZW5kYW50LCBsZSBwaGFyYW9uIGV0IHNvbiBwZXVwbGUgbmUgc29udCBwYXMgw6lwYXJnbsOpcy4gRGFucyBzb24gY2hhZ3JpbiBldCBlbiDDqXRhdCBkZSBjaG9jLCBpbCBsaWLDqHJlIHNlcyBlc2NsYXZlcywgbWFpcyBpbCBz4oCZZW4gcmVtZXQgcmFwaWRlbWVudCwgZXQgcGFydCDDoCBsYSBwb3Vyc3VpdGUgZGUgc2VzIGNhcHRpZnMgbGliw6lyw6lzLlxuXG4jIyBQw6hsZXJpbmFnZSBkYW5zIGxlIGTDqXNlcnQ6IEV4b2RlIDEzLTE5XG5cblZvdXMgdm91cyBzb3V2ZW5leiBkZSBsYSBzdWl0ZSwgbuKAmWVzdC1jZSBwYXM/IERhbnMgbGVzIGNoYXBpdHJlcyAxMy0xOSwgbm91cyBsaXNvbnMgY29tbWVudCwgY29pbmPDqSBlbnRyZSB1bmUgYXJtw6llIHB1aXNzYW50ZSBlbiBwbGVpbmUgcG91cnN1aXRlIGV0IHVuZSBtZXIgZMOpY2hhw65uw6llLCBsZSBwZXVwbGUgZGUgRGlldSB2YWNpbGxlIGRhbnMgbOKAmWluY3LDqWR1bGl0w6kuIE1hbGdyw6kgdG91dGVzIGxlcyBtZXJ2ZWlsbGVzIHF1ZSBs4oCZw4l0ZXJuZWwgYSBhY2NvbXBsaWVzIHBvdXIgZXV4LCBpbHMgYWNjdXNlbnQgZW5jb3JlIE1vw69zZTpcblxuPiBOb3VzIGFzLXR1IGFtZW7DqXMgaWNpIHBvdXIgbW91cmlyP1xuPlxuPiA8c3ViPjxjaXRlPkV4b2RlIDE0LjExPC9jaXRlPjwvc3ViPlxuXG5IZXVyZXVzZW1lbnQsIERpZXUgZXN0IHBhdGllbnQgZXQgY29tcGF0aXNzYW50LiBJbCBuZSBsYWlzc2UgcGFzIGxlIHBldXBsZSBk4oCZSXNyYcOrbCBzZSBkw6licm91aWxsZXIgc2V1bC4gQXUgY29udHJhaXJlLCBpbCBzw6lwYXJlIGxhIG1lciBkZXZhbnQgZXV4IGV0IGxlcyBjb25kdWl0IGVuIHRvdXRlIHPDqWN1cml0w6kgc3VyIGxhIHRlcnJlIGZlcm1lLCB0YW5kaXMgcXXigJlpbCBub2llIFBoYXJhb24gZXQgc29uIGFybcOpZSBkYW5zIGxlcyBwcm9mb25kZXVycy5cblxuIyMgTGEgbmFpc3NhbmNlIGRlIGxhIG5hdGlvblxuXG5Db21tZSBtZW50aW9ubsOpIGVuIGludHJvZHVjdGlvbiBldCBzZWxvbiBkZSBub21icmV1eCB0aMOpb2xvZ2llbnMsIGNldHRlIHDDqXJpb2RlIG1hcnF1ZSBsYSBuYWlzc2FuY2UgZGUgbGEgbmF0aW9uIGTigJlJc3Jhw6tsLiBKdXNxdeKAmWFsb3JzLCBpbHMgw6l0YWllbnQgZGVzIG5vbWFkZXMgZXJyYW50cywgcHVpcyBkZXMgaW1taWdyYW50cyBzb3VzIGxhIGRvbWluYXRpb24gZOKAmXVuZSBwdWlzc2FuY2Ugw6l0cmFuZ8OocmUuIE1haXMgZGFucyBs4oCZRXhvZGUsIG5vdXMgYXNzaXN0b25zIMOgIGxldXIgdHJhbnNmb3JtYXRpb24gZW4gdW5lIG5hdGlvbiBsaWJyZSBzb3VzIGxhIHByb3RlY3Rpb24gc3DDqWNpYWxlIGRlIERpZXUuIFVuIHBldXBsZSDDqWx1IGVuIHJvdXRlIHZlcnMgc29uIGRlc3RpbiBwcm9taXMgZGVzIHNpw6hjbGVzIGF1cGFyYXZhbnQsIHNvdXMgbGEgYmFubmnDqHJlIGRlIGxldXIgTGliw6lyYXRldXIgZXQgTWHDrnRyZS5cblxuRGFucyBsZXMgY2hhcGl0cmVzIHN1aXZhbnRzLCBEaWV1IGNvbnRpbnVlIGRlIGTDqW1vbnRyZXIgc2EgcGF0aWVuY2UgZW52ZXJzIHVuIHBldXBsZSBhdSBjxZN1ciByZWJlbGxlLiBNYWxncsOpIGxldXJzIHBsYWludGVzIGV0IGxldXIgbWFucXVlIGRlIGZvaSwgaWwgcG91cnZvaXQgw6AgbGV1cnMgYmVzb2lucyBlbiBsZXVyIGRvbm5hbnQgZGUgbOKAmWVhdSBkYW5zIGxlIGTDqXNlcnQsIGFpbnNpIHF1ZSBkdSBwYWluIGV0IGRlIGxhIHZvbGFpbGxlIHF1aSB0b21iZW50IGR1IGNpZWwuIFB1aXMsIGF1IGPFk3VyIGR1IGxpdnJlLCBs4oCZw4l0ZXJuZWwgbGV1ciBkb25uZSBsZSBwbHVzIHByw6ljaWV1eCBkZXMgY2FkZWF1eDogc2Egc2FpbnRlIExvaS5cblxuIyMgw4Agc3VpdnJl4oCmXG5cbkNlbGEgbm91cyBtw6huZSDDoCBsYSBkZXV4acOobWUgbW9pdGnDqSBkdSBsaXZyZSBkZSBs4oCZRXhvZGUuIE5lIG1hbnF1ZXogcGFzIDxhIGhyZWY9XCIvYXJ0aWNsZS9jb21tZW50LWNyb3lhbnQtY29tcHJlbmRyZS1leG9kZS0yL1wiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj5tb24gcHJvY2hhaW4gYXJ0aWNsZTwvYT4sIGRhbnMgbGVxdWVsIGplIHBvdXJzdWlzIGF2ZWMgbGEgc2Vjb25kZSBtb2l0acOpIGR1IGxpdnJlIGRlIGzigJlFeG9kZSBldCBzb24gYXBwbGljYXRpb24gcG91ciBsZSBwZXVwbGUgZGUgbGEgbm91dmVsbGUgYWxsaWFuY2UuXG5cbjxicj5cblxuIyMgUG91ciBhbGxlciBwbHVzIGxvaW5cblxuIyMjIDMgZW5zZWlnbmVtZW50cyB2aWTDqW9zXG5cbiogRMOpY291dnJleiBsZSBwcmVtaWVyIMOpcGlzb2RlIGRlIG5vdHJlIG5vdXZlbGxlIHPDqXJpZSAqQ2hyw6l0aWVubmUqLCBlbiBsaWVuIGF2ZWMgY2V0IGFydGljbGU6IDxhIGhyZWY9XCIvcG9kY2FzdHMvY2hyZXRpZW5uZS9pbnRyb2R1Y3Rpb24tZXhvZGUtcGV1cGxlLWRlbGl2cmVcIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+XCJJbnRyb2R1Y3Rpb24gw6AgbCdFeG9kZTogVW4gcGV1cGxlIGTDqWxpdnLDqSAoRXhvZGUgMS0xOClcIjwvYT4uXG4qIEplIHN1aXMgcmVkZXZhYmxlIGVudmVycyBKb25hdGhhbiBTcGVuY2VyIGV0IHNvbiBlbnNlaWduZW1lbnQgc3VyIGxlIGxpdnJlIGRlIGzigJlFeG9kZS4gRMOpY291dnJleiBzb24gd2ViaW5haXJlOiA8YSBocmVmPVwiL3dlYmluYWlyZXMvY29tcHJlbmRyZS1leG9kZS9cIiB0YXJnZXQ9XCJfYmxhbmtcIiByZWw9XCJub3JlZmVycmVyIG5vb3BlbmVyXCI+MWggcG91ciBjb21wcmVuZHJlIEV4b2RlPC9hPlxuKiBgPGRpdj5Ew6ljb3V2cmV6IMOpZ2FsZW1lbnQgc3VyIG1hIGNoYWluZSBZb3VUdWJlLCA8YSBocmVmPVwiaHR0cHM6Ly95b3V0dS5iZS9lV0cxbFY2dnE3Z1wiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj51bmUgw6l0dWRlIGJpYmxpcXVlPC9hPiBxdWUgauKAmWFpIGFwcG9ydMOpZSBsb3JzIGTigJl1bmUgcmV0cmFpdGUgcG91ciBmZW1tZXMgYXUgUXXDqWJlYy48L2Rpdj5gXG5cbiMjIyAzIGFydGljbGVzXG5cbiogVm9pY2kgbGUgZGV1eGnDqG1lIGFydGljbGUgZGUgY2V0dGUgc8OpcmllOiA8YSBocmVmPVwiL2FydGljbGUvY29tbWVudC1jcm95YW50LWNvbXByZW5kcmUtZXhvZGUtMi9cIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+XCJQb3VycXVvaSBEaWV1IG5vdXMgYS10LWlsIGRvbm7DqSBzYSBsb2k/XCI8L2E+XG4qIDxhIGhyZWY9XCIvYXJ0aWNsZS8yLW1lZGlhdHJpY2VzLWltcHJvYmFibGVzL1wiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj5cIjIgbcOpZGlhdHJpY2VzIGltcHJvYmFibGVzOiBDb21tZW50IERpZXUgc2Ugc2Vydml0IGTigJllbGxlcyBwb3VyIHNhdXZlciBsZSBtb25kZVwiPC9hPlxuKiA8YSBocmVmPVwiL2FydGljbGUvbm91dmVsLWV4b2RlLWVzYWllLWV2YW5naWxlLXNlbG9uLW1hcmMvXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPlwiTGUgbm91dmVsIGV4b2RlIGTigJnDiXNhw69lIGRhbnMgbOKAmcOJdmFuZ2lsZSBzZWxvbiBNYXJjXCI8L2E+XG5cbjxzdWI+QXJ0aWNsZSBwdWJsacOpIHBvdXIgbGEgcHJlbWnDqHJlIGZvaXMgbGUgMTEgZsOpdnJpZXIgMjAyMS4gSmUgbOKAmWFpIHJlbWlzIGVuIGF2YW50IGxlIDUgc2VwdGVtYnJlIDIwMjQgw6AgbOKAmW9jY2FzaW9uIGR1IGxhbmNlbWVudCBkZSBsYSA8YSBocmVmPVwiL2FydGljbGUvc29tbWFpcmUtcG9kY2FzdC1jaHJldGllbm5lI2V4b2RlXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPm5vdXZlbGxlIHPDqXJpZSBkZSBub3RyZSBwb2RjYXN0IDxlbT5DaHLDqXRpZW5uZTwvZW0+PC9hPi48L3N1Yj4iLCJhdXRob3IiOnsiZnVsbE5hbWUiOiJBbmdpZSBWZWxhc3F1ZXogVGhvcm50b24iLCJwaWN0dXJlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvSU1HXzI3MjVfNGE3ZGZkMDg3My5qcGVnIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifX0sImltYWdlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvamFtZXNfYmFsbGFyZF9hX0pfTnlfS0JKVl9VcV9JX3Vuc3BsYXNoX2FkOWNjNDFkOTcuanBnIiwiaGVpZ2h0Ijo4NTMsIndpZHRoIjoxMjgwLCJhbHRlcm5hdGl2ZVRleHQiOiIiLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9LCJ0b3BpY3MiOlt7Im5hbWUiOiJIZXJtw6luZXV0aXF1ZSJ9LHsibmFtZSI6IlBlbnRhdGV1cXVlIn0seyJuYW1lIjoiVGjDqW9sb2dpZSBkZSBs4oCZYWxsaWFuY2UifV0sIm1vZHVsZXMiOlt7fSx7Il9fdHlwZW5hbWUiOiJDb21wb25lbnRNb2R1bGVMZWFkIiwiY29udGVudCI6IlBvdXJxdW9pIGxlIGxpdnJlIGRlIGwnRXhvZGU/IETDqHMgbGVzIHByZW1pw6hyZXMgcGFnZXMsIG5vdXMgc29tbWVzIHTDqW1vaW5zIGQndW5lIG5haXNzYW5jZS4gT3VpLCBub3VzIGTDqWNvdXZyb25zIGxlIHLDqWNpdCBkZSBsYSBuYWlzc2FuY2UgZXQgZGUgbGEgZMOpbGl2cmFuY2UgZGVzIGdhcsOnb25zIGjDqWJyZXV4IHBhciBsZSBiaWFpcyBkZSBkZXV4IHNhZ2VzLWZlbW1lcyBjb3VyYWdldXNlcywgcHVpcyBjZWx1aSBkZSBNb8Ovc2UsIHNhdXbDqSBwYXIgZGV1eCBhdXRyZXMgZmVtbWVzIHRvdXQgYXVzc2kgY291cmFnZXVzZXM6IHNhIG3DqHJlIGV0IHNhIHPFk3VyIChzYW5zIHBhcmxlciBkZSBsYSBmaWxsZSBkZSBQaGFyYW9uKS4gQ2VwZW5kYW50LCBjZXMgcsOpY2l0cyBzb250IGzigJlpbWFnZSBk4oCZdW4gw6l2w6huZW1lbnQgYmllbiBwbHVzIGdyYW5kLCBhdSBjxZN1ciBkdSBsaXZyZSBkZSBsJ0V4b2RlOiBsYSBuYWlzc2FuY2UgZXQgbGEgZMOpbGl2cmFuY2UgZGUgbGEgbmF0aW9uIGQnSXNyYcOrbC4ifV19LHsiaWQiOiI0MDcxIiwidGl0bGUiOiJRdWVscXVlcyByZXNzb3VyY2VzIHBvdXIgZXhwbG9yZXIgbGUgbGl2cmUgZGUgUmViZWNjYSBNY0xhdWdobGluIiwic2x1ZyI6InJlc3NvdXJjZXMtcG91ci1leHBsb3Jlci1saXZyZS1yZWJlY2NhLW1jbGF1Z2hsaW4iLCJ0eXBlIjoiYXJ0aWNsZSIsInB1Ymxpc2hlZF9hdCI6IjIwMjQtMDctMTdUMjI6NTc6NTQuMDAwWiIsImJvZHkiOiJQcmVtacOocmVtZW50LCBs4oCZYXV0ZXVyZSBhYm9yZGUgZGVzIHN1amV0cyDDqXBpbmV1eCBkZSBtYW5pw6hyZSBzaW1wbGUgZXQgYWdyw6lhYmxlLiBEZXV4acOobWVtZW50LCBlbGxlIHV0aWxpc2UgZGVzIGlsbHVzdHJhdGlvbnMgdml2YW50ZXMgcXVpIHJlbmRlbnQgc2VzIGFyZ3VtZW50cyBwbHVzIGNsYWlycyBldCBhY2Nlc3NpYmxlcy5cblxuYDxwPkVuIGNvbGxhYm9yYXRpb24gYXZlYyBBdXLDqWxpZSBCcmljYXVkLCBtYSBjb2FuaW1hdHJpY2Ugc3VyIDxlbT5DaHLDqXRpZW5uZTwvZW0+LCBub3VzIG5vdXMgc29tbWVzIGxhbmPDqWVzIGRhbnMgPGEgaHJlZj1cIi9hcnRpY2xlL2ludGVydmlldy1SZWJlY2NhLU1jTGF1Z2hsaW5cIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+dW5lIHPDqXJpZSBkZSAxMiDDqXBpc29kZXM8L2E+LCBjaGFjdW4gZMOpZGnDqSDDoCB1biBjaGFwaXRyZSBkdSBsaXZyZSBkZSBNbWUgTWNMYXVnaGxpbi4gTm91cyBhdm9ucyBtw6ptZSBham91dMOpIDxhIGhyZWY9XCIvcG9kY2FzdHMvY2hyZXRpZW5uZS9mb3Jtb25zLWVuZmFudHMtYXBvbG9nZXRpcXVlXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPnVuIMOpcGlzb2RlIGJvbnVzPC9hPiBpbnNwaXLDqSBkZSBsYSB2ZXJzaW9uIHBvdXIgamV1bmVzIGFkb2xlc2NlbnRzIGRlIHNvbiBsaXZyZSwgPGEgaHJlZj1cImh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzLzEwLXF1ZXN0aW9ucy1xdWUtdHUtZGV2cmFpcy10ZS1wb3Nlci1zdXItbGUtY2hyaXN0aWFuaXNtZVwiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj48ZW0+MTAgcXVlc3Rpb25zIHF1ZSB0dSBkZXZyYWlzIHRlIHBvc2VyIHN1ciBsZSBjaHJpc3RpYW5pc21lPC9lbT48L2E+LjwvcD5gXG5cbk5vdXMgYXZvbnMgaW52aXTDqSBxdWVscXVlcy11bnMgZGVzIG1laWxsZXVycyBwZW5zZXVycyBmcmFuY29waG9uZXMgZGFucyBsZSBkb21haW5lIGRlIGwnYXBvbG9nw6l0aXF1ZS4gQ2VydGFpbnMgZOKAmWVudHJlIGV1eCBvbnQgZOKAmWFpbGxldXJzIMOpY3JpdCB1bmUgcmVjb21tYW5kYXRpb24gcG91ciBzb24gbGl2cmUuIE5vdHJlIG9iamVjdGlmIMOpdGFpdCBkZSBjb250ZXh0dWFsaXNlciBsZSBjb250ZW51IMOpY3JpdCBwYXIgdW5lIGJyaXRhbm5pcXVlIHZpdmFudCBhdXggw4l0YXRzLVVuaXMgZGFucyBub3RyZSByw6lhbGl0w6kgZXVyb3DDqWVubmUgZXQgcXXDqWLDqWNvaXNlLCBtYXJxdcOpZSBwYXIgdW4gZm9ydCBzw6ljdWxhcmlzbWUuIExlIHLDqXN1bHRhdCBlc3QgdW5lIHPDqXJpZSBkJ2VudHJldGllbnMgZW5yaWNoaXNzYW50cyBvw7kgZGVzIGhvbW1lcyBldCBkZXMgZmVtbWVzIGRlIHF1YWxpdMOpIGFwcG9ydGVudCBsZXVyIHNhZ2Vzc2Ugc3VyIGNlcnRhaW5lcyBkZXMgcXVlc3Rpb25zIGxlcyBwbHVzIGRpZmZpY2lsZXMgYXV4cXVlbGxlcyBub3VzLCBjcm95YW50cywgc29tbWVzIGNvbmZyb250w6lzLlxuXG4qIOKAnExlIGNocmlzdGlhbmlzbWUgcmFiYWlzc2UtdC1pbCBsZXMgZmVtbWVzP+KAnVxuKiDigJxMYSBCaWJsZSBhcHByb3V2ZS10LWVsbGUgbCdlc2NsYXZhZ2U/4oCdXG4qIOKAnFBvdXJxdW9pIHVuIERpZXUgYWltYW50IGVudmVycmFpdC1pbCBkZXMgZ2VucyBlbiBlbmZlcj/igJ1cbiog4oCcUG91cnF1b2kgRGlldSBwZXJtZXQtaWwgdGFudCBkZSBzb3VmZnJhbmNlIGRhbnMgbGUgbW9uZGU/4oCdXG5cbkNlIG5lIHNvbnQgbMOgIHF1ZSBxdWF0cmUgZGVzIGRvdXplIHF1ZXN0aW9ucyBxdWUgbm91cyBhdm9ucyBleHBsb3LDqWVzLlxuXG5FbiBwYXJhbGzDqGxlIGRlcyBpbnRlcnZpZXdzLCBkaWZmdXPDqWVzIGNoYXF1ZSBtYXJkaSwgaidhaSDDqWNyaXQgdW5lIHPDqXJpZSBkJ2FydGljbGVzIGNvdXJ0cyBzdXIgbW9uIGJsb2csIGfDqW7DqXJhbGVtZW50IHB1Ymxpw6lzIGxlcyBqZXVkaXMuIERhbnMgY2hhY3VuIGRlIGNlcyBhcnRpY2xlcywgaidhaSBwYXJ0YWfDqSBxdWVscXVlcy11bmVzIGRlIG1lcyBjaXRhdGlvbnMgcHLDqWbDqXLDqWVzIGRlcyBjaGFwaXRyZXMgZW4gcXVlc3Rpb24uIE1vbiBidXQgw6l0YWl0IGRlIHN1c2NpdGVyIGNoZXogbWVzIGFib25uw6lzIGxlIGTDqXNpciBkZSBsaXJlICoxMiByYWlzb25zKiBwYXIgZXV4LW3Dqm1lcy5cblxuYDxwPkNlcmlzZSBzdXIgbGUgZ8OidGVhdTogaidhaSBldSBsZSBwcml2aWzDqGdlIGRlIGNvbnRhY3RlciBSZWJlY2NhIE1jTGF1Z2hsaW4gcG91ciBsdWkgZGVtYW5kZXIgPGEgaHJlZj1cImh0dHBzOi8veW91dHUuYmUvdVo2Z1Q4Z093dU1cIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+dW5lIGJyw6h2ZSBlbnRyZXZ1ZSBkZSAxNSBtaW51dGVzPC9hPi4gSmUgbWUgc3VpcyBkaXQsIHBvdXJxdW9pIG5lIHBhcyBsYWlzc2VyIGwnYXV0ZXVyZSBlbGxlLW3Dqm1lIHJhY29udGVyIGNlIHF1aSBsJ2EgYW1lbsOpZSDDoCDDqWNyaXJlIHNvbiBsaXZyZSBldCBkb25uZXIgZGVzIGNvbnNlaWxzIGF1eCBjcm95YW50cyBmcmFuY29waG9uZXMgdml2YW50IGRhbnMgbm90cmUgY29udGV4dGUgaGF1dGVtZW50IHPDqWN1bGFyaXPDqT8gSmV0ZXoteSB1biBjb3VwIGQnxZNpbCBldCA8YSBocmVmPVwibWFpbHRvOmFuZ2llQHRvdXRwb3Vyc2FnbG9pcmUuY29tXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPmRpdGVzLW1vaSBjZSBxdWUgdm91cyBlbiBhdmV6IHBlbnPDqTwvYT4hPC9wPmBcblxuYDxpZnJhbWUgd2lkdGg9XCIxMDAlXCIgaGVpZ2h0PVwiNDA1XCIgc3JjPVwiaHR0cHM6Ly93d3cueW91dHViZS5jb20vZW1iZWQvdVo2Z1Q4Z093dU0/c2k9N2c0VDh0alZoTmg0Q0t4cVwiIHRpdGxlPVwiWW91VHViZSB2aWRlbyBwbGF5ZXJcIiBmcmFtZWJvcmRlcj1cIjBcIiBhbGxvdz1cImFjY2VsZXJvbWV0ZXI7IGF1dG9wbGF5OyBjbGlwYm9hcmQtd3JpdGU7IGVuY3J5cHRlZC1tZWRpYTsgZ3lyb3Njb3BlOyBwaWN0dXJlLWluLXBpY3R1cmU7IHdlYi1zaGFyZVwiIHJlZmVycmVycG9saWN5PVwic3RyaWN0LW9yaWdpbi13aGVuLWNyb3NzLW9yaWdpblwiIGFsbG93ZnVsbHNjcmVlbj48L2lmcmFtZT5gXG5cbjxicj5cblxuIyMgUGV0aXQgcsOpY2FwaXR1bGF0aWZcblxuKiBgPGRpdj5MZSBsaXZyZSwgPGEgaHJlZj1cImh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzLzEyLXJhaXNvbnMtZGUtbmUtcGx1cy1jcm9pcmUtYXUtY2hyaXN0aWFuaXNtZVwiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj48ZW0+MTIgcmFpc29ucyBkZSBuZSBwbHVzIGNyb2lyZSBhdSBjaHJpc3RpYW5pc21lPC9lbT48L2E+LCBkZSBSZWJlY2NhIE1jTGF1Z2hsaW48L2Rpdj5gXG4qIGA8ZGl2PjxhIGhyZWY9XCIvYXJ0aWNsZS9pbnRlcnZpZXctUmViZWNjYS1NY0xhdWdobGluXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPlVuZSBzw6lyaWUgZGUgMTIgw6lwaXNvZGVzIGR1IHBvZGNhc3QgPGVtPkNocsOpdGllbm5lPC9lbT48L2E+IGJhc8OpZSBzdXIgbGUgbGl2cmUgZGUgUmViZWNjYSBNY0xhdWdobGluPC9kaXY+YFxuKiBgPGRpdj48YSBocmVmPVwiL3BvZGNhc3RzL2NocmV0aWVubmUvZm9ybW9ucy1lbmZhbnRzLWFwb2xvZ2V0aXF1ZVwiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj5VbiDDqXBpc29kZSBib251czwvYT4gZGVzdGluw6kgYXV4IHBhcmVudHMgcXVpIHNvdWhhaXRlbnQgw6lxdWlwZXIgbGV1cnMgZW5mYW50cyBmYWNlIMOgIGzigJlvcHBvc2l0aW9uPC9kaXY+YFxuKiBgPGRpdj5MZSBsaXZyZSwgPGEgaHJlZj1cImh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzLzEwLXF1ZXN0aW9ucy1xdWUtdHUtZGV2cmFpcy10ZS1wb3Nlci1zdXItbGUtY2hyaXN0aWFuaXNtZVwiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj48ZW0+MTAgcXVlc3Rpb25zIHF1ZSB0dSBkZXZyYWlzIHRlIHBvc2VyIHN1ciBsZSBjaHJpc3RpYW5pc21lPC9lbT48L2E+LCBkZSBSZWJlY2NhIE1jTGF1Z2hsaW4sIGRlc3RpbsOpIGF1eCBhZG9sZXNjZW50czwvZGl2PmBcbiogYDxkaXY+PGEgaHJlZj1cImh0dHBzOi8veW91dHUuYmUvdVo2Z1Q4Z093dU1cIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+VW5lIGNvdXJ0ZSBpbnRlcnZpZXcgKHNvdXMtdGl0csOpZSk8L2E+IGRhbnMgbGFxdWVsbGUgUmViZWNjYSBNY0xhdWdobGluIHLDqXBvbmQgw6AgbWVzIHF1ZXN0aW9uczwvZGl2PmBcbiogYDxhIGhyZWY9XCJtYWlsdG86YW5naWVAdG91dHBvdXJzYWdsb2lyZS5jb21cIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+RmFpdGVzLW1vaSB2b3MgcmV0b3VycyE8L2E+YFxuXG5cXFxuIiwiYXV0aG9yIjp7ImZ1bGxOYW1lIjoiQW5naWUgVmVsYXNxdWV6IFRob3JudG9uIiwicGljdHVyZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L0lNR18yNzI1XzRhN2RmZDA4NzMuanBlZyIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn19LCJpbWFnZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0LzEyX3JhaXNvbl9kZV9uZV9wbHVzX2Nyb2lyZV9hdV9jaHJpc3RpYW5pc21lXzA0XzEwMjR4NjgzXzViN2ZhNWEyYWMuanBnIiwiaGVpZ2h0Ijo2ODMsIndpZHRoIjoxMDI0LCJhbHRlcm5hdGl2ZVRleHQiOm51bGwsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn0sInRvcGljcyI6W3sibmFtZSI6IlZpZSBjaHLDqXRpZW5uZSJ9LHsibmFtZSI6IkFwb2xvZ8OpdGlxdWUgZXQgdmlzaW9uIGR1IG1vbmRlIn0seyJuYW1lIjoiUmVzc291cmNlcyJ9LHsibmFtZSI6IlJlY2Vuc2lvbiBkZSBsaXZyZSJ9XSwibW9kdWxlcyI6W3siX190eXBlbmFtZSI6IkNvbXBvbmVudE1vZHVsZUxlYWQiLCJjb250ZW50IjoiTG9yc3F1ZSBqJ2FpIGNvbW1lbmPDqSDDoCBwYXJjb3VyaXIgbGUgbGl2cmUgKlsxMiByYWlzb25zIGRlIG5lIHBsdXMgY3JvaXJlIGF1IGNocmlzdGlhbmlzbWVdKGh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzLzEyLXJhaXNvbnMtZGUtbmUtcGx1cy1jcm9pcmUtYXUtY2hyaXN0aWFuaXNtZSkqIGRlIFJlYmVjY2EgTWNMYXVnaGxpbiwgaidhaSBpbW3DqWRpYXRlbWVudCDDqXTDqSBmcmFwcMOpZSBwYXIgc2EgZmFjaWxpdMOpIGRlIGxlY3R1cmUuIFNvbiBsaXZyZSBhIHRvdWNow6kgdW5lIGNvcmRlIHNlbnNpYmxlIGVuIG1vaSwgY+KAmWVzdCBwb3VycXVvaSBq4oCZYWkgdm91bHUgcXUnaWwgc29pdCBsdSBwYXIgbGUgcGx1cyBncmFuZCBub21icmUuIn1dfSx7ImlkIjoiNDA1NiIsInRpdGxlIjoiUXVlIHZhdXQgdW5lIGZpbGxlPyIsInNsdWciOiJxdWUtdmF1dC11bmUtZmlsbGUiLCJ0eXBlIjoiYXJ0aWNsZSIsInB1Ymxpc2hlZF9hdCI6IjIwMjQtMDctMDRUMDY6MjU6MDMuMDAwWiIsImJvZHkiOiJgYGBqYXZhc2NyaXB0XG48ZGl2IGlkPVwiZWxldmVubGFicy1hdWRpb25hdGl2ZS13aWRnZXRcIiBkYXRhLWhlaWdodD1cIjkwXCIgZGF0YS13aWR0aD1cIjEwMCVcIiBkYXRhLWZyYW1lYm9yZGVyPVwibm9cIiBkYXRhLXNjcm9sbGluZz1cIm5vXCIgZGF0YS1wdWJsaWN1c2VyaWQ9XCJjMzliMjNiYmIxN2U4MGJlNmI1ZmUzZDZmZWQ0ZDk3ZWY4YzFlNDViYzU3ZjU0MjA3NDIzYjhkMmViMTExZmIyXCIgZGF0YS1wbGF5ZXJ1cmw9XCJodHRwczovL2VsZXZlbmxhYnMuaW8vcGxheWVyL2luZGV4Lmh0bWxcIiA+TG9hZGluZyB0aGUgPGEgaHJlZj1cImh0dHBzOi8vZWxldmVubGFicy5pby90ZXh0LXRvLXNwZWVjaFwiIHRhcmdldD1cIl9ibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+RWxldmVubGFicyBUZXh0IHRvIFNwZWVjaDwvYT4gQXVkaW9OYXRpdmUgUGxheWVyLi4uPC9kaXY+PHNjcmlwdCBzcmM9XCJodHRwczovL2VsZXZlbmxhYnMuaW8vcGxheWVyL2F1ZGlvTmF0aXZlSGVscGVyLmpzXCIgdHlwZT1cInRleHQvamF2YXNjcmlwdFwiPjwvc2NyaXB0PlxuYGBgXG5cblNvbiBoaXN0b2lyZSBjb21tZW5jZSBkYW5zIGxhIG1haXNvbiBkZSBzb24gZW5mYW5jZSwgZGFucyBsZSBNaWNoaWdhbiwgb8O5IHNhIG3DqHJlIGFpbWFudGUgbHVpIGZhaXQgbCfDqWNvbGUgw6AgbGEgbWFpc29uLCBhaW5zaSBxdSfDoCBzZXMgZnLDqHJlcyBldCBzxZN1cnMuIElscyBzb250IHRyw6hzIGFjdGlmcyBhdSBzZWluIGRlIGxldXIgw4lnbGlzZSwgb8O5IFJhY2hhZWwgcydvY2N1cGUgZCdlbmZhbnRzIGTDqHMgbGUgZMOpYnV0IGRlIHNvbiBhZG9sZXNjZW5jZS4gRWxsZSBmYWl0IMOpZ2FsZW1lbnQgZHUgYmFieS1zaXR0aW5nIGV0IHRyYXZhaWxsZSBjb21tZSBjb25jaWVyZ2UgZGFucyBzb24gY2x1YiBkZSBneW0gbG9jYWwgcG91ciBhaWRlciDDoCBwYXllciBzZXMgbGXDp29ucyBkZSBneW1uYXN0aXF1ZS4gQ2Ugc3BvcnQgZXN0IHNhIHBhc3Npb24sIGV0IGMnZXN0IHVuZSBlbmZhbnQgZMOpdGVybWluw6llLCBwcsOqdGUgw6AgdG91dCBwb3VyIHLDqWFsaXNlciBzb24gcsOqdmUgZGUgZGV2ZW5pciB1bmUgZ3ltbmFzdGUgZGUgY29tcMOpdGl0aW9uLlxuXG4jIFVuZSBibGVzc3VyZSBpbmF0dGVuZHVlXG5cbkFwcsOocyBxdWVscXVlcyBhbm7DqWVzIGQnZW50cmHDrm5lbWVudCwgcGx1c2lldXJzIGJsZXNzdXJlcyBvYmxpZ2VudCBsZXMgRGVuaG9sbGFuZGVyIMOgIGNvbnN1bHRlciB1biBtw6lkZWNpbiBwb3VyIGxldXIgZmlsbGUuIERlIG5vbWJyZXVzZXMgdmlzaXRlcyBjaGV6IGxlcyBtw6lkZWNpbnMgZGUgbGEgcsOpZ2lvbiBzJ2F2w6hyZW50IGluZnJ1Y3R1ZXVzZXMgcG91ciBzb3VsYWdlciBzZXMgZG91bGV1cnMgY2hyb25pcXVlcyBhdSBkb3MgZXQgYXUgcG9pZ25ldC4gQydlc3QgYWxvcnMgcXVlIGRlcyBhbWlzIGR1IG1vbmRlIGR1IHNwb3J0IGxldXIgcmVjb21tYW5kZW50IGxlIERyIExhcnJ5IE5hc3Nhciwgc3DDqWNpYWxpc3RlIHJlbm9tbcOpIHF1aSBhIHNvaWduw6kgYXZlYyBzdWNjw6hzIGQnaW5ub21icmFibGVzIGpldW5lcyBhdGhsw6h0ZXMsIG5vdGFtbWVudCBlbiB0YW50IHF1ZSBtw6lkZWNpbiBvZmZpY2llbCBkZSBsJ8OpcXVpcGUgb2x5bXBpcXVlIGRlIGd5bW5hc3RpcXVlIGRlcyDDiXRhdHMtVW5pcy5cblxuTGUgY2FiaW5ldCBkZSBOYXNzZXIgc2UgdHJvdXZlIMOgIHBsdXMgZCd1bmUgaGV1cmUgZGUgbGV1ciBkb21pY2lsZSwgbWFpcyBsZXMgRGVuaG9sbGFuZGVyIG9udCBiZXNvaW4gZGUgcsOpc3VsdGF0cy4gSWxzIHNvbnQgcHLDqnRzIMOgIGZhaXJlIHRvdXQgY2UgcXUnaWwgZmF1dCBwb3VyIGFpZGVyIGxldXIgcHLDqWNpZXVzZSBmaWxsZSDDoCBzZSByZW1ldHRyZSBkZSBzZXMgYmxlc3N1cmVzIGV0IMOgIHJlcHJlbmRyZSBsZSBzcG9ydCBxdSdlbGxlIGFpbWUuIElscyBzb250IGxvaW4gZGUgc2UgZG91dGVyIHF1J2lscyBzb250IHN1ciBsZSBwb2ludCBkZSBjb25maWVyIGxldXIgZmlsbGUgw6AgdW4gcHLDqWRhdGV1ciBzZXh1ZWwuXG5cbiMgUmVjb25uYcOudHJlIGxlcyBzaWduZXMgZGVzIGFidXNcblxuTGUgdHJhaXRlbWVudCBkdSBwbGFuY2hlciBwZWx2aWVuLCBxdWkgaW1wbGlxdWUgcGFyZm9pcyBsYSBww6luw6l0cmF0aW9uIGR1IHZhZ2luLCBlc3QgdW5lIHByb2PDqWR1cmUgbcOpZGljYWxlIMOpdGFibGllIHBvdXIgY2VydGFpbmVzIGJsZXNzdXJlcyBzcG9ydGl2ZXMuIEMnZXN0IHBvdXJxdW9pLCBEZW5ob2xsYW5kZXIgbidhIGF1Y3VuZSBhcnJpw6hyZS1wZW5zw6llIGxvcnNxdWUgTmFzc2FyIHV0aWxpc2UgY2V0dGUgcHJhdGlxdWUgbG9ycyBkZSBzYSBwcmVtacOocmUgdmlzaXRlLiBBcHLDqHMgdG91dCwgY2UgbcOpZGVjaW4gY2hhcm1hbnQgZXQgYWltYWJsZSBqb3VpdCBkJ3VuZSByw6lwdXRhdGlvbiBpcnLDqXByb2NoYWJsZS4gRGUgcGx1cywgc2EgbcOocmUgZXN0IGFzc2lzZSDDoCBjw7R0w6kgZCdlbGxlIHBlbmRhbnQgdG91dGUgbGEgZHVyw6llIGRlIGxhIHZpc2l0ZS5cblxuTGUgY8OpbMOoYnJlIG3DqWRlY2luIGVzdCDDoCBsYSBoYXV0ZXVyIGRlIHNhIHLDqXB1dGF0aW9uIGV0IGwnYXNwaXJhbnRlIGd5bW5hc3RlIGNvbW1lbmNlIGJpZW50w7R0IMOgIHZvaXIgZGVzIHLDqXN1bHRhdHMuIEF1IGNvdXJzIGRlcyBtb2lzIHN1aXZhbnRzLCBNbWUgZXQgTWxsZSBEZW5ob2xsYW5kZXIgZm9udCBsZSBsb25nIHRyYWpldCBqdXNxdSfDoCBzb24gY2FiaW5ldCBwb3VyIGRlcyB0cmFpdGVtZW50cyBkZSBzdWl2aS4gw4AgY2hhcXVlIGZvaXMsIGlsIGVmZmVjdHVlIGxhIHRlY2huaXF1ZSB0b3V0IGVuIGRpc2N1dGFudCBhbWljYWxlbWVudCBkZSBsYSBzY29sYXJpdMOpIGRlIGwnYWRvbGVzY2VudGUsIGRlIHNlcyBjZW50cmVzIGQnaW50w6lyw6p0cyBldCBkZSBzYSB2aWUgZGUgZmFtaWxsZS4gVW4gam91ciwgY2VwZW5kYW50LCBsYSBwZXJ2ZXJzaW9uIGRlIE5hc3NlciBwcmVuZCBsZSBkZXNzdXMgZXQgRGVuaG9sbGFuZGVyIHJlY29ubmHDrnQgbGVzIHNpZ25lcyByw6l2w6lsYXRldXJzIGQndW5lIGV4Y2l0YXRpb24gc2V4dWVsbGUuIEMnZXN0IGFsb3JzIHF1J2VsbGUgZMOpY2lkZSBkZSB0b3V0IHLDqXbDqWxlciDDoCBzYSBtw6hyZS4gTW1lIERlbmhvbGxhbmRlciBlc3QgaG9ycmlmacOpZSwgY2FyIGVsbGUgYSBhc3Npc3TDqSDDoCB0b3V0ZXMgbGVzIGNvbnN1bHRhdGlvbnMsIG1haXMgTmFzc2VyIHMnZXN0IHN0cmF0w6lnaXF1ZW1lbnQgcG9zaXRpb25uw6kgcG91ciBxdSdlbGxlIG5lIHB1aXNzZSBwYXMgdm9pciBjZSBxdSdpbCBmYWlzYWl0LlxuXG5FbnNlbWJsZSwgZWxsZXMgY29uc3VsdGVudCBkZXV4IHRow6lyYXBldXRlcyBzcMOpY2lhbGlzw6lzIGRhbnMgbGUgdHJhaXRlbWVudCBkdSBwbGFuY2hlciBwZWx2aWVuLiBDZWxsZXMtY2kgbGVzIGluZm9ybWVudCBxdWUgY2UgcXUnYSBmYWl0IGNldCBob21tZSBuZSByZXNzZW1ibGUgZW4gcmllbiDDoCBsYSBwcm9jw6lkdXJlIG3DqWRpY2FsZTogTmFzc2VyIG4nYSBwYXMgdXRpbGlzw6kgZGUgZ2FudHMsIGlsIG4nYSBwYXMgZGVtYW5kw6kgbGEgcGVybWlzc2lvbiBhdSBwcsOpYWxhYmxlIGV0IGlsIG4nYSBwYXMgZXhwbGlxdcOpIGNlIHF1J2lsIGZhaXNhaXQgZW4gY291cnMgZGUgcm91dGUuIEMnZXN0IGFsb3JzIHF1ZSBEZW5ob2xsYW5kZXIgY29tcHJlbmQgY2UgcXUnZWxsZSBhIHbDqWN1LiBDZSBjw6lsw6hicmUgbcOpZGVjaW4gZW4gcXVpIGVsbGUgYXZhaXQgY29uZmlhbmNlIG5lIGNoZXJjaGFpdCBwYXMgw6AgbCdhaWRlciDDoCBndcOpcmlyLiBJbCBsYSBtYW5pcHVsYWl0IGF2ZWMgc2EgZ2VudGlsbGVzc2UgZXQgbCd1dGlsaXNhaXQgZW5zdWl0ZSBwb3VyIHNhIHNhdGlzZmFjdGlvbiBzZXh1ZWxsZS4gTGEgcHJpc2UgZGUgY29uc2NpZW5jZSBkZSBjZSBxdSdlbGxlIGEgZW5kdXLDqSBwZW5kYW50IGRlcyBtb2lzIHMnYWJhdCBzdXIgRGVuaG9sbGFuZGVyIGNvbW1lIHVuZSBhdmFsYW5jaGUsIHRvdXQgY29tbWUgbGUgZmFpdCBkZSBzYXZvaXIgcXVlIGNldCBob21tZSBhIGFjY8OocyDDoCBkJ2lubm9tYnJhYmxlcyBhdXRyZXMgZmlsbGVzIGNvbW1lIGVsbGUuIEVsbGUgZG9pdCBwYXJsZXIuXG5cbiMg4oCcTmUgZGlzIHJpZW7igJ1cblxuTWFpcyBsb3JzcXUnZWxsZSBsZSBmYWl0LCBsZSBtZXNzYWdlIHF1ZSBsdWkgYWRyZXNzZSBsYSByZXNwb25zYWJsZSBkZSBzYSBzYWxsZSBkZSBzcG9ydCBlc3QgY2xhaXI6IExhcnJ5IE5hc3NhciBlc3QgdW4gZXhwZXJ0IGRhbnMgbGUgZG9tYWluZSBkZSBsYSBtw6lkZWNpbmUgc3BvcnRpdmUsIMOgIGxhIHLDqXB1dGF0aW9uIGltcGVjY2FibGUuIEwnw6l2YWx1YXRpb24gZGUgbGEgc2l0dWF0aW9uIHBhciBsJ2Fkb2xlc2NlbnRlIGRvaXQgw6p0cmUgZXJyb27DqWUuIOKAnE5lIGRpcyByaWVu4oCdLCBsdWkgZGl0LW9uLiBDb25zY2llbnRzIGRlIGxldXIgaW1wdWlzc2FuY2UgZmFjZSDDoCB1biB0ZWwgYWR2ZXJzYWlyZSwgbGVzIERlbmhvbGxhbmRlciBuJ29udCBwYXMgbGUgY2hvaXguIEFwcsOocyB0b3V0LCBjJ2VzdCBsZXVyIHBhcm9sZSBjb250cmUgbGEgc2llbm5lLlxuXG5QZW5kYW50IGxlcyBhbm7DqWVzIHF1aSBzdWl2ZW50LCBEZW5ob2xsYW5kZXIgcHLDqXNlbnRlIGRlcyBzeW1wdMO0bWVzIGRlIHRyYXVtYXRpc21lLCBub3RhbW1lbnQgZGVzIGNhdWNoZW1hcnMsIGRlcyBjcmlzZXMgZGUgcGFuaXF1ZSBldCwgcGFyZm9pcywgdW5lIHBldXIgaW52YWxpZGFudGUgZGVzIGhvbW1lcy4gUGFyIGxhIGdyw6JjZSBkZSBEaWV1IGV0IGF2ZWMgbGUgc291dGllbiBkZSBzYSBmYW1pbGxlIGV0IGRlIHNvbiDDiWdsaXNlLCBlbGxlIHRlbnRlIGRlIHRpcmVyIHVuIHRyYWl0IHN1ciBzb24gcGFzc8OpLiBFbGxlIHBvdXJzdWl0IGRlcyDDqXR1ZGVzIGRlIGRyb2l0IGV0LCBjZSBmYWlzYW50LCBhY3F1aWVydCB1bmUgZXhwZXJ0aXNlIGRhbnMgbGUgZG9tYWluZSBtw6ptZSBxdWkgbHVpIHBlcm1ldHRyYSBkZSBtZW5lciBsYSBjaGFyZ2UgY29udHJlIGwnYWdyZXNzZXVyIGRlIHNvbiBlbmZhbmNlLiBMYXJyeSBOYXNzZXIgYXZhaXQgY2hvaXNpIGxhIG1hdXZhaXNlIGNpYmxlLlxuXG4jIExlIHRlbXBzIGRlIHBhcmxlclxuXG5TZWl6ZSBhbnMgc2Ugc29udCDDqWNvdWzDqXMuIE5vdHJlIHByb3RhZ29uaXN0ZSBlc3QgbWFyacOpZSwgYSB0cm9pcyBlbmZhbnRzIGV0IHZpdCDDoCBMb3Vpc3ZpbGxlLCBkYW5zIGxlIEtlbnR1Y2t5LiBFbGxlIGV4ZXJjZSBsZSBtw6l0aWVyIGQnYXZvY2F0ZSBwZW5kYW50IHF1ZSBzb24gbWFyaSBwcsOpcGFyZSB1biBkb2N0b3JhdCBhdSBTb3V0aGVybiBCYXB0aXN0IFRoZW9sb2dpY2FsIFNlbWluYXJ5LiBSYXNzZW1ibGFudCB0b3V0ZXMgbGVzIHByZXV2ZXMgY29udHJlIE5hc3NlciwgRGVuaG9sbGFuZGVyIGTDqWNpZGUgcXUnaWwgZXN0IHRlbXBzIGRlIHBvcnRlciBwbGFpbnRlIGNvbnRyZSBsZSBtw6lkZWNpbiBkYW5zIHNvbiDDiXRhdCBkJ29yaWdpbmUsIGxlIE1pY2hpZ2FuLiBFbGxlIHNhaXQgcXUndW4gZW5xdcOqdGV1ciBwZXUgY29tcGF0aXNzYW50IGEgbGUgcG91dm9pciBkJ2VudGVycmVyIGxlIGRvc3NpZXIgZXQgZGUgbWV0dHJlIGZpbiDDoCBzZXMgcG91cnN1aXRlcy4gTGUgZmFpdCBlc3QgcXVlIHN1ciAyMzAgdmlvbHMgc2lnbmFsw6lzLCB1biBzZXVsIGVzdCBjb25kYW1uw6kuIE1haXMsIHByb3ZpZGVudGllbGxlbWVudCwgbGUgU2VpZ25ldXIgY2hvaXNpdCB1bmUgcGVyc29ubmUgcXVpIHNvdWhhaXRlIHF1ZSBsYSB2w6lyaXTDqSDDqWNsYXRlIGF1IGdyYW5kIGpvdXIgcGx1dMO0dCBxdWUgZGUgcHJvdMOpZ2VyIHVuIHByw6lkYXRldXIuXG5cbkxhIHByZXNzZSBuZSB0YXJkZSBwYXMgw6AgcsOpYWdpci4gVW4gam91cm5hbGlzdGUgZCdpbnZlc3RpZ2F0aW9uIGTDqWNvdXZyZSBxdWUgY2Ugbidlc3QgcGFzIGxhIHByZW1pw6hyZSBhY2N1c2F0aW9uIHBvcnTDqWUgY29udHJlIE5hc3Nlci4gRGVzIGFsbMOpZ2F0aW9ucyBvbnQgZmFpdCBzdXJmYWNlIGRhbnMgbGUgcGFzc8OpLCBhdmFudCBkJ8OqdHJlIGVudGVycsOpZXMuIEwnYXJ0aWNsZSBwcsOpc2VudGFudCBsZSB0w6ltb2lnbmFnZSBkZSBEZW5ob2xsYW5kZXIgYXR0aXJlIGJpZW50w7R0IGQnYXV0cmVzIHZpY3RpbWVzIHF1aSBvbnQgc291ZmZlcnQgZW4gc2lsZW5jZSBwZW5kYW50IGRlcyBkw6ljZW5uaWVzLiBFbGxlcyB2ZXVsZW50IGVsbGVzIGF1c3NpIGRvbm5lciBkdSBwb2lkcyDDoCBsJ2FmZmFpcmUgZW4gdMOpbW9pZ25hbnQgZGUgY2UgcXUnZWxsZXMgb250IGVuZHVyw6kuXG5cbiMgTGEgbWFyw6llIG1vbnRhbnRlXG5cbkNlIHF1aSBzdWl0IGVzdCB1bmUgc2FnYSBkdSBiaWVuIGNvbnRyZSBsZSBtYWwuIE5hc3NlciBlbmdhZ2UgbGEgbWVpbGxldXJlIMOpcXVpcGUgZGUgZMOpZmVuc2UgcG9zc2libGUgcG91ciBhc3N1cmVyIHNvbiBhY3F1aXR0ZW1lbnQuIENlcGVuZGFudCwgbGVzIHByZXV2ZXMgY29udHJlIGx1aSBzJ2FjY3VtdWxlbnQgY29tbWUgdW5lIG1hcsOpZSBtb250YW50ZS4gVW5lIHBhdGllbnRlIHF1ZSBsZSBtw6lkZWNpbiBhdmFpdCBhYnVzw6llIMOgIHNvbiBkb21pY2lsZSBwbHV0w7R0IHF1ZSBkYW5zIHNvbiBjYWJpbmV0IG3DqWRpY2FsIHNlIHByw6lzZW50ZS4gQ2VsYSBwZXJtZXQgYXV4IGVucXXDqnRldXJzIGRlIHBlcnF1aXNpdGlvbm5lciBzYSBtYWlzb24uIElscyB5IGTDqWNvdXZyZW50IHVuIHN0b2NrIGFjY2FibGFudCBkZSBtYXTDqXJpZWwgcMOpZG9wb3Jub2dyYXBoaXF1ZSBzdXIgc29uIG9yZGluYXRldXIuXG5cbkxlIHZlbnQgdG91cm5lLiBGYWNlIMOgIGxhIGxpc3RlIGNyb2lzc2FudGUgZGVzIHZpY3RpbWVzIGV0IGF1eCBwcmV1dmVzIHRhbmdpYmxlcyB0cm91dsOpZXMgw6Agc29uIGRvbWljaWxlLCBMYXJyeSBOYXNzZXIgcGxhaWRlIGNvdXBhYmxlIGRlIHBsdXNpZXVycyBjaGVmcyBkJ2FjY3VzYXRpb24gZCdhZ3Jlc3Npb24gc2V4dWVsbGUgY29udHJlIHVuZSBtaW5ldXJlLiBUb3V0IGF1IGxvbmcgZGUgc29uIGlsbHVzdHJlIGNhcnJpw6hyZSwgbGUgbcOpZGVjaW4gYSBhYnVzw6kgc2V4dWVsbGVtZW50IGRlIHBsdXMgZGUgMzAwIGZpbGxlcy4gRGVzIGZpbGxlcyBxdWUgRGlldSBhIGNyw6nDqWVzIMOgIHNvbiBpbWFnZS4gRGVzIGZpbGxlcyBkJ3VuZSB2YWxldXIgaW5lc3RpbWFibGUuXG5cbiMgUXVpIHZhIHJldHJvdXZlciBjZXMgcGV0aXRlcyBmaWxsZXM/XG5cbkxlcyBzcMOpY2lhbGlzdGVzIGRlIGxhIGTDqWZlbnNlIGRlcyBlbmZhbnRzIGFmZmlybWVudCBxdSd1bmUgZmlsbGUgc3VyIHF1YXRyZSBldCB1biBnYXLDp29uIHN1ciBzaXggc2Vyb250IHZpY3RpbWVzIGQnYWJ1cyBzZXh1ZWxzIGF2YW50IGwnw6JnZSBkZSAxOCBhbnMuIFN0YXRpc3RpcXVlbWVudCwgY2VsYSByZXByw6lzZW50ZSAyNSAlIGRlcyBmZW1tZXMgcXVpIGxpc2VudCBjZXMgbW90cyBldCAxNyAlIGRlcyBob21tZXMuIE5vbWJyZXV4IHNvbnQgY2V1eCBxdWkgZW5mb3Vpc3NlbnQgbGV1ciBob250ZSBldCBsZXVyIGRvdWxldXIuIEQnYXV0cmVzIHNpZ25hbGVudCBsJ2FidXMgw6AgdW4gYWR1bHRlIGRlIGNvbmZpYW5jZSwgbWFpcyBzb250IHLDqWR1aXRzIGF1IHNpbGVuY2UuIEMnZXN0IGV4YWN0ZW1lbnQgY2UgcXVpIGVzdCBhcnJpdsOpIGF1eCBjZW50YWluZXMgZGUgZmlsbGVzIGFidXPDqWVzIHBhciBMYXJyeSBOYXNzZXIuIEV0IGMnZXN0IGNlIHF1aSBhIGNvbmR1aXQgUmFjaGFlbCBEZW5ob2xsYW5kZXIgw6AgbGFuY2VyIGwnYXBwZWwgc3VpdmFudCBhdSBqdWdlIEFxdWlsaW5vIGRhbnMgc2EgZMOpY2xhcmF0aW9uIGQnaW1wYWN0IGxvcnMgZGUgbGEgPGEgaHJlZj1cIi9hcnRpY2xlL2xpbmNyb3lhYmxlLXRlbW9pZ25hZ2UtZHVuZS1hbmNpZW5uZS1neW1uYXN0ZS1hdS1wcm9jZXMtZGUtY2VsdWktcXVpLWFidXNlLWRlbGxlXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPmNsw7R0dXJlIGR1IHByb2PDqHMgZGUgTGFycnkgTmFzc2VyPC9hPjpcblxuPiBRdWkgdmEgcmV0cm91dmVyIGNlcyBwZXRpdGVzIGZpbGxlcywgcXVpIHZhIGxldXIgZGlyZSBjb21iaWVuIGVsbGVzIHZhbGVudCwgY29tYmllbiBlbGxlcyBzb250IHByw6ljaWV1c2VzLCBjb21iaWVuIGVsbGVzIG3DqXJpdGVudCBqdXN0aWNlIGV0IHByb3RlY3Rpb24/IFF1aSB2YSBkaXJlIMOgIGNlcyBwZXRpdGVzIGZpbGxlcyBxdWUgY2UgcXVpIGxldXIgYSDDqXTDqSBmYWl0IGEgZHUgcG9pZHM/IFF1J2VsbGVzIHNvbnQgdnVlcyBldCBhcHByw6ljacOpZXMsIHF1J2VsbGVzIG5lIHNvbnQgcGFzIHNldWxlcyBldCBxdSdlbGxlcyBuZSBzb250IHBhcyBzYW5zIHByb3RlY3Rpb24/IEplIG4nYWkgcGFzIHB1IGxlIGZhaXJlLCBtYWlzIG5vdXMgc29tbWVzIGljaSBhdWpvdXJkJ2h1aSwgZXQgY2UgbWVzc2FnZSBwZXV0IMOqdHJlIGVudm95w6kgYXVqb3VyZCdodWkuIEF2ZWMgbGEgc2VudGVuY2UgcXVlIHZvdXMgcHJvbm9uY2VyZXosIHZvdXMgcG91cnJleiBkaXJlIMOgIHRvdXRlcyBjZXMgcGV0aXRlcyBmaWxsZXMsIMOgIHRvdXMgbGVzIHByw6lkYXRldXJzLCDDoCB0b3V0ZXMgbGVzIHBldGl0ZXMgZmlsbGVzIG91IGpldW5lcyBmZW1tZXMgcXVpIG5vdXMgcmVnYXJkZW50LCBjb21iaWVuIHZhdXQgdW5lIHBldGl0ZSBmaWxsZS5cblxuIyBRdWUgdmF1dCB1bmUgZmlsbGUgYXV4IHlldXggZGUgRGlldT9cblxuRW4gdGFudCBxdSdlbmZhbnRzIGRlIERpZXUsIG5vdXMgc2F2b25zIGV4YWN0ZW1lbnQgY2UgcXVlIHZhdXQgdW5lIGZpbGxlLiBEaWV1IGwnYWltZSB0ZWxsZW1lbnQgcXUnaWwgbGEgcmVuY29udHJlIGRhbnMgbGUgZMOpc2VydCwgbGEgYsOpbml0IGV0IGVsbGUgbCdhcHBlbGxlIOKAnExlIERpZXUgcXVpIG1lIHZvaXTigJ0gKEduIDE2LjEzKS4gSWwgbCdhaW1lIHRlbGxlbWVudCBxdSdpbCBsYSBwbGFjZSBjb21tZSBqdWdlLCBwcm9waMOodGUgZXQgbcOocmUgZGUgc29uIHBldXBsZSAoSmcgNC40OyA1LjcpLiBJbCBsJ2FpbWUgdGVsbGVtZW50IHF1J2lsIGVudGVuZCBzZXMgY3JpcyBhbWVycyBldCBsdWkgYWNjb3JkZSB1biBmaWxzIGFsb3JzIHF1J2VsbGUgZXN0IHN0w6lyaWxlICgxUyAxLjktMjApLiBJbCBsJ2FpbWUgdGVsbGVtZW50IHF1J2lsIGx1aSBkb25uZSBsZSBwb3V2b2lyIGRlIHBhcmRvbm5lciDDoCBzb24ga2lkbmFwcGV1ciwgbGUgY29tbWFuZGFudCBsw6lwcmV1eCBkZSBsJ2FybcOpZSBzeXJpZW5uZS4gR3LDomNlIMOgIHNvbiB0w6ltb2lnbmFnZSwgaWwgdHJvdXZlIGxhIGd1w6lyaXNvbiBldCBsYSByw6lkZW1wdGlvbiBhdXByw6hzIGRlIGwnw4l0ZXJuZWwgKDJSIDUuMS0yNykuIElsIGwnYWltZSB0ZWxsZW1lbnQgcXUnaWwgbGEgZMOpbGl2cmUgZGUgc2VwdCBkw6ltb25zIChMYyA4LjIpLiBJbCBsJ2FpbWUgdGVsbGVtZW50IHF1J2lsIGltbW9ydGFsaXNlIHNvbiBkw6l2b3VlbWVudCBlbiBwcsOpcGFyYW50IHNvbiBjb3JwcyBwb3VyIGwnZW50ZXJyZW1lbnQgKE1jIDE0LjMtOSkuXG5cbklsIGwnYWltZSB0ZWxsZW1lbnQgcXUnaWwgZW52b2llIHNvbiBGaWxzIHVuaXF1ZSB2ZXJzZXIgc29uIHNhbmcgcG91ciBsYSBzYXV2ZXIuIEV0IGlsIGwnYWltZSB0ZWxsZW1lbnQgcXUnaWwgbHVpIGFwcGFyYcOudCBhcHLDqHMgc2EgcsOpc3VycmVjdGlvbiBldCBsYSBjaGFyZ2UgZGUgdMOpbW9pZ25lciBkZSBzYSB2aWN0b2lyZSBzdXIgbGUgdG9tYmVhdS4gU2kgdGVsbGUgZXN0IGwnw6l0ZW5kdWUgZGUgbCdhbW91ciBkZSBEaWV1LCBhbG9ycyBuZSBsZXMgYWltb25zIHBhcyBtb2lucy5cblxuIyMgUG91ciBhbGxlciBwbHVzIGxvaW5cblxuKiBWb3VzIHBvdXZleiB0cm91dmVyIGxlIGxpdnJlIGVuIHZlbnRlIGljaTogKltRdWUgdmF1dCB1bmUgZmlsbGU/IExlIGNvbWJhdCBk4oCZdW5lIGd5bW5hc3RlIHBvdXIgZMOpbm9uY2VyIGxlcyBhYnVzIGR1IGRvY3RldXIgTmFzc2FyXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9xdWUtdmF1dC11bmUtZmlsbGUpKiwgZGUgUmFjaGFlbCBEZW5ob2xsYW5kZXIsIMOJZGl0aW9ucyBPVVJBTklBXG5cblxcXG4iLCJhdXRob3IiOnsiZnVsbE5hbWUiOiJBbmdpZSBWZWxhc3F1ZXogVGhvcm50b24iLCJwaWN0dXJlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvSU1HXzI3MjVfNGE3ZGZkMDg3My5qcGVnIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifX0sImltYWdlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvYW50aG9ueV90cmFuX2lfZV9QdjlfRHhnN19VX3Vuc3BsYXNoXzczY2E1YThjNWQuanBnIiwiaGVpZ2h0IjoxMjgwLCJ3aWR0aCI6MTkyMCwiYWx0ZXJuYXRpdmVUZXh0IjoiIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifSwidG9waWNzIjpbeyJuYW1lIjoiQW1vdXIgZGUgRGlldSJ9LHsibmFtZSI6IlNleHVhbGl0w6kifSx7Im5hbWUiOiJTb3VmZnJhbmNlIn0seyJuYW1lIjoiw4l0aGlxdWUifSx7Im5hbWUiOiJKdXN0aWNlIHNvY2lhbGUifV0sIm1vZHVsZXMiOlt7Il9fdHlwZW5hbWUiOiJDb21wb25lbnRNb2R1bGVMZWFkIiwiY29udGVudCI6IkRhbnMgc29uIG91dnJhZ2UgYXV0b2Jpb2dyYXBoaXF1ZSBpbnRpdHVsw6kgKltRdWUgdmF1dCB1bmUgZmlsbGU/IExlIGNvbWJhdCBk4oCZdW5lIGd5bW5hc3RlIHBvdXIgZMOpbm9uY2VyIGxlcyBhYnVzIGR1IGRvY3RldXIgTmFzc2FyXShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9xdWUtdmF1dC11bmUtZmlsbGUpKiwgUmFjaGFlbCBEZW5ob2xsYW5kZXIgcmFjb250ZSBzb24gcGFyY291cnMgw6lwcm91dmFudCBwb3VyIGZhaXJlIHRvbWJlciBsJ3VuIGRlcyBwaXJlcyBww6lkb3BoaWxlcyBkZSBsJ0hpc3RvaXJlLiJ9LHt9XX0seyJpZCI6IjQwNDkiLCJ0aXRsZSI6IkNvbW1lbnQgZG9ubmVyIMOgIG5vcyBlbmZhbnRzIGVudmllIGRlIHByaWVyPyIsInNsdWciOiJwb3VycXVvaS12b3VzLWRldnJpZXotbGlyZS1kaXMtbHVpLXRvdXQtcGFydG91dC10b3Vqb3VycyIsInR5cGUiOiJhcnRpY2xlIiwicHVibGlzaGVkX2F0IjoiMjAyNC0wNi0yN1QwMzo0NTowMC4wMDBaIiwiYm9keSI6IiMgQXUgY29tbWVuY2VtZW50XG5cbkNlIHBldGl0IG1hbnVlbCBzdXIgbGEgcHJpw6hyZSBzJ291dnJlIHN1ciBsZSBqYXJkaW4uIERhbnMgdG91dGUgbCdoaXN0b2lyZSBkZSBsJ2h1bWFuaXTDqSwgYXVjdW4gYXV0cmUgbGlldSBvdSBtb21lbnQgbidhIHBlcm1pcyB1bmUgdGVsbGUgY29tbXVuaW9uIHNhbnMgZW50cmF2ZSBhdmVjIERpZXUuIE1haXMgbG9yc3F1ZSBsZSBww6ljaMOpIGVzdCBlbnRyw6kgZGFucyBsZSBtb25kZSwgaWwgYSBicmlzw6kgbGEgbGliZXJ0w6kgZG9udCBqb3Vpc3NhaWVudCBub3MgcHJlbWllcnMgcGFyZW50cy4gTGUgcMOpY2jDqSBhIHJlbmR1IGxhIHByacOocmUgZGlmZmljaWxlLCBtYWlzIHBhcyBpbXBvc3NpYmxlLlxuXG4jIEwnaW52aXRhdGlvbiBkZW1ldXJlXG5cbkRpZXUgYSByYXBwZWzDqSDDoCBzb24gcGV1cGxlIHF1ZSBsJ2ludml0YXRpb24gcmVzdGFpdCBvdXZlcnRlIHBvdXIgcXUnaWwgdmllbm5lIMOgIGx1aSDDoCB0b3V0IG1vbWVudCBldCBlbiB0b3V0IGxpZXUuIEMnZXN0IGNlIHF1J2lsIGEgZmFpdC4gV2lsZmVyIGV4cGxpcXVlIHF1J2Vuc3VpdGUsIERpZXUgYSBlbnZvecOpIHNvbiBGaWxzIGRhbnMgbGUgbW9uZGUuIElsIGEgZW5zZWlnbsOpIMOgIHNvbiBwZXVwbGUgw6AgcHJpZXIgcGFyIHNvbiBleGVtcGxlIGRlIGTDqXBlbmRhbmNlIGV0IGRlIGNvbW11bmlvbiBhdmVjIGxlIFDDqHJlLCBhaW5zaSBxdWUgcGFyIHNlcyBpbnN0cnVjdGlvbnMgc3VyIGxhIHByacOocmUgKGxlIE5vdHJlIFDDqHJlKS5cblxuQXByw6hzIHNhIG1vcnQsIHNhIHLDqXN1cnJlY3Rpb24gZXQgc29uIGFzY2Vuc2lvbiwgcG91cnN1aXQtZWxsZSwgSsOpc3VzIGEgZW52b3nDqSBzb24gU2FpbnQtRXNwcml0IGNvbW1lIGlsIGwnYXZhaXQgcHJvbWlzLiBFdCBpbCBub3VzIGNvbmR1aXQgw6AgcHJpZXIgY29tbWUgbGUgZmFpc2FpZW50IGxlcyBzYWludHMgZGUgbCdBbmNpZW4gVGVzdGFtZW50LiBNbWUgV29sZmVyIHByw6lzZW50ZSBkaXZlcnMgc2PDqW5hcmlvcyBkYW5zIGxlc3F1ZWxzIG5vdXMgcG91dm9ucyBwYXJsZXIgw6AgRGlldS4gw4AgdG91dCBtb21lbnQuIE4naW1wb3J0ZSBvw7kuIFF1ZSBub3VzIGRlbWFuZGlvbnMgbGEgZ3XDqXJpc29uIGQndW4gYW1pIG1hbGFkZSBvdSBxdWUgbm91cyBub3VzIHLDqXZlaWxsaW9ucyBkJ3VuIHLDqnZlIGVmZnJheWFudC4gUXVlIG5vdXMgc295b25zIGRhbnMgbGEgdm9pdHVyZSBvdSBkYW5zIG5vdHJlIGNoYW1icmUsIGF2ZWMgZGVzIGFtaXMgZXQgZGUgbGEgZmFtaWxsZSwgb3UgdG91dCBzZXVsLlxuXG5XaWxmZXIgY29uY2x1dCBhdmVjIGxhIG1laWxsZXVyZSBub3V2ZWxsZSBxdWkgc29pdDogVW4gam91ciwgSsOpc3VzIHJldmllbmRyYSBldCBub3VzIHZpdnJvbnMgYXZlYyBsdWkgZGFucyB1biBtb25kZSBwYXJmYWl0IGV0IG1lcnZlaWxsZXV4IG/DuSBub3VzIHBvdXJyb25zIGx1aSBwYXJsZXIgZmFjZSDDoCBmYWNlLCBjb21tZSBBZGFtIGV0IMOIdmUgbCdvbnQgZmFpdC4gRXQgcGVuZGFudCBxdWUgbm91cyBhdHRlbmRvbnMsIHBvdXJxdW9pIG5lIHBhcyBsdWkgcGFybGVyIGTDqHMgbWFpbnRlbmFudD8gUXVlbGxlIGZpbiBhcHByb3ByacOpZSBwb3VyIHVuIGxpdnJlIHN1ciBsYSBwcmnDqHJlISBVbmUgaW52aXRhdGlvbiDDoCBmYWlyZSBleGFjdGVtZW50IGNlbGEuXG5cbiMgVW5lIHJlcHLDqXNlbnRhdGlvbiBkZSB0b3VzIGxlcyBlbmZhbnRzIGRlIERpZXVcblxuQ29tbWUgYydlc3Qgc291dmVudCBsZSBjYXMgZGFucyBzZXMgbGl2cmVzIHBvdXIgZW5mYW50cywgRWNoZXZlcnJpIGTDqXBlaW50IGRlcyBmYW1pbGxlcyBkJ29yaWdpbmVzIGV0aG5pcXVlcyBldCBjdWx0dXJlbGxlcyBkaXZlcnNlcy4gRWxsZSBtb250cmUgw6lnYWxlbWVudCB1biBnYXLDp29uIGVuIGZhdXRldWlsIHJvdWxhbnQgcXVpIHByaWUgZGFucyBsZXMgdG9pbGV0dGVzIGV0IHVuZSBmaWxsZSDDoCBtb2JpbGl0w6kgcsOpZHVpdGUgcXVpIHBhcnQgw6AgbCdhdmVudHVyZSBhdmVjIHNvbiBww6hyZS4gSidpbWFnaW5lIGRlcyBwZXRpdHMgZW5mYW50cyBsaXNhbnQgY2UgbGl2cmUgYXZlYyBsZXVycyBwYXJlbnRzIGV0IGZhaXNhbnQgam95ZXVzZW1lbnQgcmVtYXJxdWVyOiAq4oCcw4dhIG1lIHJlc3NlbWJsZSHigJ0qXG5cbiMgQ29uY2x1c2lvblxuXG5gPHA+w4AgbCdhcHByb2NoZSBkZSBsJ8OpdMOpLCBwb3VycXVvaSBuZSBwYXMgZW52aXNhZ2VyIGRlIGZhaXJlIHByb3Zpc2lvbiBkZSBsaXZyZXMgaW50w6lyZXNzYW50cyBjb21tZSBjZWx1aS1jaSBwb3VyIGxlcyBlbmZhbnRzIGRlIHZvdHJlIGVudG91cmFnZT8gSmUgcmVjb21tYW5kZSDDqWdhbGVtZW50IGxlIGNhaGllciBkJ2FjdGl2aXTDqXMgcXVpIGwnYWNjb21wYWduZS4gUXVvaSBkZSBtaWV1eCBxdWUgZGUgbm91dmVhdXggbGl2cmVzIGJyaWxsYW50cyBwb3VyIMOpdml0ZXIgbGUgcGnDqGdlIGR1IHRlbXBzIHBhc3PDqSBkZXZhbnQgdW4gw6ljcmFuIHBlbmRhbnQgbGVzIHZhY2FuY2VzPyBSZXRyb3V2ZXogbGUgbGl2cmUgPGEgaHJlZj1cImh0dHBzOi8vYmxmc3RvcmUuY29tL2NvbGxlY3Rpb25zL2FsbC9wcm9kdWN0cy9kaXMtbHVpLXRvdXQtcGFydG91dC10b3Vqb3Vyc1wiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj5pY2kgZW4gRXVyb3BlPC9hPiBldCA8YSBocmVmPVwiaHR0cHM6Ly9wdWJsaWNhdGlvbnNjaHJldGllbm5lcy5jb20vcHJvZHVjdHMvZGlzLWx1aS10b3V0LXBhcnRvdXQtdG91am91cnNcIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+aWNpIGF1IFF1w6liZWM8L2E+IGFpbnNpIHF1ZSBsZSBjYWhpZXIgZCdhY3Rpdml0w6lzIDxhIGhyZWY9XCJodHRwczovL2JsZnN0b3JlLmNvbS9jb2xsZWN0aW9ucy9hbGwvcHJvZHVjdHMvY2FoaWVyLWRhY3Rpdml0ZXMtZGlzLWx1aS10b3V0LXBhcnRvdXQtdG91am91cnNcIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+aWNpIGVuIEV1cm9wZTwvYT4gZXQgPGEgaHJlZj1cImh0dHBzOi8vcHVibGljYXRpb25zY2hyZXRpZW5uZXMuY29tL3Byb2R1Y3RzL2Rpcy1sdWktdG91dC1wYXJ0b3V0LXRvdWpvdXJzXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPmljaSBhdSBRdcOpYmVjPC9hPi48L3A+YCIsImF1dGhvciI6eyJmdWxsTmFtZSI6IkFuZ2llIFZlbGFzcXVleiBUaG9ybnRvbiIsInBpY3R1cmUiOnsidXJsIjoiaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9JTUdfMjcyNV80YTdkZmQwODczLmpwZWciLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9fSwiaW1hZ2UiOnsidXJsIjoiaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9EaXNfbHVpX3RvdXRfcGFydG91dF90b3Vqb3Vyc180NzBiNTcyMGNkLnBuZyIsImhlaWdodCI6ODUzLCJ3aWR0aCI6MTI4MCwiYWx0ZXJuYXRpdmVUZXh0IjoiIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifSwidG9waWNzIjpbeyJuYW1lIjoiUHJpw6hyZSJ9LHsibmFtZSI6IkVuZmFuY2UgZXQgamV1bmVzc2UifSx7Im5hbWUiOiJSZWNlbnNpb24gZGUgbGl2cmUifV0sIm1vZHVsZXMiOlt7Il9fdHlwZW5hbWUiOiJDb21wb25lbnRNb2R1bGVMZWFkIiwiY29udGVudCI6IkxhIHByacOocmUgdm91cyB2aWVudC1lbGxlIG5hdHVyZWxsZW1lbnQ/IENlIG4nZXN0IGNlcnRhaW5lbWVudCBwYXMgbGUgY2FzIHBvdXIgbW9pLiBMZXMgZGlzY2lwbGVzIGRlIErDqXN1cyBldXgtbcOqbWVzIGF2YWllbnQgYmVzb2luIGQnYXBwcmVuZHJlIMOgIHByaWVyLiBJbCBlbiB2YSBkZSBtw6ptZSBwb3VyIGxlcyBlbmZhbnRzLiBDJ2VzdCBwb3VycXVvaSBqJ2FwcHLDqWNpZSBjZSBjaGFybWFudCBsaXZyZSBkZXN0aW7DqSBhdXggZW5mYW50cyBkZSAzIMOgIDYgYW5zOiAqW0Rpcy1sdWkgdG91dCwgcGFydG91dCwgdG91am91cnNdKGh0dHBzOi8vYmxmc3RvcmUuY29tL2NvbGxlY3Rpb25zL2FsbC9wcm9kdWN0cy9kaXMtbHVpLXRvdXQtcGFydG91dC10b3Vqb3VycykqLCDDqWNyaXQgcGFyIExhdXJhIFdpZmxlciBldCBpbGx1c3Ryw6kgcGFyIENhdGFsaW5hIEVjaGV2ZXJyaS4ifSx7fV19LHsiaWQiOiI0MDM3IiwidGl0bGUiOiJEw6ljb3V2cmV6IGxlIGxpdnJlIHBvdXIgZW5mYW50cywgXCJKZSB2b2lzIErDqXN1c1wiIiwic2x1ZyI6InBvdXJxdW9pLWxpcmUtamUtdm9pcy1qZXN1cy1uYW5jeS1ndXRocmllIiwidHlwZSI6ImFydGljbGUiLCJwdWJsaXNoZWRfYXQiOiIyMDI0LTA2LTIwVDAzOjQ1OjAwLjAwMFoiLCJib2R5IjoiIyMgTGUgZMOpZmk6IGRlcyBoaXN0b2lyZXMgZMOpY29ycsOpbMOpZXNcblxuTCd1bmUgZGVzIHJhaXNvbnMgZXN0IHF1ZSBsYSBwbHVwYXJ0IGQnZW50cmUgbm91cyBuJ2F2b25zIHBhcyBldSBjZSBjYWRyZSBkw6hzIG5vcyBwcmVtacOocmVzIGludGVyYWN0aW9ucyBhdmVjIGxhIEJpYmxlLiBEYW5zIGxlcyDDqWNvbGVzIGR1IGRpbWFuY2hlIGV0IGxlcyBsaXZyZXMgcG91ciBlbmZhbnRzLCBub3VzIGF2b25zIGFwcHJpcyBsZXMgaGlzdG9pcmVzIGQnQWRhbSBldCDDiHZlLCBkZSBOb8OpIGV0IGRlIHNhIGZhbWlsbGUsIGQnQWJyYWhhbSBldCBkZSBTYXJhaCwgZGUgRGF2aWQgZXQgR29saWF0aCwgZGUgRGFuaWVsIGV0IGRlcyBsaW9ucywgZXQgYmllbiBkJ2F1dHJlcyBlbmNvcmUuIE1haXMgaWwgcydhZ2lzc2FpdCBnw6luw6lyYWxlbWVudCBkJ2hpc3RvaXJlcyBkw6ljb3Jyw6lsw6llcy4gTm91cyBuJ2F2b25zIHBhcyBhcHByaXMgbGV1ciBsaWVuIMOgIGxhIGdyYW5kZSBoaXN0b2lyZSAoYydlc3Qtw6AtZGlyZSBsZSBtw6l0YS1yw6ljaXQpLlxuXG4jIyBMYSBzb2x1dGlvbjogY2hlcmNoZXIgbGVzIG9tYnJlc1xuXG5EYW5zIHNlcyBwcmVtacOocmVzIHBhZ2VzLCBHdXRocmllIHV0aWxpc2UgbCdpbGx1c3RyYXRpb24gYXBwcm9wcmnDqWUgZGUgUGF1bCBkYW5zIENvbG9zc2llbnMgMi4xNy4gVG91dCBjZWxhIG4nw6l0YWl0IHF1ZSBsJ29tYnJlIGRlcyBjaG9zZXMgw6AgdmVuaXIsIG1haXMgbGEgcsOpYWxpdMOpIGVzdCBlbiBDaHJpc3QuIEVsbGUgZXhwbGlxdWUgcXVlIGwnQW5jaWVuIFRlc3RhbWVudCBlc3QgcmVtcGxpIGQnb21icmVzIGRlIENocmlzdC4gQ2VsbGVzLWNpIG5lIG5vdXMgZGlzZW50IHBhcyB0b3V0IHN1ciBsdWkuIE1haXMgZWxsZXMgbm91cyBlbiBkaXNlbnQgYmVhdWNvdXAgw6Agc29uIHN1amV0LiBFbGxlIG5vdXMgZW1tw6huZSBlbnN1aXRlIMOgIHRyYXZlcnMgcXVlbHF1ZXMtdW5lcyBkZXMgaGlzdG9pcmVzIGxlcyBwbHVzIGFpbcOpZXMgZGUgbCdBbmNpZW4gVGVzdGFtZW50LiBFbGxlIGV4cGxpcXVlIGJyacOodmVtZW50IGNoYWN1bmUgZCdlbnRyZSBlbGxlcy4gUHVpcywgZWxsZSBub3VzIG1vbnRyZSwgw6AgcGFydGlyIGRlcyDDiWNyaXR1cmVzLCBjb21tZW50IGNlIHLDqWNpdCBjb250ZW5haXQgbCdvbWJyZSwgZG9udCBsYSByw6lhbGl0w6kgc2UgdHJvdXZlIGVuIENocmlzdC4gTm91cyByZWNvbm5haXNzb25zIGNlcnRhaW5lcyBkZSBjZXMgb21icmVzIHBsdXMgZmFjaWxlbWVudCBxdWUgZCdhdXRyZXMuIFF1ZSBKw6lzdXMgc29pdCBsZSBwbHVzIGdyYW5kIEFkYW0gbmUgbm91cyBzdXJwcmVuZCBwYXMgKFJtIDUuMTkpLiBQYXMgcGx1cyBxdWUgbGUgZmFpdCBxdWUgbGUgRmlscyBzb2l0IGwnQWduZWF1IFBhc2NhbCAoSm4gMS4yOSkuXG5cbk1haXMgY2VydGFpbmVzIG9tYnJlcyBxdWUgR3V0aHJpZSBtZXQgZW4gbHVtacOocmUgc29udCB0b3V0IGF1c3NpIG3DqXJpdGFudGVzLCBldCBlbGxlcyDDqXRhaWVudCBub3V2ZWxsZXMgcG91ciBtb2kuIFBhciBleGVtcGxlLCBlbiByZWdhcmRhbnQgbGUgY2llbCBub2N0dXJuZSwgQWJyYWhhbSBzZSBzb3V2aWVudCBkZSBsYSBwcm9tZXNzZSBkZSBEaWV1IGRlIGx1aSBkb25uZXIgYXV0YW50IGRlIGRlc2NlbmRhbnRzIHF1ZSBkJ8OpdG9pbGVzIGRhbnMgbGUgY2llbC4gRGFucyBsJ29tYnJlIGRlIGNlcyDDqXRvaWxlcywgbm91cyB2b3lvbnMgSsOpc3VzLCBcImwnw6l0b2lsZSBicmlsbGFudGUgZHUgbWF0aW5cIiAoQXAgMjIuMTYpLiBKJ2Fkb3JlIGNlbGEhIEVuIG5vdXMgZmFpc2FudCBwYXJjb3VyaXIgZGUgY2V0dGUgbWFuacOocmUgY2VydGFpbnMgZGVzIHLDqWNpdHMgbGVzIHBsdXMgYWltw6lzIGRlIGwnQW5jaWVuIFRlc3RhbWVudCwgbCdhdXRldXIgZW5zZWlnbmUgYXV4IGpldW5lcyBldCBhdXggbW9pbnMgamV1bmVzIGNvbW1lbnQgaW50ZXJwcsOpdGVyIGwnw4ljcml0dXJlLiBDaGVyY2hleiBsZXMgb21icmVzIVxuXG4jIyBMZSBwb3V2b2lyIGRlcyBpbWFnZXNcblxuSmVubnkgQnJha2UgZmFpdCB1biBleGNlbGxlbnQgdHJhdmFpbCBwb3VyIGRvbm5lciB2aWUgYXV4IG1vdHMgc3VyIGxhIHBhZ2UuIE1vw69zZSBhIMOpbGV2w6kgbGUgc2VycGVudCBkJ2FpcmFpbiBkYW5zIGxlIGTDqXNlcnQuIEplbm55IEJyYWtlIGZhaXQgw6ljaG8gYXV4IHBhcm9sZXMgZGUgSmVhbiAzOjE0LTE1IGVuIGRlc3NpbmFudCB1bmUgY3JvaXggw6AgbCdvbWJyZSBkZSBjZSBzZXJwZW50LiBKYWNvYiBiw6luaXQgSnVkYSBzdXIgc29uIGxpdCBkZSBtb3J0LiBFbGxlIGlsbHVzdHJlIGNlIG1vbWVudCBlbiByZXByw6lzZW50YW50IGwnb21icmUgZCd1biBsaW9uIHN1ciBsZSBtdXIgZGVycmnDqHJlIGxlIGZpbHMgw6lsdS4gVG91dCBhdSBsb25nIGR1IGxpdnJlLCBCcmFrZSBicm9zc2UgdW4gcG9ydHJhaXQgZGUgY2UgcXVlIEd1dGhyaWUgZXhwbGlxdWUgc2kgbWFnaXN0cmFsZW1lbnQgYXZlYyBkZXMgbW90cy5cblxuIyMgQ29uY2x1c2lvblxuXG5KJ2FpIHRyb3V2w6kgY2UgcGV0aXQgbGl2cmUgcG91ciBsZXMgZW5mYW50cyBkZSA0IMOgIDcgYW5zIGluc3RydWN0aWYgZXQgZW5yaWNoaXNzYW50LiBKZSBzdWlzIGNvbnZhaW5jdWUgcXVlIHZvdXMgZXQgdm9zIGVuZmFudHMgZW4gZmVyZXogYXV0YW50LiBWb3VzIHBvdXZleiB0cm91dmVyIGxlIGxpdnJlLCAqSmUgdm9pcyBKw6lzdXMqLCDDqWNyaXQgcGFyIE5hbmN5IEd1dGhyaWUgZXQgaWxsdXN0csOpIHBhciBKZW5ueSBCcmFrZSBbaWNpIGVuIEZyYW5jZV0oaHR0cHM6Ly9ibGZzdG9yZS5jb20vcHJvZHVjdHMvamUtdm9pcy1qZXN1cy1kYW5zLWxhbmNpZW4tdGVzdGFtZW50KSBldCBbaWNpIGF1IFF1w6liZWNdKGh0dHBzOi8vcHVibGljYXRpb25zY2hyZXRpZW5uZXMuY29tL3Byb2R1Y3RzL2plLXZvaXMtamVzdXMtZGFucy1sYW5jaWVuLXRlc3RhbWVudD9fcG9zPTE3Jl9zaWQ9ZWNkMjFkMDY4Jl9zcz1yKS4iLCJhdXRob3IiOnsiZnVsbE5hbWUiOiJBbmdpZSBWZWxhc3F1ZXogVGhvcm50b24iLCJwaWN0dXJlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvSU1HXzI3MjVfNGE3ZGZkMDg3My5qcGVnIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifX0sImltYWdlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvVm9pcl9KZXN1c19kMWZmNzVkNzNjLnBuZyIsImhlaWdodCI6ODUzLCJ3aWR0aCI6MTI4MCwiYWx0ZXJuYXRpdmVUZXh0IjoiIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifSwidG9waWNzIjpbeyJuYW1lIjoiSGVybcOpbmV1dGlxdWUifSx7Im5hbWUiOiJFbmZhbmNlIGV0IGpldW5lc3NlIn0seyJuYW1lIjoiUmVjZW5zaW9uIGRlIGxpdnJlIn1dLCJtb2R1bGVzIjpbeyJfX3R5cGVuYW1lIjoiQ29tcG9uZW50TW9kdWxlTGVhZCIsImNvbnRlbnQiOiJEZXB1aXMgcGx1c2lldXJzIGFubsOpZXMsIGwndW5lIGRlIG1lcyBwcmluY2lwYWxlcyB2b2NhdGlvbnMgZGFucyBsZSBtaW5pc3TDqHJlIGVzdCBkZSBmb3JtZXIgZGVzIGZlbW1lcyDDoCBleHBvc2VyIGxhIEJpYmxlLiBBdSBjxZN1ciBkZSBjZXR0ZSBmb3JtYXRpb24gc2UgdHJvdXZlIGxlIGTDqXNpciBkZSBsZXMgYWlkZXIgw6AgZMOpY291dnJpciBDaHJpc3Qgw6AgcGFydGlyIGRlIGwnZW5zZW1ibGUgZGVzIMOJY3JpdHVyZXMuIEJlYXVjb3VwIGRlIGNlcyBjaMOocmVzIHPFk3VycyBvbnQgZ3JhbmRpIGRhbnMgbCfDiWdsaXNlLiBQb3VydGFudCwgY2V0dGUgYXBwcm9jaGUgZGUgbCdpbnRlcnByw6l0YXRpb24gYmlibGlxdWUgKGMnZXN0LcOgLWRpcmUgbCdoZXJtw6luZXV0aXF1ZSBjaHJpc3RvY2VudHJpcXVlKSBlc3Qgbm91dmVsbGUgcG91ciB1biBncmFuZCBub21icmUgZCdlbnRyZSBlbGxlcy4ifSx7fV19LHsiaWQiOiI0MDMxIiwidGl0bGUiOiJMZSBwYXJjb3VycyBkZSBsJ2ludGVycHLDqXRhdGlvbjogdW4gZ3VpZGUgc2ltcGxlIHBvdXIgw6l0dWRpZXIgbGEgQmlibGUiLCJzbHVnIjoiZ3VpZGUtc2ltcGxlLWV0dWRpZXItYmlibGUiLCJ0eXBlIjoiYXJ0aWNsZSIsInB1Ymxpc2hlZF9hdCI6IjIwMjQtMDYtMTNUMDM6NDU6MDAuMDAwWiIsImJvZHkiOiJUb3V0IGNyb3lhbnQgcGV1dCBldCBkb2l0IGFwcHJlbmRyZSDDoCBpbnRlcnByw6l0ZXIgbGVzIMOJY3JpdHVyZXMgcG91ciBsdWktbcOqbWUuIE5vdXMgZGV2cmlvbnMgbGUgZmFpcmUgZW4gY29tbXVuYXV0w6ksIGVuIG5vdXMgYXBwdXlhbnQgc3VyIGxhIHNhZ2Vzc2UgY29sbGVjdGl2ZSBkZSBub3MgbGVhZGVycyBzcGlyaXR1ZWxzLCBkZSBsYSBmYW1pbGxlIGRlIERpZXUgZGFucyBub3RyZSDDiWdsaXNlIGxvY2FsZSwgZGVzIHNhaW50cyDDoCB0cmF2ZXJzIGwnaGlzdG9pcmUgZGUgbOKAmcOJZ2xpc2UsIGV0IGRlcyBmcsOocmVzIGV0IGRlcyBzxZN1cnMgw6AgdHJhdmVycyBsZSBtb25kZS5cblxuQ2VwZW5kYW50LCBub3VzIG5lIGRldm9ucyBwYXMgY29tcHRlciBleGNsdXNpdmVtZW50IHN1ciBsZXMgYXV0cmVzIHBvdXIgbm91cyBlbnNlaWduZXIgbGEgUGFyb2xlIGRlIERpZXUuIExlIHRyYXZhaWwgZGVzIHBhc3RldXJzIGVzdCBkZSBwcm90w6lnZXIgbGV1cnMgdHJvdXBlYXV4IGRlcyBmYXV4IGVuc2VpZ25hbnRzIGV0IGxlcyDDqXF1aXBlciBwb3VyIGwnxZN1dnJlIGR1IG1pbmlzdMOocmUuIERpZXUgYSBkb3TDqSBjaGFxdWUgY2hyw6l0aWVuIGR1IFNhaW50LUVzcHJpdCBwb3VyIHF1J2lsIG5vdXMgZ3VpZGUgZGFucyB0b3V0ZSBsYSB2w6lyaXTDqSAoSm4gMTYuMTMpLiBIZXVyZXVzZW1lbnQsIGxlIHByb2Nlc3N1cyBlc3QgYmllbiBwbHVzIHNpbXBsZSBxdSdvbiBuZSBwb3VycmFpdCBsZSBjcm9pcmUuIElsIG5vdXMgc3VmZml0IGRlIHN1aXZyZSB1biBwYXJjb3VycyBzaW1wbGUgZW50cmUgbGUgdGV4dGUgYW5jaWVuIChldXgvw6AgbGV1ciDDqXBvcXVlKSBldCBsZSAyMeG1iSBzacOoY2xlIChub3VzL2F1am91cmQnaHVpKS4gQ2VsYSByZXNzZW1ibGUgw6AgcGV1IHByw6hzIMOgIGNlY2k8c3VwPjE8L3N1cD5cblxuYDxkaXYgYWxpZ249XCJjZW50ZXJcIj48aW1nIHNyYz1cImh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvU2NoZW1hX2YzNGJkNzRlY2UucG5nXCIgd2lkdGg9MTAwJSBoZWlnaHQ9NDAlPjwvZGl2PmBcblxuIyBJZGVudGlmaWVyIGxhIHN0cnVjdHVyZVxuXG5Db21tZW5jZXogcGFyIGxlIHRleHRlIGRlIHZvdHJlIGNob2l4LCBlbiBiYXMgw6AgZ2F1Y2hlIGRlIGNlIGdyYXBoaXF1ZS4gSW1hZ2luZXogcXVlIHZvdXMgc291aGFpdGlleiBjb21wcmVuZHJlIEV4b2RlIDEuIExhIHByZW1pw6hyZSDDqXRhcGUgZGUgbCdpbnRlcnByw6l0YXRpb24gY29uc2lzdGUgw6AgaWRlbnRpZmllciBsYSBzdHJ1Y3R1cmUgZHUgcGFzc2FnZS4gRW4gZWZmZXQsIHRvdXQgdGV4dGUgcG9zc8OoZGUgdW5lIHN0cnVjdHVyZS4gQ2V0dGUgc3RydWN0dXJlIHLDqXbDqGxlIHVuIHBvaW50IGNlbnRyYWwuIENlIHBvaW50IGNlbnRyYWwgZG9pdCBmYcOnb25uZXIgbm90cmUgbWVzc2FnZS4gTGEgbWVpbGxldXJlIG3DqXRob2RlIHBvdXIgZMOpdGVybWluZXIgbGEgc3RydWN0dXJlIGQndW4gdGV4dGUgZMOpcGVuZCBkdSB0eXBlIGRlIHRleHRlIHF14oCZb24gdHJhaXRlLlxuXG4jIExhIHBvw6lzaWVcblxuVW4gcHNhdW1lLCBwYXIgZXhlbXBsZSwgZXN0IGRpdmlzw6kgZW4gc3Ryb3BoZXMgcXVlIG5vdXMgcG91dm9ucyBpZGVudGlmaWVyIGVuIHJlbWFycXVhbnQgbCd1dGlsaXNhdGlvbiBkZSBsYSByw6lww6l0aXRpb24sIGR1IGNvbnRyYXN0ZSBldCBkZXMgY2hhbmdlbWVudHMgZGUgdGVtcHMsIGRlIHRvbiwgZGUgbG9jdXRldXIgZXQgZGUgcGVyc29ubmUocykgw6AgbGFxdWVsbGUvYXV4cXVlbGxlcyBpbCBzJ2FkcmVzc2UuIFBhciBleGVtcGxlLCBsJ2F1dGV1ciBjb21tZW5jZS10LWlsIHBhciBhZHJlc3NlciBkZXMgbG91YW5nZXMgw6AgRGlldT8gU2UgdG91cm5lLXQtaWwgZW5zdWl0ZSB2ZXJzIGxlIHBldXBsZSBkZSBEaWV1IGV0IGwnZXhob3J0ZS10LWlsIMOgIGZhaXJlIGNvbmZpYW5jZSDDoCBs4oCZw4l0ZXJuZWw/IFBldXQtw6p0cmUgc2UgdG91cm5lLXQtaWwgZW5zdWl0ZSB2ZXJzIGxlcyBuYXRpb25zIGVudmlyb25uYW50ZXMsIGxlcyBpbnZpdGFudCDDoCBhZG9yZXIgbGUgRGlldSBkJ0lzcmHDq2w/IFZvaWzDoCBxdWVscXVlcyBpbmRpY2VzIHF1aSBwZXV2ZW50IG5vdXMgYWlkZXIgw6AgZMOpdGVybWluZXIgbGEgc3RydWN0dXJlIGQndW4gcG/DqG1lLlxuXG4jIExlIGRpc2NvdXJzXG5cbkNlIHR5cGUgZGUgdGV4dGUgc2UgcmV0cm91dmUgbGUgcGx1cyBzb3V2ZW50IGRhbnMgbGVzIMOpcMOudHJlcy4gUG91ciBlbiB0cm91dmVyIGxhIHN0cnVjdHVyZSwgbm91cyBkZXZyb25zIHLDqXZpc2VyIGxhIGdyYW1tYWlyZSBldCB0ZW5pciBjb21wdGUgZGVzIGNvbmpvbmN0aW9ucyAobW90cyBvdSBwaHJhc2VzIGRlIGxpYWlzb24gdGVscyBxdWUg4oCcZG9uYywgYWluc2kgcXVlLCBldCwgbWFpcywgcGFyY2UgcXVl4oCdKSwgZGVzIHByw6lwb3NpdGlvbnMgKG1vdHMgcXVpIGluZGlxdWVudCBsZXMgcmVsYXRpb25zIGVudHJlIGxlcyDDqWzDqW1lbnRzIGQndW5lIHBocmFzZSwgdGVscyBxdWUg4oCcYXUtZGVzc3VzLCBhdS1kZXNzb3VzLCBhdmFudCwgYXByw6hzLCBkYW5zLCBob3Jz4oCdKSwgZGUgbGEgcsOpcMOpdGl0aW9uIGV0IGRlcyBjaGFuZ2VtZW50cyBkZSB0ZW1wcyBvdSBkZSBzdWpldC5cblxuIyBMZSByw6ljaXRcblxuRGUgZ3JhbmRlcyBwYXJ0aWVzIGRlIGwnQW5jaWVuIFRlc3RhbWVudCBldCBkZXMgw4l2YW5naWxlcyBjb25zaXN0ZW50IGVuIGRlcyByw6ljaXRzLiBOb3VzIHBvdXZvbnMgaWRlbnRpZmllciBsZXVyIHN0cnVjdHVyZSBlbiBjb25zaWTDqXJhbnQgY29tbWVudCBjaGFxdWUgc2PDqG5lIGltcGxpcXVlIGRlcyBjaGFuZ2VtZW50cyBkYW5zIGxlcyBwZXJzb25uYWdlcyBwcmluY2lwYXV4LCBsZSB0ZW1wcyBvdSBsZSBsaWV1LCBldCBsZXMgdGjDqG1lcy4gUGFyZm9pcywgbGEgY29tcHLDqWhlbnNpb24gZGUgbCdpbnRyaWd1ZSBkZSBsJ2hpc3RvaXJlIGVzdCDDqWdhbGVtZW50IHV0aWxlIChzaXR1YXRpb24gaW5pdGlhbGUsIMOpbMOpbWVudCBwZXJ0dXJiYXRldXIsIHBvaW50IGN1bG1pbmFudCwgZMOpbm91ZW1lbnQgZXQgc2l0dWF0aW9uIGZpbmFsZSkuIERhbnMgRXhvZGUgMSwgaidhaSBpZGVudGlmacOpIDQgc2PDqG5lcyBkaXN0aW5jdGVzOlxuXG4qIEhpc3RvaXJlIGRlIGxhIGZhbWlsbGUgKHYuMS03KVxuKiBUcmF2YWlsIGZvcmPDqSAodi44LTE0KVxuKiBUcmF2YWlsIHJhcGlkZSAodi4xNS0yMSlcbiogUGhhcmFvbiBmdXJpZXV4ICh2LjIyKVxuXG4jIENvbXByZW5kcmUgbGUgY29udGV4dGVcblxuTCfDqXRhcGUgc3VpdmFudGUgcG91ciBpbnRlcnByw6l0ZXIgY29ycmVjdGVtZW50IHVuIHBhc3NhZ2UgY29uc2lzdGUgw6AgY2hlcmNoZXIgw6AgY29tcHJlbmRyZSBzb24gY29udGV4dGUgbGl0dMOpcmFpcmUsIGhpc3RvcmlxdWUgZXQgYmlibGlxdWUuXG5cbiMjIENvbnRleHRlIGxpdHTDqXJhaXJlXG5cbkNvbW1lbnQgbGVzIHVuaXTDqXMgbGl0dMOpcmFpcmVzIHF1aSBwcsOpY8OoZGVudCBldCBzdWl2ZW50IGltbcOpZGlhdGVtZW50IGxhIG7DtHRyZSDDqWNsYWlyZW50LWVsbGVzIGxlIHNlbnMgZGUgbm90cmUgdGV4dGU/IERhbnMgbGUgY2FzIGQnRXhvZGUgMSwgbCfDqWzDqW1lbnQgbGUgcGx1cyBwZXJ0aW5lbnQgZGUgc29uIGNvbnRleHRlIGxpdHTDqXJhaXJlIGVzdCBsZSBjaGFwaXRyZSBmaW5hbCBkZSBsYSBHZW7DqHNlLiBDZSBsaXZyZSBkZXMgY29tbWVuY2VtZW50cyBzZSB0ZXJtaW5lIHBhciBsYSBtb3J0IGRlIEpvc2VwaC4gQXZhbnQgZGUgcmVuZHJlIHNvbiBkZXJuaWVyIHNvdXBpciwgaWwgZGVtYW5kZSDDoCBzZXMgZnLDqHJlcyBkZSBsdWkgcHJvbWV0dHJlIGQnZW1tZW5lciBzZXMgb3MgYXZlYyBldXggZGFucyBsZSBwYXlzIHF1ZSBEaWV1IGEgcHJvbWlzIMOgIEFicmFoYW0sIElzYWFjIGV0IEphY29iIChHbiA1MC4yNC0yNSkuIEpvc2VwaCBtZXVydCBhdmVjIGxlcyBwcm9tZXNzZXMgZGUgRGlldSBzdXIgbGVzIGzDqHZyZXMuIEwnaW1wb3J0YW5jZSBkZSBjZXR0ZSByw6lhbGl0w6kgYXBwYXJhw650cmEgYXUgZnVyIGV0IMOgIG1lc3VyZSBkZSBsJ8OpdHVkZSBkdSB0ZXh0ZS5cblxuIyMgTGUgY29udGV4dGUgaGlzdG9yaXF1ZVxuXG5Qb3VyIMOpdmFsdWVyIGwnaW5mbHVlbmNlIGR1IGNvbnRleHRlIGhpc3RvcmlxdWUsIG5vdXMgZGV2b25zIHBlbnNlciDDoCBkZXV4IGdyb3VwZXMgZGUgcGVyc29ubmVzIGRpc3RpbmN0ZXM6IGxlcyBhY3RldXJzIGR1IGRyYW1lIHF1aSBzZSBkw6lyb3VsZSBkYW5zIG5vdHJlIHBhc3NhZ2UgZXQgbGVzIGRlc3RpbmF0YWlyZXMgb3JpZ2luYXV4IGR1IGxpdnJlIGRhbnMgc2EgZm9ybWUgYWNoZXbDqWUuIEFpbnNpLCBwb3VyIEV4b2RlLCBsZXMgcGVyc29ubmFnZXMgZGUgbCdoaXN0b2lyZSBkJ0V4b2RlIDEgc29udCBsZSByb2kgZCfDiWd5cHRlICh2LjgpLCBsZSBwZXVwbGUgw6lneXB0aWVuIHF1J2lsIGVucsO0bGUgZGFucyBzb24gc2luaXN0cmUgcGxhbiAodi45LCAyMiksIGxlcyBIw6licmV1eCBxdWkgc2UgbXVsdGlwbGllbnQgZXQgcXVpIHNvdWZmcmVudCBzb3VzIGxhIGJvdHRlIGRlIGwnb3BwcmVzc2lvbiAodi4xMiksIGV0IFNoaXBocmEgZXQgUHVhaCwgbGVzIHNhZ2VzLWZlbW1lcyBow6licmHDr3F1ZXMgcXVpIGNyYWlnbmVudCBEaWV1IHBsdXMgcXVlIFBoYXJhb24gKHYuMjEpLlxuXG5MZSBkZXV4acOobWUgZ3JvdXBlIGRlIHBlcnNvbm5lcyDDoCBjb25zaWTDqXJlciBlc3QgbGUgcHVibGljIGluaXRpYWwgcXVpIGEgcmXDp3UgbGUgbGl2cmUgZGUgbCdFeG9kZS4gTW/Dr3NlIGEgcsOpZGlnw6kgbGVzIMOpdsOpbmVtZW50cyBxdWUgbm91cyBsaXNvbnMgZGFucyBFeG9kZSAxLTQwIGF1IGTDqWJ1dCBkZSBsJ2VycmFuY2UgZGFucyBsZSBkw6lzZXJ0LiBMZXMgZGVzdGluYXRhaXJlcyBkdSBsaXZyZSBzb250IGRvbmMgbGEgcHJlbWnDqHJlIGfDqW7DqXJhdGlvbiBkJ0jDqWJyZXV4IHF1aSBvbnQgY29ubnUgbGEgZMOpbGl2cmFuY2UgZGUgRGlldSBkZSBsJ2VzY2xhdmFnZSBlbiDDiWd5cHRlLlxuXG5BcHLDqHMgYXZvaXIgaWRlbnRpZmnDqSBjZXMgZGV1eCBncm91cGVzLCBub3VzIGRldm9ucyBub3VzIG1ldHRyZSDDoCBsZXVyIHBsYWNlLiBOb3VzIGRldm9ucyBlc3NheWVyIGRlIGNvbXByZW5kcmUgY2UgcXUnaWxzIG9udCB2w6ljdS4gUG91ciBjZWxhLCBjb21tZW7Dp29ucyBlbiBwcmlhbnQgZXQgZW4gZGVtYW5kYW50IMOgIGwnRXNwcml0IGRlIG5vdXMgYWlkZXIgw6AgaW1hZ2luZXIgY2UgcXVlIGxlcyBkaWZmw6lyZW50ZXMgcGFydGllcyB2aXZhaWVudCBkYW5zIGxlIGRyYW1lIHF1aSBzZSBkw6lyb3VsYWl0LiDDgCBxdW9pIHJlc3NlbWJsZSBsYSB2aWUgZGVzIEjDqWJyZXV4IGVuIHRhbnQgcXUnw6l0cmFuZ2VycyBkYW5zIHVuIHBheXMgb8O5IGxlIHJvaSBpbmNpdGUgc29uIHBldXBsZSDDoCBzZSByZXRvdXJuZXIgY29udHJlIGV1eD8gUXVlIHJlc3NlbnRlbnQgbGVzIHNhZ2VzLWZlbW1lcyBsb3JzcXVlIFBoYXJhb24gbGV1ciBvcmRvbm5lIGRlIGNvbW1ldHRyZSBkZXMgaW5mYW50aWNpZGVzPyBCaWVuIHPDu3IsIGNlbGEgbmUgbm91cyBtw6huZSBwYXMgc2kgbG9pbi4gQydlc3QgcG91cnF1b2kgdW4gYm9uIGRpY3Rpb25uYWlyZSBvdSBjb21tZW50YWlyZSBiaWJsaXF1ZSBwZXV0IG5vdXMgcmVuc2VpZ25lciBzdXIgbGEgZ8Opb2dyYXBoaWUsIGxhIHRvcG9ncmFwaGllIGV0IGxlcyBwcmF0aXF1ZXMgY3VsdHVyZWxsZXMgZGUgbCfDqXBvcXVlLlxuXG4jIyBMZSBjb250ZXh0ZSBiaWJsaXF1ZVxuXG5MZSBjb250ZXh0ZSBiaWJsaXF1ZSBmYWl0IHLDqWbDqXJlbmNlIGF1eCDDiWNyaXR1cmVzIHByw6ljw6lkZW50ZXMgcXVlIGwnYXV0ZXVyIGNpdGUgb3Ugw6l2b3F1ZSBwb3VyIMOpdGF5ZXIgc29uIGFyZ3VtZW50YXRpb24uIERhbnMgbGUgY2FzIGQnRXhvZGUgMSwgbm91cyBlbnRlbmRvbnMgZGVzIMOpY2hvcyBkdSBNYW5kYXQgY3VsdHVyZWwgKEduIDEuMjgpIGV0IGRlIGwnQWxsaWFuY2UgYWJyYWhhbWlxdWUgKEduIDEyLjEtMykuIE1vw69zZSBlbXBydW50ZSBsZSB2b2NhYnVsYWlyZSBkZSBjZXMgZGV1eCBwYXNzYWdlcyBjbMOpcyBkZSBzb24gcHJlbWllciBsaXZyZSwgZXQgY2V0dGUgcsOpYWxpdMOpIGluZmx1ZW5jZXJhIGxhIG1hbmnDqHJlIGRvbnQgbm91cyBpbnRlcnByw6l0b25zIG5vdHJlIHRleHRlLlxuXG4jIETDqWNvdXZyaXIgbGUgbGllbiDDoCBsJ8OJdmFuZ2lsZVxuXG5VbmUgZm9pcyBxdWUgbm91cyBhdm9ucyBkw6l0ZXJtaW7DqSBsZSBjb250ZXh0ZSBldCBsYSBzdHJ1Y3R1cmUgZCd1biBwYXNzYWdlLCBub3VzIGRldnJpb25zIMOqdHJlIGVuIG1lc3VyZSBkJ2VuIHNhaXNpciBsZSBzZW5zIHBvdXIgc29uIHB1YmxpYyBkJ29yaWdpbmUuIElsIHMnYWdpdCBkJ3VuZSDDqXRhcGUgZXNzZW50aWVsbGUgZGUgbCdpbnRlcnByw6l0YXRpb24gYmlibGlxdWUsIGNhciBzaSBub3VzIHBhc3NvbnMgZGlyZWN0ZW1lbnQgZGUgbm90cmUgdGV4dGUgw6AgbCfDiXZhbmdpbGUsIG5vdXMgcmlzcXVvbnMgZGUgc3Bpcml0dWFsaXNlciBsZSB0ZXh0ZSBldCBkJ2lnbm9yZXIgbCdvYmplY3RpZiBkZSBsJ2F1dGV1ci4gQXByw6hzIHRvdXQsIGxlIGxpdnJlIGRlIGxhIEJpYmxlIHF1ZSBub3VzIMOpdHVkaW9ucyBhIMOpdMOpIMOpY3JpdCBwb3VyIHVuIGdyb3VwZSBwYXJ0aWN1bGllciBkZSBwZXJzb25uZXMgZGFucyB1biBsaWV1IGfDqW9ncmFwaGlxdWUgc3DDqWNpZmlxdWUsIGV0IG5vdXMgcmVuZG9ucyB1biBtYXV2YWlzIHNlcnZpY2UgYXV4IMOJY3JpdHVyZXMgbG9yc3F1ZSBub3VzIGlnbm9yb25zIGNlbGEgZXQgcXVlIG5vdXMgbm91cyBlbiBzZXJ2b25zIGltbcOpZGlhdGVtZW50IHBvdXIgcGFybGVyIGRlIG5vdXMgYXVqb3VyZCdodWkuIERlIG3Dqm1lLCBzaSBub3VzIG5vdXMgZGlyaWdlb25zIHRvdXQgZHJvaXQgdmVycyBub3VzL21haW50ZW5hbnQgc2FucyBjb25uZWN0ZXIgbm90cmUgdGV4dGUgw6AgbCfDqXZhbmdpbGUsIG5vdXMgcmlzcXVvbnMgZGUgbGUgbW9yYWxpc2VyLiBKZSB2ZXV4IGRpcmUgcGFyIGzDoCBxdWUgbm91cyBkb25ub25zIGxlcyBpbXDDqXJhdGlmcyBtb3JhdXggZHUgcGFzc2FnZSBzYW5zIGxhIHB1aXNzYW5jZSBkZSBsJ0VzcHJpdCBwb3VyIGxlcyBtZXR0cmUgZW4gxZN1dnJlIGVuIHRhbnQgcXVlIHBhcnRpY2lwYW50cyDDoCBsYSBub3V2ZWxsZSBhbGxpYW5jZS5cblxuUGFyIGNvbnPDqXF1ZW50LCBwb3VyIGxlcyBjcm95YW50cyBlbiBKw6lzdXMtQ2hyaXN0LCBsJ8OpdGFwZSBzdWl2YW50ZSBzdXIgbGUgcGFyY291cnMgZGUgbCdpbnRlcnByw6l0YXRpb24gZXN0IGVzc2VudGllbGxlLiBQdWlzcXVlIHRvdXRlIGwnw4ljcml0dXJlIG5vdXMgcmVudm9pZSBhdSBDaHJpc3QgKExjIDI0LjI3LCA0NC00NiksIG5vdXMgZGV2b25zIMOpdGFibGlyIGRlIHF1ZWxsZSBtYW5pw6hyZSBub3RyZSBwYXNzYWdlIG5vdXMgY29uZHVpdCDDoCBsYSBjcm9peCBldCDDoCBsYSByw6lzdXJyZWN0aW9uIGRlIErDqXN1cy4gQ29tbWVudCBFeG9kZSAxIG5vdXMgaW5kaXF1ZS10LWlsIGwnw4l2YW5naWxlPyBFbiBub3VzIG1vbnRyYW50IHF1ZSBsJ2FsbGlhbmNlIGFicmFoYW1pcXVlIHMnZXN0IHBhcnRpZWxsZW1lbnQgYWNjb21wbGllIGRhbnMgbGEgY3JvaXNzYW5jZSBudW3DqXJpcXVlIGRlIGxhIGRlc2NlbmRhbmNlIHBoeXNpcXVlIGRlcyBwYXRyaWFyY2hlcy4gTWFpcyBxdWUgc29uIGFjY29tcGxpc3NlbWVudCB1bHRpbWUgdmllbmRyYSBsb3JzcXVlIERpZXUgcmFjaMOodGVyYSB1bmUgbXVsdGl0dWRlIGlubm9tYnJhYmxlIGRlIHRvdXRlIG5hdGlvbiwgdHJpYnUgZXQgbGFuZ3VlLlxuXG4jIERlcm5pZXIgYXJyw6p0OiBub3VzL8OgIG5vdHJlIMOpcG9xdWVcblxuVW5lIGZvaXMgcXVlIG5vdXMgYXZvbnMgcmVsacOpIG5vdHJlIHRleHRlIMOgIGwnw4l2YW5naWxlIGRlIErDqXN1cy1DaHJpc3QsIG5vdXMgcG91dm9ucyBlbmZpbiBhcnJpdmVyIMOgIG5vdHJlIGRlc3RpbmF0aW9uIGZpbmFsZTogbm91cy/DoCBub3RyZSDDqXBvcXVlLiBQdWlzLWplIHLDqXN1bWVyIGxlIG1lc3NhZ2UgZGUgbW9uIHRleHRlIGVuIHVuZSBzaW1wbGUgcGhyYXNlPyBVbmUgZm9pcyBxdWUgamUgbCdhaSBmYWl0LCBwdWlzLWplIGFwcGxpcXVlciBjZXR0ZSBoaXN0b2lyZSBhbmNpZW5uZSBhdSAyMeG1iSBzacOoY2xlPyBKJ2FpIHLDqXN1bcOpIGxlIG1lc3NhZ2UgZCdFeG9kZSAxIGNvbW1lIHN1aXQ6IFB1aXNxdWUgbGUgRGlldSBkZSBsJ2ltcG9zc2libGUgdGllbnQgc2VzIHByb21lc3NlcywgY3JhaWdub25zLWxlIGV0IG5lIGNyYWlnbm9ucyBwYXMgbGVzIHB1aXNzYW5jZXMgZGUgY2UgbW9uZGUuIEonYWkgdGlyw6kgbGVzIHBvaW50cyBkJ2FwcGxpY2F0aW9uIHN1aXZhbnRzIGQnRXhvZGUgMTogRGlldSBjaG9pc2l0IGQndXRpbGlzZXIgZGVzIHBlcnNvbm5lcyBpbnNpZ25pZmlhbnRlcyBxdWkgbGUgY3JhaWduZW50IGNvbW1lIGxlcyBkZXV4IHNhZ2VzLWZlbW1lcyBow6licmHDr3F1ZXMsIGFsb3JzIGNyYWlnbm9ucyBEaWV1IGV0IGZhaXNvbnMtbHVpIGNvbmZpYW5jZSBwb3VyIHF1J2lsIHNlIHNlcnZlIGRlIG5vdXMgYXVzc2ksIHBvdXIgc2EgZ2xvaXJlLiBEaWV1IGEgw6l0w6kgZmlkw6hsZSBhdXggcHJvbWVzc2VzIGZhaXRlcyDDoCBBYnJhaGFtLCBldCBpbCBjb250aW51ZSDDoCB0ZW5pciBzZXMgcHJvbWVzc2VzIGVudmVycyBub3VzIGF1am91cmQnaHVpLlxuXG4jIENvbmNsdXNpb25cblxuSidhaSBkaXQgYXUgZMOpYnV0IGRlIGNldCBhcnRpY2xlIHF1ZSBsZSBwcm9jZXNzdXMgw6l0YWl0IHNpbXBsZS4gSmUgbidhaSBjZXBlbmRhbnQgcGFzIGRpdCBxdSdpbCDDqXRhaXQgZmFjaWxlLiBJbCBmYXV0IHRyYXZhaWxsZXIgZHVyIGV0IHBlcnPDqXbDqXJlci4gSWwgZmF1dCBjb25zYWNyZXIgZHUgdGVtcHMgZXQgZGUgbCfDqW5lcmdpZSDDoCBsJ8OpdHVkZSBldCDDoCBsYSByw6lmbGV4aW9uLiBJbCBwZXV0IMOpZ2FsZW1lbnQgw6p0cmUgbsOpY2Vzc2FpcmUgZGUgbWV0dHJlIGRlIGPDtHTDqSBkZXMgZm9uZHMgcG91ciBxdWVscXVlcyByZXNzb3VyY2VzIHNvbGlkZXMsIGNvbW1lIHVuIGRpY3Rpb25uYWlyZSBiaWJsaXF1ZSBldC9vdSB1biBjb21tZW50YWlyZS4gQ2VwZW5kYW50LCBsZSByZXRvdXIgc3VyIGludmVzdGlzc2VtZW50IHByb21ldCBkZSBwcm9kdWlyZSBkZXMgZGl2aWRlbmRlcyDDqXRlcm5lbHMuIEVuIGVmZmV0LCBjaGFxdWUgcGFzc2FnZSBxdWUgdm91cyDDqXR1ZGllcmV6IGRlIGNldHRlIG1hbmnDqHJlIHZvdXMgcGVybWV0dHJhIGRlIGTDqWNvdXZyaXIgZGVzIHBpZXJyZXMgcHLDqWNpZXVzZXMgY2FjaMOpZXMgZGFucyBsZSBzb2wgZGUgbGEgUGFyb2xlIGRlIERpZXUuIFF1ZWxsZSBtZWlsbGV1cmUgZmHDp29uIGQnaW52ZXN0aXIgZGFucyBsJ8OpdGVybml0w6khXG5cbmA8c3ViPjEuIE5vdXMgc29tbWVzIHJlZGV2YWJsZXMgYXUgPGEgaHJlZj1cImh0dHBzOi8vc2ltZW9udHJ1c3Qub3JnL2ZyL2NvdXJzLW9ubGluZS9cIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+PGVtPkNoYXJsZXMgU2ltZW9uIFRydXN0PC9lbT48L2E+IHBvdXIgbGV1cnMgY291cnMgZXQgZm9ybWF0aW9ucyBxdWkgZW5zZWlnbmVudCBjZXMgcHJpbmNpcGVzLCBhaW5zaSBxdWUgcG91ciBsYSBwZXJtaXNzaW9uIGRlIHBhcnRhZ2VyIGNlbGEgYXZlYyB2b3VzLjxicj5VbiBncmFuZCBtZXJjaSDDoCBNYXJpZS1Bbm5lIFBhcGlsbG9uIHBvdXIgbGEgY3LDqWF0aW9uIGRlIGNldHRlIGltYWdlLjwvc3ViPmAiLCJhdXRob3IiOnsiZnVsbE5hbWUiOiJBbmdpZSBWZWxhc3F1ZXogVGhvcm50b24iLCJwaWN0dXJlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvSU1HXzI3MjVfNGE3ZGZkMDg3My5qcGVnIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifX0sImltYWdlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvYWxleGFuZHJhX2Z1bGxlcl93a2d2N19JMl9WX1R6X01fdW5zcGxhc2hfYTViYTZlNWZlYS5qcGciLCJoZWlnaHQiOjM0MTEsIndpZHRoIjo1MTE2LCJhbHRlcm5hdGl2ZVRleHQiOiIiLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9LCJ0b3BpY3MiOlt7Im5hbWUiOiJIZXJtw6luZXV0aXF1ZSJ9LHsibmFtZSI6IlRow6lvbG9naWUgQmlibGlxdWUifSx7Im5hbWUiOiJQcsOpZGljYXRpb24gZXQgZW5zZWlnbmVtZW50In1dLCJtb2R1bGVzIjpbeyJfX3R5cGVuYW1lIjoiQ29tcG9uZW50TW9kdWxlTGVhZCIsImNvbnRlbnQiOiJKJ2FpbWUgZW5zZWlnbmVyIGF1eCBmZW1tZXMgY29tbWVudCDDqXR1ZGllciBsYSBCaWJsZS4gTGV1ciBkb25uZXIgbGVzIG91dGlscyBwb3VyIGTDqWNvdXZyaXIgbGVzIHRyw6lzb3JzIGRlIGxhIFBhcm9sZSBkZSBEaWV1IHBvdXIgZWxsZXMtbcOqbWVzIG1lIHByb2N1cmUgdW5lIGdyYW5kZSBqb2llLiBKJ2FpIGxlIHByaXZpbMOoZ2UgZGUgZmFpcmUganVzdGVtZW50IMOnYSBlbiB0YW50IHF1ZSByZXNwb25zYWJsZSBkZSBtaW5pc3TDqHJlIGbDqW1pbmluIGNoZXogU09MQS4ifSx7fV19LHsiaWQiOiIxMjMiLCJ0aXRsZSI6IlBvdXJxdW9pIHZvdXMgZGV2cmlleiBsaXJlIFwiUG91cnF1b2kgRGlldSBzZSBtw6psZXJhaXQtaWwgZGUgbWEgdmllIHNleHVlbGxlP1wiIiwic2x1ZyI6InBvdXJxdW9pLWxpcmUtc2FtLWFsbGJlcnJ5IiwidHlwZSI6ImFydGljbGUiLCJwdWJsaXNoZWRfYXQiOiIyMDI0LTA2LTA2VDAzOjQ1OjAwLjAwMFoiLCJib2R5IjoiYDxhIGhyZWY9XCJodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9wb3VycXVvaS1kaWV1LXNlLW1lbGVyYWl0LWlsLWRlLW1hLXZpZS1zZXh1ZWxsZVwiIHRhcmdldD1cIl9ibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+PGltZyBzcmM9XCJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L0xpdnJlX0FsbGJlcnJ5XzU0MDE5MDlhZGIucG5nXCIgdnNwYWNlPVwiMTVcIiBoc3BhY2U9XCIyNVwiIGFsaWduPVwicmlnaHRcIiB3aWR0aD01MCUgaGVpZ2h0PTEwJT48L2E+YFxuXG5gPHA+Q2UgbGl2cmUgZmFpdCBwYXJ0aWUgZOKAmXVuZSBzw6lyaWUgaW50aXR1bMOpZSBcIlF1ZXN0aW9uaW5nIEZhaXRoXCIsIGRhbnMgbGFxdWVsbGUgZGVzIHNww6ljaWFsaXN0ZXMgY2hyw6l0aWVucyByw6lwb25kZW50IGF1eCBxdWVzdGlvbnMgY291cmFudGVzIGRlcyBzY2VwdGlxdWVzIGF2ZWMgbGEgdsOpcml0w6kgZGUgbOKAmcOJdmFuZ2lsZS4gQ+KAmWVzdCBkb25jIGF2ZWMgcGxhaXNpciBxdWUgauKAmWFpIGx1IDxhIGhyZWY9XCJodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9wb3VycXVvaS1kaWV1LXNlLW1lbGVyYWl0LWlsLWRlLW1hLXZpZS1zZXh1ZWxsZVwiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj48ZW0+UG91cnF1b2kgRGlldSBzZSBtw6psZXJhaXQtaWwgZGUgbWEgdmllIHNleHVlbGxlPzwvZW0+PC9hPiBkZSBTYW0gQWxiZXJyeS48L3A+PGRpdiBzdHlsZT1cImNsZWFyOmJvdGhcIj48L2Rpdj5gXG5cbiMjIExhIHLDqXBvbnNlIGVzdCBzaW1wbGU6IERpZXUgc2Ugc291Y2llIGRlIG5vdXNcblxuQWxsYmVycnkgcG9zc8OoZGUgdW4gc3R5bGUgZOKAmcOpY3JpdHVyZSBjaGFsZXVyZXV4LCB2w6lyaWRpcXVlIGV0IGNvbXBhdGlzc2FudC4gRMOocyBsZXMgcHJlbWnDqHJlcyBwYWdlcywgaWwgcmVjb25uYcOudCBxdWUgY2UgcXXigJlpbCBz4oCZYXBwcsOqdGUgw6AgZXhwb3NlciDDoCBzZXMgbGVjdGV1cnMgY29uY2VybmFudCBs4oCZw6l0aGlxdWUgc2V4dWVsbGUgYmlibGlxdWUgcG91cnJhaXQgbGV1ciBkb25uZXIgZW52aWUgZGUgamV0ZXIgc29uIGxpdnJlIHBhciBsYSBmZW7DqnRyZSAocC4xMykuIFBvdXJ0YW50LCBpbCBwYXJsZSBmcmFuY2hlbWVudCBkZSBzZXMgcHJvcHJlcyBkaWZmaWN1bHTDqXM6IGlsIGVzdCB1biBob21tZSBjw6lsaWJhdGFpcmUgcXVpLCBkZSBzb24gcHJvcHJlIGF2ZXUsIG5lIHNlIG1hcmllcmEgcHJvYmFibGVtZW50IGphbWFpcy4gRXQgaWwgc+KAmWVzdCBlbmdhZ8OpIMOgIHZpdnJlIGRhbnMgbGUgY8OpbGliYXQuXG5cblBvdXJ0YW50LCBsYSByw6lwb25zZSBzb3VsZXbDqWUgZGFucyBsZSB0aXRyZSBkdSBsaXZyZSBlc3Qgc2ltcGxlOiBEaWV1IHNlIG3DqmxlIGRlIG5vdHJlIHZpZSBzZXh1ZWxsZSBwYXJjZSBxdeKAmWlsIHNlIHNvdWNpZSBkZSBub3VzLCBldCBwYXJjZSBxdWUgbWFsIHZpdnJlIG5vdHJlIHNleHVhbGl0w6kgcGV1dCBlbmdlbmRyZXIgYmVhdWNvdXAgZGUgZMOpZ8OidHMgZXQgZGVzIGJsZXNzdXJlcyBwcm9mb25kZXMgKHAuMTUpLiBEYW5zIGxlcyBjaGFwaXRyZXMgcXVpIHN1aXZlbnQsIGlsIGTDqXZlbG9wcGUgbG9uZ3VlbWVudCBjZXR0ZSBpZMOpZS5cblxuIyMgQ29uc3RydWlyZSB1biB0ZXJyYWluIGTigJllbnRlbnRlXG5cbkRhbnMgbGVzIGNoYXBpdHJlcyAxIGV0IDIsIEFsbGJlcnJ5IGNyw6llIHVuIGxpZW4gYXZlYyBzZXMgbGVjdGV1cnMgZW4gw6l0YWJsaXNzYW50IHVuIHRlcnJhaW4gZOKAmWVudGVudGUuIEVuIG1hdGnDqHJlIGRlIHNleHVhbGl0w6kgaHVtYWluZSwgcXVlbCBlc3QgbOKAmcOpbMOpbWVudCBzdXIgbGVxdWVsIHRvdXQgw6p0cmUgaHVtYWluIGTDqWNlbnQgc2VyYWl0IGTigJlhY2NvcmQ/IFF1ZSBsZSBjb25zZW50ZW1lbnQgbXV0dWVsIGVzdCBlc3NlbnRpZWwuIExlcyBtb3V2ZW1lbnRzICNNZVRvbyBldCA8YSBocmVmPVwiL2FydGljbGUvYWJ1cy1zZXh1ZWxzLWV4aXN0ZW50LWF1c3NpLWRhbnMtbm9zLWVnbGlzZXMvXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPiNDaHVyY2hUb288L2E+IHNvbnQgbsOpcyBkZSBjcmlzIGzDqWdpdGltZXMgY29udHJlIGxlcyBhYnVzIGV0IGxldXJzIGRpc3NpbXVsYXRpb25zIGRhbnMgbGEgc29jacOpdMOpIGVuIGfDqW7DqXJhbCBldCBkYW5zIGzigJnDiWdsaXNlLlxuXG5BbGxiZXJyeSBzb3V0aWVudCBxdWUgbm91cyBzZXJ2b25zIHVuIFNhdXZldXIgcXVpIGEgbHVpLW3Dqm1lIHNvdWZmZXJ0IGTigJl1bmUgw6lub3JtZSBpbmp1c3RpY2UgZXQgcXVpIGEgw6AgbGEgZm9pcyB2w6ljdSBldCBlbnNlaWduw6kgdW5lIHByb2ZvbmRlIGNvbXBhc3Npb24gZW52ZXJzIGxlcyBzb3VmZnJhbmNlcyBkZXMgYXV0cmVzLiBUb3V0IGFjdGUgZOKAmTxhIGhyZWY9XCIvYXJ0aWNsZS9hYnVzLXNleHVlbC1jb21tZW50LXNvcnRpci1zaWxlbmNlL1wiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj5hZ3Jlc3Npb24gc2V4dWVsbGU8L2E+IGNvbnRyZSB1biBwb3J0ZXVyIGRlIHNvbiBpbWFnZSBlc3QgYWxvcnMgdW5lIHZpb2xhdGlvbiBk4oCZdW4gbGlldSBzYWNyw6kgKHAuMjcpLlxuXG4jIyBM4oCZdW5pb24gZOKAmXVuZSBzZXVsZSBjaGFpclxuXG5BcHLDqHMgYXZvaXIgw6l0YWJsaSBxdWUgbm91cyBu4oCZYXZvbnMgcGFzIGxlIGRyb2l0LCBlbiBmYWl0LCBkZSBkb25uZXIgbGlicmUgY291cnMgw6AgdG91dGVzIG5vcyBwdWxzaW9ucyBzZXh1ZWxsZXMsIEFsbGJlcnJ5IGV4cGxpcXVlIGF1IGNoYXBpdHJlIDMgcXVlbCBlc3QgbGUgYnV0IGRlIGxhIHNleHVhbGl0w6kuIExvcnNxdeKAmWlsIHPigJlhZ2l0IGRlIGzigJlhY3RlIHNleHVlbCwgbm90cmUgY3VsdHVyZSBjb25zaWTDqHJlIHF1ZSBzb24gYnV0IGVzdCBhdmFudCB0b3V0IGxlIHBsYWlzaXIuIFBvdXJ0YW50LCBs4oCZYXV0ZXVyIHNvdXRpZW50IHF1ZSBsZSBzZXhlIMOpdGFpdCBs4oCZaWTDqWUgZGUgRGlldSBldCBxdWUgbOKAmWFjdGUgc2V4dWVsIMOpdGFpdCBpbXBsaWNpdGUgZGFucyBzb24gcHJlbWllciBjb21tYW5kZW1lbnQgYXUgcHJlbWllciBob21tZSBldCDDoCBsYSBwcmVtacOocmUgZmVtbWUgKEduIDEuMjgpIChwLiA0OCkuXG5cbklsIG5lIHLDqWR1aXQgY2VwZW5kYW50IHBhcyBsZSBidXQgZHUgc2V4ZSDDoCBsYSBwcm9jcsOpYXRpb24uIElsIGTDqXZlbG9wcGUgcGx1dMO0dCBs4oCZaWTDqWUgcXVlIGzigJlpbnRpbWl0w6kgZGUgbOKAmXVuaW9uIGTigJl1bmUgc2V1bGUgY2hhaXIgZXN0IHVuZSBpbWFnZSBkZSBsYSByw6l1bmlmaWNhdGlvbiBkZSBsYSBjaGFpciBxdWUgRGlldSBhdmFpdCBzw6lwYXLDqWUgZW4gY3LDqWFudCDDiHZlIMOgIHBhcnRpciBkZSBsYSBjw7R0ZSBk4oCZQWRhbSAocC41MSkuXG5cbiMjIExhIHByZW1pw6hyZSByw6l2b2x1dGlvbiBzZXh1ZWxsZVxuXG5EYW5zIGxlcyBjaGFwaXRyZXMgNCBldCA1LCBBbGxiZXJyeSBleHBsaXF1ZSBsb25ndWVtZW50IHF1ZSBsYSBCaWJsZSBlbnNlaWduZSBlZmZlY3RpdmVtZW50IHF1ZSBsYSBzZXh1YWxpdMOpIGVzdCByw6lzZXJ2w6llIGF1IG1hcmlhZ2UsIGNlIHF1aSDDqXRhaXQgYXVzc2kgcsOpdm9sdXRpb25uYWlyZSDDoCBs4oCZw6lwb3F1ZSBkdSBOb3V2ZWF1IFRlc3RhbWVudCBxdeKAmWF1am91cmTigJlodWkuIERhbnMgbOKAmUVtcGlyZSByb21haW4sIGxlcyBmZW1tZXMgbuKAmcOpdGFpZW50IGd1w6hyZSBwbHVzIHF1ZSBkZSBsYSBtYXJjaGFuZGlzZS4gTGVzIGZlbW1lcyByaWNoZXMgw6l0YWllbnQgZGVzIGNvbW1vZGl0w6lzIHByw6ljaWV1c2VzLCBldCBsZXMgZmVtbWVzIHBhdXZyZXMgw6l0YWllbnQgdmVuZHVlcyBhdSBwbHVzIG9mZnJhbnQsIG1haXMgZWxsZXMgcG91dmFpZW50IHRvdXRlcyDDqnRyZSBleHBsb2l0w6llcyBzYW5zIHNjcnVwdWxlcy5cblxuQ+KAmWVzdCBwb3VycXVvaSBs4oCZw6l0aGlxdWUgc2V4dWVsbGUgY2hyw6l0aWVubmUgZXN0IHF1YWxpZmnDqWUgcGFyIHVuIHNww6ljaWFsaXN0ZSBkZSBwcmVtacOocmUgcsOpdm9sdXRpb24gc2V4dWVsbGUgKHAuIDczKS4gTWFpcyBpbCBuZSBz4oCZYWdpdCBwYXMgc2V1bGVtZW50IGRlIG1vbm9nYW1pZS4gTOKAmUFuY2llbiBldCBsZSBOb3V2ZWF1IFRlc3RhbWVudCBicm9zc2VudCB1biB0YWJsZWF1IHNhbnMgcHLDqWPDqWRlbnQgZGUgbGEgcsOpY2lwcm9jaXTDqSBldCBkZSBsYSBzb3VtaXNzaW9uIG11dHVlbGxlIGRhbnMgbGUgbWFyaWFnZS4gUGVuc2V6IGF1IENhbnRpcXVlIGRlcyBDYW50aXF1ZXMgNi4zLCBwYXIgZXhlbXBsZTogKuKAnEplIHN1aXMgw6AgbW9uIGJpZW4tYWltw6ksIGV0IGlsIGVzdCDDoCBtb2ku4oCdKiBPdSDDoCAxIENvcmludGhpZW5zIDcuMi00LCBkYW5zIGxlcXVlbCBQYXVsIGTDqW1vbnRyZSBxdWUgbm9uIHNldWxlbWVudCBsZSBjb3JwcyBkZSBsYSBmZW1tZSBhcHBhcnRpZW50IMOgIHNvbiBtYXJpLCBtYWlzIGF1c3NpIHF1ZSBsZSBjb3JwcyBkdSBtYXJpIGFwcGFydGllbnQgw6Agc2EgZmVtbWUuIENlIGZhaXNhbnQsIGlsIGxlcyBwbGFjZSBzdXIgdW4gcGllZCBk4oCZw6lnYWxpdMOpIGzigJl1biBwYXIgcmFwcG9ydCDDoCBs4oCZYXV0cmUsIGNlIHF1aSDDqXRhaXQgaW7DqWRpdCBhdXBhcmF2YW50IChwLiA3NikuXG5cbiMjIFRvdXQgY29tbWVuY2UgZGFucyBsZSBjxZN1clxuXG5BdSBjaGFwaXRyZSA2LCBs4oCZYXV0ZXVyIGV4cGxpcXVlIGNvbW1lbnQgbOKAmcOpdGhpcXVlIHNleHVlbGxlIGJpYmxpcXVlIHJlbWV0IGVuIHF1ZXN0aW9uIG5vbWJyZSBkZSBub3Mgbm9ybWVzIHNleHVlbGxlcyBhY3R1ZWxsZXMuIEV0IGNlIGTigJlhdXRhbnQgcGx1cyBxdWUgbOKAmcOJY3JpdHVyZSByYW3DqG5lIGxhIHF1ZXN0aW9uIGF1IGPFk3VyLCBxdeKAmWlsIHPigJlhZ2lzc2UgZHUgZGl4acOobWUgY29tbWFuZGVtZW50IGludGVyZGlzYW50IGxhIGNvbnZvaXRpc2UgKEV4IDIwLjE3KSBvdSBkdSBmYWl0IHF1ZSBKw6lzdXMgYXNzaW1pbGUgbGEgY29udm9pdGlzZSDDoCBs4oCZYWR1bHTDqHJlLiBFbiB1dGlsaXNhbnQgbGUgcsOpY2l0IGRlIERhdmlkIGV0IEJldGhzYWLDqWUgY29tbWUgw6l0dWRlIGRlIGNhcywgQWxsYmVycnkgZMOpbW9udHJlIHF1ZSBjZSBxdWkgYSBjb21tZW5jw6kgcGFyIHVuIHJlZ2FyZCBkZSBjb25jdXBpc2NlbmNlIGRlIGxhIHBhcnQgZHUgcm9pIHNlIHRlcm1pbmUgcGFyIGxhIHBlcnRlIGRlIHRvdXQgcG91ciBCZXRoc2Fiw6llOiBzb24gaW50w6lncml0w6kgc2V4dWVsbGUsIHNvbiBtYXJpLCBsYSB2aWUgcXXigJllbGxlIG1lbmFpdCBldCwgauKAmWFqb3V0ZXJhaSwgbOKAmWVuZmFudCBxdWkgYSByw6lzdWx0w6kgZGUgbGV1ciB1bmlvbiBub24gY29uc2Vuc3VlbGxlIChwLiA4NCkuXG5cbkxhIGNvbnZvaXRpc2UsIGFmZmlybWUtdC1pbCwgZMOpc2h1bWFuaXNlIMOgIGxhIGZvaXMgbOKAmW9iamV0IGRlIHNvbiByZWdhcmQgZXQgbGUgY29uc29tbWF0ZXVyIHF1aSBzZSBkw6lzZW5zaWJpbGlzZSDDoCBsYSBkw6l0cmVzc2UgZGUgbGEgcGVyc29ubmUgcXXigJlpbCBhIGRldmFudCBsdWkgKHAuODcpLiBFbGxlIGVzdCBsZSBtb3RldXIgZGUgbOKAmWluZHVzdHJpZSBwb3Jub2dyYXBoaXF1ZSBldCBkZSBsYSB0cmFpdGUgZGVzIMOqdHJlcyBodW1haW5zIHF1aSBsYSBzb3VzLXRlbmQuXG5cbiMjIEF1LWRlbMOgIGRlIGxhIG1vZGlmaWNhdGlvbiBkdSBjb21wb3J0ZW1lbnRcblxuQXByw6hzIGF2b2lyIGV4cG9zw6kgZGUgZHVyZXMgdsOpcml0w6lzIHN1ciBub3MgY8WTdXJzIGNvcnJvbXB1cyBldCBub3RyZSBjb21wbGljaXTDqSBkYW5zIGzigJlleHBsb2l0YXRpb24gaHVtYWluZSwgbGUgcGFzdGV1ciBBbGxiZXJyeSBvZmZyZSwgYXUgY2hhcGl0cmUgNywgZGVzIG1vdHMgZOKAmWVzcG9pciDDoCBjZXV4IHF1aSBvbnQgbOKAmWltcHJlc3Npb24gZOKAmWF2b2lyIGTDqWrDoCB0b3V0IGfDomNow6kuIExhIEJpYmxlIG7igJllbnNlaWduZSBwYXMgdW5lIMOpdGhpcXVlIHNleHVlbGxlIGRlIG1vZGlmaWNhdGlvbiBkdSBjb21wb3J0ZW1lbnQuIEF1IGNvbnRyYWlyZSwgbGEgYm9ubmUgbm91dmVsbGUgZGUgbOKAmcOJdmFuZ2lsZSBlc3QgcXVlIGxvcnNxdWUgbm91cyBmYWlzb25zIGNvbmZpYW5jZSDDoCBDaHJpc3QsIG5vdXMgc29tbWVzIHRyYW5zZm9ybcOpcyBkZSBs4oCZaW50w6lyaWV1ci4gTOKAmUVzcHJpdCBkZSBEaWV1IG5vdXMgZG9ubmUgZGUgbm91dmVhdXggYXBww6l0aXRzIChwLjEwMSksIGV0IGxlIFNhdXZldXIgcXVpIHJlbmQgY2V0dGUgxZN1dnJlIHBvc3NpYmxlIGVzdCBiaWVuIHBsdXMgdGVuZHJlIHF1ZSBjZSBxdWUgbm91cyBwb3VycmlvbnMgaW1hZ2luZXIgKHAuOTkpLlxuXG4jIyBOb3RyZSB2w6lyaXRhYmxlIGlkZW50aXTDqTogY29ubnVzIGV0IGFpbcOpc1xuXG5FbnN1aXRlLCBhdSBjaGFwaXRyZSA4LCBpbCByw6lwb25kIMOgIGxhIHF1ZXN0aW9uIGRlIHNhdm9pciBzaSBub3RyZSBpZGVudGl0w6kgZMOpcGVuZCBkZSBub3RyZSDDqXBhbm91aXNzZW1lbnQgc2V4dWVsLiBKw6lzdXMgZXQgbGEgZmVtbWUgc2FtYXJpdGFpbmUgY29uc3RpdHVlbnQgdW5lIGF1dHJlIMOpdHVkZSBkZSBjYXMuIErDqXN1cyBkw6ljbGFyZSDDqnRyZSBs4oCZZWF1IHZpdmUsIHVuIGRvbiByZW5kdSBwb3NzaWJsZSBwYXJjZSBxdWUgQ2hyaXN0IGEgcG9ydMOpIG5vdHJlIHNvaWYsIG5vcyB0w6luw6hicmVzLCBub3RyZSBjb25kYW1uYXRpb24gc3VyIGxhIGNyb2l4IGVuIGRldmVuYW50IG5vdHJlIHN1YnN0aXR1dCAocC4xMTgpLiBDZXQgw6ljaGFuZ2UgZ2FyYW50aXQgcXXigJllbiBsdWkgbm91cyBzb21tZXMgw6AgbGEgZm9pcyBwcm9mb25kw6ltZW50IGNvbm51cyBldCBwcm9mb25kw6ltZW50IGFpbcOpcy4gRXQgY+KAmWVzdCBsw6AgcXVlIHLDqXNpZGUgbm90cmUgdsOpcml0YWJsZSBpZGVudGl0w6kuXG5cbiMjIERlcyBjb25zZWlscyBk4oCZZXhwZXJ0IGVuIGFtb3VyIGRlIGxhIHBhcnQgZHUgRGlldSBxdWkgZXN0IEFtb3VyXG5cbkF1IGNoYXBpdHJlIDksIG5vdXMgYXJyaXZvbnMgw6AgdW5lIHF1ZXN0aW9uIHF1aSBlc3QgYXUgY8WTdXIgZGUgbGEgY29tcHLDqWhlbnNpb24gZGUgbGEgc2V4dWFsaXTDqSBkYW5zIG5vdHJlIGN1bHR1cmU6IGVzdC1jZSBxdeKAmWlsIG5lIHN1ZmZpdCBwYXMgZOKAmWFpbWVyPyBBbGxiZXJyeSByw6lwb25kIMOgIGNldHRlIHF1ZXN0aW9uIGVuIHNlIHBsb25nZWFudCBkYW5zIGxlIHBhc3NhZ2UgcXVpbnRlc3NlbnRpZWwgZGUgbGEgQmlibGUgc3VyIGzigJlhbW91ciwgMSBDb3JpbnRoaWVucyAxMy4gSWwgc291dGllbnQgcXVlIGzigJlvYmplY3RpZiBkZSBQYXVsIGRhbnMgY2UgdGV4dGUgZXN0IGRlIG5vdXMgbW9udHJlciDDoCBxdWVsIHBvaW50IG5vdXMgbmUgc29tbWVzIHBhcyDDoCBsYSBoYXV0ZXVyIChwLjEzMSkgZXQgw6AgcXVlbCBwb2ludCBub3VzIGF2b25zIGJlc29pbiBkZSBs4oCZYWlkZSBkZSBEaWV1IHBvdXIgYmllbiBhaW1lciBsZXMgYXV0cmVzLiBM4oCZYW1vdXIgZXN0IGxlIGRvbWFpbmUgZOKAmWV4cGVydGlzZSBkZSBEaWV1LCBwdWlzcXXigJlpbCBlc3QgYW1vdXIgKHAuMTM0KS5cblxuw4AgY2UgdGl0cmUsIGlsIG5vdXMgYXBwcmVuZCDDoCBhaW1lciBkaWZmw6lyZW1tZW50IHNlbG9uIGxlcyBjb250ZXh0ZXMsIHF14oCZaWwgc+KAmWFnaXNzZSBkZSBub3MgYW5pbWF1eCBkZSBjb21wYWduaWUsIGRlIG5vcyBwYXJlbnRzLCBkZSBub3MgcGFydGVuYWlyZXMsIGRlIG5vdHJlIHDDonRpc3NlcmllIG91IGRlIG5vdHJlIHNhdWNpc3NlIHByw6lmw6lyw6llLiBFdCBwb3VyIGNhbmFsaXNlciBub3MgYWZmZWN0aW9ucywgaWwgZmF1dCBjb21wcmVuZHJlIHF1ZSBs4oCZYW1vdXIgbm91cyBhcHBlbGxlIMOgIHJlc3BlY3RlciB1biB0aW1pbmcgcHLDqWNpcyAocC4xNDEpLCBkZSBwZXVyIFwiZOKAmcOpdmVpbGxlciBs4oCZYW1vdXIgYXZhbnQgcXXigJlpbCBuZSBsZSB2ZXVpbGxlXCIgKEN0IDIuNikuXG5cbiMjIEzigJlhbW91ciBhdSBwcsOpc2VudDogdW5lIGltYWdlIGV0IHVuIGF2YW50LWdvw7t0IGRlIGzigJlhbW91ciBkYW5zIGzigJnDqXRlcm5pdMOpXG5cbkRhbnMgc2VzIGRldXggZGVybmllcnMgY2hhcGl0cmVzLCBBbGxiZXJyeSByZXBsYWNlIGxhIHF1ZXN0aW9uIGluaXRpYWxlIGR1IGxpdnJlIGV0IHNhIHLDqXBvbnNlIGRhbnMgbGUgY29udGV4dGUgZGUgbGEgZ3JhbmRlIGhpc3RvaXJlIGRlIGxhIEJpYmxlLiBM4oCZaGlzdG9pcmUgZGUgbOKAmXVuaXZlcnMgZXN0IHVuZSBoaXN0b2lyZSBk4oCZYW1vdXIgKHAuMTQ5KS4gRXQgYXUgY2VudHJlIGRlIGNldHRlIGhpc3RvaXJlIGTigJlhbW91ciBzZSB0cm91dmUgbGUgbWFyaWFnZSB0YW50IGF0dGVuZHUgZGUgbOKAmcOJcG91eCBhdmVjIHNvbiDDiXBvdXNlLlxuXG5MZSBtYXJpYWdlIHRlcnJlc3RyZSBzZXJ0IGRvbmMgZOKAmWltYWdlIGV0IGTigJlhdmFudC1nb8O7dCBkZSBub3RyZSB1bmlvbiBhdmVjIENocmlzdCBxdWkgYSBjb21tZW5jw6kgZXQgc2VyYSBhY2hldsOpZSBkYW5zIGV0IHBvdXIgbOKAmcOpdGVybml0w6kgKHAuIDE2MSkuIEV0IGPigJllc3QgcG91cnF1b2kgbm91cyBzb21tZXMgdm91w6lzIMOgIMOqdHJlIGTDqcOndXMgbG9yc3F1ZSBub3VzIGNoZXJjaG9ucyBsYSBzYXRpc2ZhY3Rpb24gdWx0aW1lIGRhbnMgdW5lIHJlbGF0aW9uIGh1bWFpbmUgYWxvcnMgcXVlIG5vdXMgYXZvbnMgw6l0w6kgY3LDqcOpcyBwb3VyIHVuZSByZWxhdGlvbiBjw6lsZXN0ZS5cblxuPiBTaSBub3VzIGNvbXByZW5vbnMgcXVlIG5vdHJlIG9ic2Vzc2lvbiBwb3VyIGxlcyByZWxhdGlvbnMgYW1vdXJldXNlcyBlc3QgZW4gcsOpYWxpdMOpIGzigJllbXByZWludGUgbGFpc3PDqWUgcGFyIHVuZSBoaXN0b2lyZSBwbHVzIGdyYW5kZSDigJNs4oCZw6ljaG8gZOKAmXVuZSBtw6lsb2RpZSBwbHVzIGhhcm1vbmlldXNlLCBsZSBwYW5uZWF1IGluZGljYXRldXIgdmVycyBs4oCZdWx0aW1lIGRlc3RpbmF0aW9u4oCTIGFsb3JzIG5vdXMgc2Vyb25zIGVuIG1lc3VyZSBkZSB2aXZyZSBjZXR0ZSByw6lhbGl0w6kgcXVpIHN1cnBhc3NlIGRlIGxvaW4gbGVzIHJlbGF0aW9ucyB0ZXJyZXN0cmVzIGxlcyBwbHVzIGludGltZXMuXG4+XG4+IDxzdWI+PGNpdGU+cC4gMTYzPC9jaXRlPjwvc3ViPlxuXG4jIyBDb25jbHVzaW9uXG5cbmA8cD5BbG9ycywgcG91cnF1b2kgZGV2cmllei12b3VzIGxpcmUgPGEgaHJlZj1cImh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzL3BvdXJxdW9pLWRpZXUtc2UtbWVsZXJhaXQtaWwtZGUtbWEtdmllLXNleHVlbGxlXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPjxlbT5Qb3VycXVvaSBEaWV1IHNlIG3DqmxlcmFpdC1pbCBkZSBtYSB2aWUgc2V4dWVsbGU/PC9lbT48L2E+ICBQYXJjZSBxdeKAmWlsIGZvdXJuaXQgdW5lIHLDqXBvbnNlIHByb2ZvbmTDqW1lbnQgc2Vuc2libGUsIHNhZ2UgZXQgYmlibGlxdWUgw6AgbOKAmXVuZSBkZXMgcXVlc3Rpb25zIGxlcyBwbHVzIGltcG9ydGFudGVzIGRlIG5vdHJlIGfDqW7DqXJhdGlvbi4gUXVlIGxlIFNlaWduZXVyIHNlIHNlcnZlIGRlIGNlIGxpdnJlIHBvdXIgw6lxdWlwZXIgbm90cmUgZ8OpbsOpcmF0aW9uIGRlIHNhIHNhZ2Vzc2UgYWZpbiBxdeKAmWVsbGUgY29tcHJlbm5lIGV0IGFkb3B0ZSB1bmUgdmlzaW9uIGJpYmxpcXVlIGRlIGxhIHNleHVhbGl0w6kgaHVtYWluZS4gKDxhIGhyZWY9XCJodHRwczovL3B1YmxpY2F0aW9uc2NocmV0aWVubmVzLmNvbS9wcm9kdWN0cy9wb3VycXVvaS1kaWV1LXNlLW1lbGVyYWl0LWlsLWRlLW1hLXZpZS1zZXh1ZWxsZVwiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj5EaXNwb25pYmxlIGljaTwvYT4gc2kgdm91cyBoYWJpdGV6IGF1IFF1w6liZWMpLjwvcD5gIiwiYXV0aG9yIjp7ImZ1bGxOYW1lIjoiQW5naWUgVmVsYXNxdWV6IFRob3JudG9uIiwicGljdHVyZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L0lNR18yNzI1XzRhN2RmZDA4NzMuanBlZyIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn19LCJpbWFnZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L2FsZXhhbmRyYV9nb3JuX3NtdV9TX2pfVV9aYTlfSV91bnNwbGFzaF9lMTY2NDE5NzQ3OTY4MV9mNWM3N2FjNmEyLmpwZyIsImhlaWdodCI6ODcxLCJ3aWR0aCI6MTEwMCwiYWx0ZXJuYXRpdmVUZXh0IjpudWxsLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9LCJ0b3BpY3MiOlt7Im5hbWUiOiJWaWUgY2hyw6l0aWVubmUifSx7Im5hbWUiOiJTZXh1YWxpdMOpIn0seyJuYW1lIjoiUmVjZW5zaW9uIGRlIGxpdnJlIn1dLCJtb2R1bGVzIjpbeyJfX3R5cGVuYW1lIjoiQ29tcG9uZW50TW9kdWxlTGVhZCIsImNvbnRlbnQiOiJTaSB2b3VzIG7igJnDqnRlcyBwYXMgZW5jb3JlIGZhbWlsaWVyIGF2ZWMgU2FtIEFsbGJlcnJ5LCBpbCBlc3QgcHJvYmFibGVtZW50IGxlIHBsdXMgY29ubnUgcG91ciBzb24gbGl2cmUgKltEaWV1IGVzdC1pbCBob21vcGhvYmU/XShodHRwczovL2JsZnN0b3JlLmNvbS9wcm9kdWN0cy9kaWV1LWVzdC1pbC1ob21vcGhvYmUpKi4gTWFpcyBpbCBhIMOpY3JpdCBvdSBjb250cmlidcOpIMOgIHVuZSB2YXJpw6l0w6kgZGUgbGl2cmVzLCBub3RhbW1lbnQgZGVzIGNvbW1lbnRhaXJlcyBkdSBOb3V2ZWF1IFRlc3RhbWVudCwgZXQgZGVzIGxpdnJlcyBzdXIgbGEgbm91dmVsbGUgdmllIGVuIENocmlzdCBldCBzdXIgbOKAmWltcG9ydGFuY2UgZGUgbOKAmcOJZ2xpc2UgbG9jYWxlLCBwb3VyIG7igJllbiBjaXRlciBxdWUgcXVlbHF1ZXMtdW5zLiJ9LHt9XX0seyJpZCI6IjQwMDUiLCJ0aXRsZSI6IkxlIGNocmlzdGlhbmlzbWUgcmFiYWlzc2UtdC1pbCBsYSBmZW1tZT8iLCJzbHVnIjoiY2hyaXN0aWFuaXNtZS1yYWJhaXNzZS1mZW1tZSIsInR5cGUiOiJhcnRpY2xlIiwicHVibGlzaGVkX2F0IjoiMjAyNC0wNS0zMFQwMzo0NTowMC4wMDBaIiwiYm9keSI6IkNldXggcXVpIGxpc2VudCBtZXMgYXJ0aWNsZXMgZXQgc3VpdmVudCAqQ2hyw6l0aWVubmUqIG5lIHNlcm9udCBwYXMgc3VycHJpcyBkJ2FwcHJlbmRyZSBxdWUgY2Ugc3VqZXQgbWUgdGllbnQgcGFydGljdWxpw6hyZW1lbnQgw6AgY8WTdXIuIEonYWkgc291dmVudCBhZmZpcm3DqSBxdWUgbcOqbWUgbGVzIHBhc3NhZ2VzIGxlcyBwbHVzIFvDqXBpbmV1eCBkZSBsJ8OJY3JpdHVyZV0oL2Jsb2cvYW5naWUtdmVsYXNxdWV6LXRob3JudG9uL2ZpbHRyZXM/dG9waWM9VGV4dGVzJTIwZGlmZmljaWxlcyksIGNldXggcXVpIHNlbWJsZW50IGTDqW5pZ3JlciBsZXMgZmVtbWVzLCBmb250IHNvdXZlbnQgdG91dCBsZSBjb250cmFpcmUuIFN1cnRvdXQgc2kgbm91cyBwcmVub25zIGxlIHRlbXBzIGQnZW5sZXZlciBub3MgbHVuZXR0ZXMgbW9kZXJuZXMgZXQgb2NjaWRlbnRhbGVzIGV0IGRlIGxlcyDDqXR1ZGllciBkYW5zIGxldXIgY29udGV4dGUgY3VsdHVyZWwuXG5cbj4gU2kgbOKAmW9uIHNlIGNvbnRlbnRlIGRlIGxpcmUgZGVzIHBhc3NhZ2VzIMOgIGxhIHNldWxlIGx1bWnDqHJlIGRlIG5vcyBwcsOpc3VwcG9zw6lzLCBvbiBhdXJhIGJpZW4gZHUgbWFsIMOgIGxlcyBjb21wcmVuZHJlLiBTaSwgYXUgY29udHJhaXJlLCBvbiBsZXMgcmVwbGFjZSBkYW5zIGxlIHBhbm9yYW1hIGRlIGzigJloaXN0b2lyZSBkdSBzYWx1dCwgYWxvcnMgbGUgcG9zdHVsYXQgYmlibGlxdWUgY29uY2VybmFudCBs4oCZaG9tbWUgZXQgbGEgZmVtbWUgcmV2w6p0aXJhIHVuIHNlbnMgbm91dmVhdS5cblxuTWNMYXVnaGxpbiBzJ2V4cHJpbWUgYWluc2kgc3VyIGzigJllbnNlaWduZW1lbnQgZOKAmcOJcGjDqXNpZW5zIDUgc3VyIGxlIG1hcmlhZ2UsIGwndW4gZGVzIHBhc3NhZ2VzIGxlcyBwbHVzIGNvbm51cyBzdXIgbGUgZ2VucmU6XG5cbj4gTGVzIGNocsOpdGllbnMsIGTDqXNpcmV1eCBkZSBqdXN0aWZpZXIgbGVzIGNvbW1hbmRlbWVudHMgZGl2aW5zLCBlc3NhaWVudCBwYXJmb2lzIGTigJlhbmNyZXIgY2V0dGUgaW1hZ2UgZHUgbWFyaWFnZSBkYW5zIHVuZSBwc3ljaG9sb2dpZSBheMOpZSBzdXIgbGUgZ2VucmUgc2V4dWVsLiBE4oCZYXByw6hzIGV1eCwgbGVzIGZlbW1lcyBhdXJhaWVudCBuYXR1cmVsbGVtZW50IHRlbmRhbmNlIMOgIHN1aXZyZSwgYWxvcnMgcXVlIGxlcyBob21tZXMgYXVyYWllbnQgbmF0dXJlbGxlbWVudCB0ZW5kYW5jZSDDoCBkaXJpZ2VyLiBNYWlzIHZvaWzDoCwgbGVzIGhvbW1lcyBzb250IGF2YW50IHRvdXQgZXhob3J0w6lzIMOgIGFpbWVyIGV0IG5vbiDDoCBkaXJpZ2VyLiBPciwgamUgbuKAmWFpIGphbWFpcyBlbnRlbmR1IHF1aWNvbnF1ZSBzb3V0ZW5pciBxdWUgbGVzIGhvbW1lcyBzZXJhaWVudCBuYXR1cmVsbGVtZW50IHBsdXMgZW5jbGlucyDDoCBhaW1lcuKApiBMYSB2aXNpb24gcXVlIGRvbm5lIMOJcGjDqXNpZW5zIDUgZHUgbWFyaWFnZSBuZSBkw6lmaW5pdCBwYXMgbm9zIHLDtGxlcyBlbiBmb25jdGlvbiBk4oCZdW5lIHBzeWNob2xvZ2llIGNlbnRyw6llIHN1ciBsZSBnZW5yZSwgbWFpcyBlbiBmb25jdGlvbiBk4oCZdW5lIHRow6lvbG9naWUgY2VudHLDqWUgc3VyIENocmlzdC5cblxuUmV0cm91dmV6IGwnaW50w6lncmFsaXTDqSBkZSBub3RyZSBjb252ZXJzYXRpb24gZGFucyBbbCfDqXBpc29kZSBkZSBsYSBzZW1haW5lXSgvcG9kY2FzdHMvY2hyZXRpZW5uZS9jaHJpc3RpYW5pc21lLXJhYmFpc3NlLWZlbW1lcy1sZWEtcnljaGVuKS5cblxuU2kgdm91cyBuZSBsJ2F2ZXogcGFzIGVuY29yZSBmYWl0LCBwcm9jdXJlei12b3VzIHVuIGV4ZW1wbGFpcmUgZGUgKlsxMiByYWlzb25zIGRlIG5lIHBsdXMgY3JvaXJlIGF1IGNocmlzdGlhbmlzbWVdKGh0dHBzOi8vYmxmc3RvcmUuY29tL3Byb2R1Y3RzLzEyLXJhaXNvbnMtZGUtbmUtcGx1cy1jcm9pcmUtYXUtY2hyaXN0aWFuaXNtZSkqISIsImF1dGhvciI6eyJmdWxsTmFtZSI6IkFuZ2llIFZlbGFzcXVleiBUaG9ybnRvbiIsInBpY3R1cmUiOnsidXJsIjoiaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9JTUdfMjcyNV80YTdkZmQwODczLmpwZWciLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9fSwiaW1hZ2UiOnsidXJsIjoiaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9wZXhlbHNfdGhpcmRtYW5fODQ2ODQ2MV9hYmEyMDc1ZDlmLmpwZyIsImhlaWdodCI6NzMzLCJ3aWR0aCI6MTEwMCwiYWx0ZXJuYXRpdmVUZXh0IjoiIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifSwidG9waWNzIjpbeyJuYW1lIjoiQ29tcGzDqW1lbnRhcmlzbWUifSx7Im5hbWUiOiJGw6ltaW5pdMOpIn0seyJuYW1lIjoiSGlzdG9pcmUgZGUgbOKAmcOJZ2xpc2UifSx7Im5hbWUiOiJBcG9sb2fDqXRpcXVlIGV0IHZpc2lvbiBkdSBtb25kZSJ9XSwibW9kdWxlcyI6W3siX190eXBlbmFtZSI6IkNvbXBvbmVudE1vZHVsZUxlYWQiLCJjb250ZW50IjoiQ2V0dGUgc2VtYWluZSBzdXIgKkNocsOpdGllbm5lKiwgQXVyw6lsaWUgZXQgbW9pIGF2b25zIGV1IGxlIHBsYWlzaXIgZGUgcmVjZXZvaXIgTMOpYSBSeWNoZW4sIHVuZSBhbWllIGRlIGxvbmd1ZSBkYXRlIGRlIFRQU0csIGNyw6lhdHJpY2UgZGUgY29udGVudSBjaGV6ICpbSW1hZ28gRGVpXShodHRwczovL3d3dy5pbWFnb2RlaS5mcikqIGV0IGNvLWFuaW1hdHJpY2UgZHUgcG9kY2FzdCAqW1NhZ2Vzc2UgZXQgTW9qaXRvXShodHRwczovL3d3dy5pbWFnb2RlaS5mci9wb2RjYXN0cy9zYWdlc3NlLWV0LW1vaml0by8pKi4gUXVpIGRlIG1pZXV4IMOgIGludml0ZXIgcG91ciByw6lwb25kcmUgw6AgbCdhY2N1c2F0aW9uIGNvbW11bsOpbWVudCBwb3J0w6llIGNvbnRyZSBsZSBjaHJpc3RpYW5pc21lOiBsZSBjaHJpc3RpYW5pc21lIHJhYmFpc3NlLXQtaWwgbGVzIGZlbW1lcz8ifSx7fV19XX19Cg==", "status": 200, "url": "http://127.0.0.1:1337/graphql"}, "revalidate": 3600, "tags": []}