import styled from "styled-components";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";
import { device } from "styles/device";
import { removeLastBackSlash } from "utils/string.utils";
import InnerHTML from "dangerously-set-html-content";

const codeComponent = ({ children }) => {
  return(
    <InnerHTML html={children}/>
  )
}

const tableComponent = ({ children }) => (
  <div className={"table-container"}>
    <table>
      {children}
    </table>
  </div>
)

// Composant personnalisé pour les liens avec styles TPSG
const linkComponent = ({ href, children, ...props }) => (
  <a href={href} {...props}>
    {children}
  </a>
);

// Composant personnalisé pour les balises <u>
const underlineComponent = ({ children }) => (
  <u>{children}</u>
);

export default function MDPost({ content }) {

  // TODO: Unifier avec RenderMarkdown
  return (
    <Wrapper className={"md-post"}>
      <ReactMarkdown
        components={{
          "code": codeComponent,
          "table": tableComponent,
          "a": linkComponent,
          "u": underlineComponent,
        }}
        rehypePlugins={[rehypeRaw, remarkGfm]}>
        {removeLastBackSlash(content)}
      </ReactMarkdown>
    </Wrapper>
  )
}


const Wrapper = styled.div`
  font-family: "Lora", Charter, Times, "Times New Roman", serif;
  font-size: 18px;
  line-height: 28px;
  font-weight: 400;

  img {
    max-width: 100%;
  }

  h5, h6 {
    font-size: 18px;
    line-height: 28px;
    font-weight: 500;
    margin-bottom: 0;
  }

  h1,
  h2,
  h3,
  h4 {
    font-family: Stelvio, sans-serif;
    font-weight: 600;
    margin-bottom: 0;
  }

  h1,
  h2 {
    font-size: 28px;
    line-height: 32px;
    margin-top: 32px;

    &:first-child {
      margin-top: 0;
    }
  }

  h3 {
    font-size: 24px;
    line-height: 28px;
    margin-top: 12px;
  }

  p {
    font-size: 18px;
    line-height: 28px;
    margin-top: 0;
    margin-bottom: 24px;
    color: #161616;

    sup {
      vertical-align: top;
      position: relative;
      top: -0.4em;
      margin-left: 4px;
      margin-right: 4px;
    }
  }

  blockquote {
    position: relative;
    margin: 0;
    padding: 8px 0 8px 36px;

    &::before {
      content: "“";
      display: inline-block;
      position: absolute;
      font-size: 54px;
      font-weight: bold;
      color: #363636;
      left: 0;
      top: 18px;
      width: 100px;
    }
  }

  blockquote p {
    font-size: 18px;
    line-height: 28px;
    font-style: italic;
  }

  em {
  }

  ul, ol {
    margin-top: 12px;
    padding-left: 24px;
  }

  li {
    line-height: 170%;
    margin: 0 0 8px 0;
  }

  a {
    color: var(--brand-color) !important;
    text-decoration: none !important;
  }

  a:hover {
    color: var(--brand-color) !important;
    text-decoration: underline !important;
  }

  /* Spécificité renforcée pour les liens dans les paragraphes */
  p a {
    color: var(--brand-color) !important;
    text-decoration: none !important;
  }
  p a:hover {
    color: var(--brand-color) !important;
    text-decoration: underline !important;
  }

  /* Spécificité renforcée pour les liens dans les listes */
  li a {
    color: var(--brand-color) !important;
    text-decoration: none !important;
  }
  li a:hover {
    color: var(--brand-color) !important;
    text-decoration: underline !important;
  }

  /* Spécificité renforcée pour les balises u */
  u {
    text-decoration: underline !important;
  }

  hr {
    border: none;
    margin: 40px 0 40px 0;

    &::before {
      content: "***";
      display: block;
      letter-spacing: 10px;
      text-align: center;
      color: #161616;
    }
  }

  cite {
    &:before {
      content: "– ";
      font-size: 600;
    }
  }

  @media ${device.tablet} {
    max-width: 720px;
    margin: 80px 0 0 0;
    h1, h2 {
      font-size: 32px;
      line-height: 36px;
      margin-top: 44px;
      margin-bottom: 6px;
    }

    h3, h4 {
      margin-top: 32px;
      margin-bottom: 0;
      font-size: 26px;
      line-height: 30px;
    }

    h4 {
      opacity: 0.6;
      font-weight: 500;
    }

    p {
      color: #323232;
      font-size: 20px;
      line-height: 32px;
      font-weight: 400;
      margin-bottom: 24px;
    }

    ul, ol {
      margin-top: 12px;
      padding-left: 24px;
      margin-bottom: 24px;
      font-size: 20px;
      line-height: 32px;
    }

    blockquote p {
      font-size: 20px;
      line-height: 32px;
      font-weight: 500;
      font-style: italic;
      margin-bottom: 0;
    }

    blockquote {
      margin-bottom: 24px;
    }

    .post-content-button {
      display: inline-block;
      margin-top: 16px;
      margin-bottom: 16px;
      padding: 10px 32px;
      border-radius: 40px;
      text-align: center;
      color: white;
      text-decoration: none;
      font-family: Switzer, sans-serif;
      font-weight: 400;
      background-color: var(--brand-color);
    }
  }

  .table-container {
    position: relative;
    overflow-x: auto;
    margin-bottom: 24px;
    width: 100%;
    background-image: linear-gradient(to right, white, white), linear-gradient(to right, white, white), linear-gradient(to right, rgba(0, 0, 20, .50), rgba(255, 255, 255, 0)), linear-gradient(to left, rgba(0, 0, 20, .50), rgba(255, 255, 255, 0));
    /* Shadows */
    /* Shadow covers */
    background-position: left center, right center, left center, right center;
    background-repeat: no-repeat;
    background-color: white;
    background-size: 20px 100%, 20px 100%, 10px 100%, 16px 100%;
    background-attachment: local, local, scroll, scroll;
  }

  table {
    font-family: Switzer, sans-serif;
    border: 1px solid #ccc;
    border-collapse: collapse;
    padding: 0;
    width: 100%;
    overflow-x: auto;
  }

  table caption {
    font-size: 1.5em;
    margin: .5em 0 .75em;
  }

  table tr {
    background-color: rgba(248, 248, 248, 0.9);
    border: 1px solid #ddd;
    padding: .35em;
  }

  table th,
  table td {
    font-size: 0.85em;
    line-height: 1.4em;
    padding: .625em;
    text-align: left;
    min-width: 240px;
    color: rgba(0, 0, 0, 0.72);
  }

  table th {
    font-weight: 600;
    color: rgba(0, 0, 0, 9);
  }

  table th {
    font-size: 0.85em;
    //font-weight: 500;
    //text-transform: uppercase;
  }

  @media screen and (max-width: 600px) {

    .table-container {
      background: none;
    }
    table {
      border: 0;
    }

    table caption {
      font-size: 1.3em;
    }

    table thead {
      border: none;
      clip: rect(0 0 0 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px;
    }

    table tr {
      border-bottom: 3px solid #ddd;
      display: block;
      margin-bottom: .625em;
    }

    table td {
      border-bottom: 1px solid #ddd;
      display: block;
      font-size: .8em;
      text-align: left;
    }

    table td::before {
      /*
      * aria-label has no advantage, it won't be read inside a table
      content: attr(aria-label);
      */
      content: attr(data-label);
      float: left;
      font-weight: bold;
      text-transform: uppercase;
    }

    table td:last-child {
      border-bottom: 0;
    }
  }
`;
