"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_shared_ConvertkitForm_CKForm_js"],{

/***/ "./components/shared/ConvertkitForm/CKForm.js":
/*!****************************************************!*\
  !*** ./components/shared/ConvertkitForm/CKForm.js ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CKForm; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  width: 100%;\\n  .form-title {\\n    font-size: 42px;\\n    margin-top: 0;\\n    margin-bottom: 16px;\\n  }\\n  .form-desc {\\n    margin-top: 24px;\\n    margin-bottom: 24px;\\n    font-weight: 400;\\n    color: #161616;\\n    font-size: 22px;\\n  }\\n  .formkit-input {\\n    margin-bottom: 16px;\\n    background-color: #F0F0F0 !important;\\n    color: #161616 !important;\\n    border-radius: 0 !important;\\n    border: none !important;\\n    width: 100% !important;\\n    height: 52px;\\n    font-size: 18px !important;\\n    padding-left: 20px !important;\\n    &::placeholder {\\n      color: #888888 !important;\\n    }\\n  }\\n  .formkit-submit {\\n    margin-bottom: 16px;\\n    color: white !important;\\n    border: none !important;\\n    border-radius: 0 !important;\\n    background-color: #080808 !important;\\n    width: 100%;\\n    height: 52px;\\n    font-size: 18px !important;\\n    cursor: pointer;\\n    &:hover {\\n      background-color: var(--brand-color) !important;\\n    }\\n  }\\n  div {\\n    padding: 0 !important;\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\n\nfunction CKForm(param) {\n    let { title, desc, formString } = param;\n    if (!formString) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {}, void 0, false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(FormWrapper, {\n        children: [\n            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h4\", {\n                className: \"form-title\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\ConvertkitForm\\\\CKForm.js\",\n                lineNumber: 9,\n                columnNumber: 17\n            }, this),\n            desc && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                className: \"form-desc\",\n                children: desc\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\ConvertkitForm\\\\CKForm.js\",\n                lineNumber: 10,\n                columnNumber: 16\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                dangerouslySetInnerHTML: {\n                    __html: \"\".concat(formString)\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\ConvertkitForm\\\\CKForm.js\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\ConvertkitForm\\\\CKForm.js\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n_c = CKForm;\nconst FormWrapper = styled_components__WEBPACK_IMPORTED_MODULE_2__[\"default\"].div.withConfig({\n    displayName: \"CKForm__FormWrapper\",\n    componentId: \"sc-4ad002d4-0\"\n})(_templateObject());\n_c1 = FormWrapper;\nvar _c, _c1;\n$RefreshReg$(_c, \"CKForm\");\n$RefreshReg$(_c1, \"FormWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/shared/ConvertkitForm/CKForm.js\n"));

/***/ })

}]);