"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xml-js";
exports.ids = ["vendor-chunks/xml-js"];
exports.modules = {

/***/ "(rsc)/./node_modules/xml-js/lib/array-helper.js":
/*!*************************************************!*\
  !*** ./node_modules/xml-js/lib/array-helper.js ***!
  \*************************************************/
/***/ ((module) => {

eval("\nmodule.exports = {\n    isArray: function(value) {\n        if (Array.isArray) {\n            return Array.isArray(value);\n        }\n        // fallback for older browsers like  IE 8\n        return Object.prototype.toString.call(value) === \"[object Array]\";\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sLWpzL2xpYi9hcnJheS1oZWxwZXIuanMiLCJtYXBwaW5ncyI6IjtBQUFBQSxPQUFPQyxPQUFPLEdBQUc7SUFFZkMsU0FBUyxTQUFTQyxLQUFLO1FBQ3JCLElBQUlDLE1BQU1GLE9BQU8sRUFBRTtZQUNqQixPQUFPRSxNQUFNRixPQUFPLENBQUNDO1FBQ3ZCO1FBQ0EseUNBQXlDO1FBQ3pDLE9BQU9FLE9BQU9DLFNBQVMsQ0FBQ0MsUUFBUSxDQUFDQyxJQUFJLENBQUVMLFdBQVk7SUFDckQ7QUFFRiIsInNvdXJjZXMiOlsid2VicGFjazovL3Rwc2ctbmV4dC8uL25vZGVfbW9kdWxlcy94bWwtanMvbGliL2FycmF5LWhlbHBlci5qcz9iMDhiIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0ge1xyXG5cclxuICBpc0FycmF5OiBmdW5jdGlvbih2YWx1ZSkge1xyXG4gICAgaWYgKEFycmF5LmlzQXJyYXkpIHtcclxuICAgICAgcmV0dXJuIEFycmF5LmlzQXJyYXkodmFsdWUpO1xyXG4gICAgfVxyXG4gICAgLy8gZmFsbGJhY2sgZm9yIG9sZGVyIGJyb3dzZXJzIGxpa2UgIElFIDhcclxuICAgIHJldHVybiBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwoIHZhbHVlICkgPT09ICdbb2JqZWN0IEFycmF5XSc7XHJcbiAgfVxyXG5cclxufTtcclxuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJpc0FycmF5IiwidmFsdWUiLCJBcnJheSIsIk9iamVjdCIsInByb3RvdHlwZSIsInRvU3RyaW5nIiwiY2FsbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xml-js/lib/array-helper.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xml-js/lib/index.js":
/*!******************************************!*\
  !*** ./node_modules/xml-js/lib/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*jslint node:true */ \nvar xml2js = __webpack_require__(/*! ./xml2js */ \"(rsc)/./node_modules/xml-js/lib/xml2js.js\");\nvar xml2json = __webpack_require__(/*! ./xml2json */ \"(rsc)/./node_modules/xml-js/lib/xml2json.js\");\nvar js2xml = __webpack_require__(/*! ./js2xml */ \"(rsc)/./node_modules/xml-js/lib/js2xml.js\");\nvar json2xml = __webpack_require__(/*! ./json2xml */ \"(rsc)/./node_modules/xml-js/lib/json2xml.js\");\nmodule.exports = {\n    xml2js: xml2js,\n    xml2json: xml2json,\n    js2xml: js2xml,\n    json2xml: json2xml\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sLWpzL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxtQkFBbUI7QUFFbkIsSUFBSUEsU0FBU0MsbUJBQU9BLENBQUM7QUFDckIsSUFBSUMsV0FBV0QsbUJBQU9BLENBQUM7QUFDdkIsSUFBSUUsU0FBU0YsbUJBQU9BLENBQUM7QUFDckIsSUFBSUcsV0FBV0gsbUJBQU9BLENBQUM7QUFFdkJJLE9BQU9DLE9BQU8sR0FBRztJQUNmTixRQUFRQTtJQUNSRSxVQUFVQTtJQUNWQyxRQUFRQTtJQUNSQyxVQUFVQTtBQUNaIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHBzZy1uZXh0Ly4vbm9kZV9tb2R1bGVzL3htbC1qcy9saWIvaW5kZXguanM/ZWUyYiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKmpzbGludCBub2RlOnRydWUgKi9cclxuXHJcbnZhciB4bWwyanMgPSByZXF1aXJlKCcuL3htbDJqcycpO1xyXG52YXIgeG1sMmpzb24gPSByZXF1aXJlKCcuL3htbDJqc29uJyk7XHJcbnZhciBqczJ4bWwgPSByZXF1aXJlKCcuL2pzMnhtbCcpO1xyXG52YXIganNvbjJ4bWwgPSByZXF1aXJlKCcuL2pzb24yeG1sJyk7XHJcblxyXG5tb2R1bGUuZXhwb3J0cyA9IHtcclxuICB4bWwyanM6IHhtbDJqcyxcclxuICB4bWwyanNvbjogeG1sMmpzb24sXHJcbiAganMyeG1sOiBqczJ4bWwsXHJcbiAganNvbjJ4bWw6IGpzb24yeG1sXHJcbn07XHJcbiJdLCJuYW1lcyI6WyJ4bWwyanMiLCJyZXF1aXJlIiwieG1sMmpzb24iLCJqczJ4bWwiLCJqc29uMnhtbCIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xml-js/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xml-js/lib/js2xml.js":
/*!*******************************************!*\
  !*** ./node_modules/xml-js/lib/js2xml.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar helper = __webpack_require__(/*! ./options-helper */ \"(rsc)/./node_modules/xml-js/lib/options-helper.js\");\nvar isArray = (__webpack_require__(/*! ./array-helper */ \"(rsc)/./node_modules/xml-js/lib/array-helper.js\").isArray);\nvar currentElement, currentElementName;\nfunction validateOptions(userOptions) {\n    var options = helper.copyOptions(userOptions);\n    helper.ensureFlagExists(\"ignoreDeclaration\", options);\n    helper.ensureFlagExists(\"ignoreInstruction\", options);\n    helper.ensureFlagExists(\"ignoreAttributes\", options);\n    helper.ensureFlagExists(\"ignoreText\", options);\n    helper.ensureFlagExists(\"ignoreComment\", options);\n    helper.ensureFlagExists(\"ignoreCdata\", options);\n    helper.ensureFlagExists(\"ignoreDoctype\", options);\n    helper.ensureFlagExists(\"compact\", options);\n    helper.ensureFlagExists(\"indentText\", options);\n    helper.ensureFlagExists(\"indentCdata\", options);\n    helper.ensureFlagExists(\"indentAttributes\", options);\n    helper.ensureFlagExists(\"indentInstruction\", options);\n    helper.ensureFlagExists(\"fullTagEmptyElement\", options);\n    helper.ensureFlagExists(\"noQuotesForNativeAttributes\", options);\n    helper.ensureSpacesExists(options);\n    if (typeof options.spaces === \"number\") {\n        options.spaces = Array(options.spaces + 1).join(\" \");\n    }\n    helper.ensureKeyExists(\"declaration\", options);\n    helper.ensureKeyExists(\"instruction\", options);\n    helper.ensureKeyExists(\"attributes\", options);\n    helper.ensureKeyExists(\"text\", options);\n    helper.ensureKeyExists(\"comment\", options);\n    helper.ensureKeyExists(\"cdata\", options);\n    helper.ensureKeyExists(\"doctype\", options);\n    helper.ensureKeyExists(\"type\", options);\n    helper.ensureKeyExists(\"name\", options);\n    helper.ensureKeyExists(\"elements\", options);\n    helper.checkFnExists(\"doctype\", options);\n    helper.checkFnExists(\"instruction\", options);\n    helper.checkFnExists(\"cdata\", options);\n    helper.checkFnExists(\"comment\", options);\n    helper.checkFnExists(\"text\", options);\n    helper.checkFnExists(\"instructionName\", options);\n    helper.checkFnExists(\"elementName\", options);\n    helper.checkFnExists(\"attributeName\", options);\n    helper.checkFnExists(\"attributeValue\", options);\n    helper.checkFnExists(\"attributes\", options);\n    helper.checkFnExists(\"fullTagEmptyElement\", options);\n    return options;\n}\nfunction writeIndentation(options, depth, firstLine) {\n    return (!firstLine && options.spaces ? \"\\n\" : \"\") + Array(depth + 1).join(options.spaces);\n}\nfunction writeAttributes(attributes, options, depth) {\n    if (options.ignoreAttributes) {\n        return \"\";\n    }\n    if (\"attributesFn\" in options) {\n        attributes = options.attributesFn(attributes, currentElementName, currentElement);\n    }\n    var key, attr, attrName, quote, result = [];\n    for(key in attributes){\n        if (attributes.hasOwnProperty(key) && attributes[key] !== null && attributes[key] !== undefined) {\n            quote = options.noQuotesForNativeAttributes && typeof attributes[key] !== \"string\" ? \"\" : '\"';\n            attr = \"\" + attributes[key]; // ensure number and boolean are converted to String\n            attr = attr.replace(/\"/g, \"&quot;\");\n            attrName = \"attributeNameFn\" in options ? options.attributeNameFn(key, attr, currentElementName, currentElement) : key;\n            result.push(options.spaces && options.indentAttributes ? writeIndentation(options, depth + 1, false) : \" \");\n            result.push(attrName + \"=\" + quote + (\"attributeValueFn\" in options ? options.attributeValueFn(attr, key, currentElementName, currentElement) : attr) + quote);\n        }\n    }\n    if (attributes && Object.keys(attributes).length && options.spaces && options.indentAttributes) {\n        result.push(writeIndentation(options, depth, false));\n    }\n    return result.join(\"\");\n}\nfunction writeDeclaration(declaration, options, depth) {\n    currentElement = declaration;\n    currentElementName = \"xml\";\n    return options.ignoreDeclaration ? \"\" : \"<?\" + \"xml\" + writeAttributes(declaration[options.attributesKey], options, depth) + \"?>\";\n}\nfunction writeInstruction(instruction, options, depth) {\n    if (options.ignoreInstruction) {\n        return \"\";\n    }\n    var key;\n    for(key in instruction){\n        if (instruction.hasOwnProperty(key)) {\n            break;\n        }\n    }\n    var instructionName = \"instructionNameFn\" in options ? options.instructionNameFn(key, instruction[key], currentElementName, currentElement) : key;\n    if (typeof instruction[key] === \"object\") {\n        currentElement = instruction;\n        currentElementName = instructionName;\n        return \"<?\" + instructionName + writeAttributes(instruction[key][options.attributesKey], options, depth) + \"?>\";\n    } else {\n        var instructionValue = instruction[key] ? instruction[key] : \"\";\n        if (\"instructionFn\" in options) instructionValue = options.instructionFn(instructionValue, key, currentElementName, currentElement);\n        return \"<?\" + instructionName + (instructionValue ? \" \" + instructionValue : \"\") + \"?>\";\n    }\n}\nfunction writeComment(comment, options) {\n    return options.ignoreComment ? \"\" : \"<!--\" + (\"commentFn\" in options ? options.commentFn(comment, currentElementName, currentElement) : comment) + \"-->\";\n}\nfunction writeCdata(cdata, options) {\n    return options.ignoreCdata ? \"\" : \"<![CDATA[\" + (\"cdataFn\" in options ? options.cdataFn(cdata, currentElementName, currentElement) : cdata.replace(\"]]>\", \"]]]]><![CDATA[>\")) + \"]]>\";\n}\nfunction writeDoctype(doctype, options) {\n    return options.ignoreDoctype ? \"\" : \"<!DOCTYPE \" + (\"doctypeFn\" in options ? options.doctypeFn(doctype, currentElementName, currentElement) : doctype) + \">\";\n}\nfunction writeText(text, options) {\n    if (options.ignoreText) return \"\";\n    text = \"\" + text; // ensure Number and Boolean are converted to String\n    text = text.replace(/&amp;/g, \"&\"); // desanitize to avoid double sanitization\n    text = text.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    return \"textFn\" in options ? options.textFn(text, currentElementName, currentElement) : text;\n}\nfunction hasContent(element, options) {\n    var i;\n    if (element.elements && element.elements.length) {\n        for(i = 0; i < element.elements.length; ++i){\n            switch(element.elements[i][options.typeKey]){\n                case \"text\":\n                    if (options.indentText) {\n                        return true;\n                    }\n                    break; // skip to next key\n                case \"cdata\":\n                    if (options.indentCdata) {\n                        return true;\n                    }\n                    break; // skip to next key\n                case \"instruction\":\n                    if (options.indentInstruction) {\n                        return true;\n                    }\n                    break; // skip to next key\n                case \"doctype\":\n                case \"comment\":\n                case \"element\":\n                    return true;\n                default:\n                    return true;\n            }\n        }\n    }\n    return false;\n}\nfunction writeElement(element, options, depth) {\n    currentElement = element;\n    currentElementName = element.name;\n    var xml = [], elementName = \"elementNameFn\" in options ? options.elementNameFn(element.name, element) : element.name;\n    xml.push(\"<\" + elementName);\n    if (element[options.attributesKey]) {\n        xml.push(writeAttributes(element[options.attributesKey], options, depth));\n    }\n    var withClosingTag = element[options.elementsKey] && element[options.elementsKey].length || element[options.attributesKey] && element[options.attributesKey][\"xml:space\"] === \"preserve\";\n    if (!withClosingTag) {\n        if (\"fullTagEmptyElementFn\" in options) {\n            withClosingTag = options.fullTagEmptyElementFn(element.name, element);\n        } else {\n            withClosingTag = options.fullTagEmptyElement;\n        }\n    }\n    if (withClosingTag) {\n        xml.push(\">\");\n        if (element[options.elementsKey] && element[options.elementsKey].length) {\n            xml.push(writeElements(element[options.elementsKey], options, depth + 1));\n            currentElement = element;\n            currentElementName = element.name;\n        }\n        xml.push(options.spaces && hasContent(element, options) ? \"\\n\" + Array(depth + 1).join(options.spaces) : \"\");\n        xml.push(\"</\" + elementName + \">\");\n    } else {\n        xml.push(\"/>\");\n    }\n    return xml.join(\"\");\n}\nfunction writeElements(elements, options, depth, firstLine) {\n    return elements.reduce(function(xml, element) {\n        var indent = writeIndentation(options, depth, firstLine && !xml);\n        switch(element.type){\n            case \"element\":\n                return xml + indent + writeElement(element, options, depth);\n            case \"comment\":\n                return xml + indent + writeComment(element[options.commentKey], options);\n            case \"doctype\":\n                return xml + indent + writeDoctype(element[options.doctypeKey], options);\n            case \"cdata\":\n                return xml + (options.indentCdata ? indent : \"\") + writeCdata(element[options.cdataKey], options);\n            case \"text\":\n                return xml + (options.indentText ? indent : \"\") + writeText(element[options.textKey], options);\n            case \"instruction\":\n                var instruction = {};\n                instruction[element[options.nameKey]] = element[options.attributesKey] ? element : element[options.instructionKey];\n                return xml + (options.indentInstruction ? indent : \"\") + writeInstruction(instruction, options, depth);\n        }\n    }, \"\");\n}\nfunction hasContentCompact(element, options, anyContent) {\n    var key;\n    for(key in element){\n        if (element.hasOwnProperty(key)) {\n            switch(key){\n                case options.parentKey:\n                case options.attributesKey:\n                    break; // skip to next key\n                case options.textKey:\n                    if (options.indentText || anyContent) {\n                        return true;\n                    }\n                    break; // skip to next key\n                case options.cdataKey:\n                    if (options.indentCdata || anyContent) {\n                        return true;\n                    }\n                    break; // skip to next key\n                case options.instructionKey:\n                    if (options.indentInstruction || anyContent) {\n                        return true;\n                    }\n                    break; // skip to next key\n                case options.doctypeKey:\n                case options.commentKey:\n                    return true;\n                default:\n                    return true;\n            }\n        }\n    }\n    return false;\n}\nfunction writeElementCompact(element, name, options, depth, indent) {\n    currentElement = element;\n    currentElementName = name;\n    var elementName = \"elementNameFn\" in options ? options.elementNameFn(name, element) : name;\n    if (typeof element === \"undefined\" || element === null || element === \"\") {\n        return \"fullTagEmptyElementFn\" in options && options.fullTagEmptyElementFn(name, element) || options.fullTagEmptyElement ? \"<\" + elementName + \"></\" + elementName + \">\" : \"<\" + elementName + \"/>\";\n    }\n    var xml = [];\n    if (name) {\n        xml.push(\"<\" + elementName);\n        if (typeof element !== \"object\") {\n            xml.push(\">\" + writeText(element, options) + \"</\" + elementName + \">\");\n            return xml.join(\"\");\n        }\n        if (element[options.attributesKey]) {\n            xml.push(writeAttributes(element[options.attributesKey], options, depth));\n        }\n        var withClosingTag = hasContentCompact(element, options, true) || element[options.attributesKey] && element[options.attributesKey][\"xml:space\"] === \"preserve\";\n        if (!withClosingTag) {\n            if (\"fullTagEmptyElementFn\" in options) {\n                withClosingTag = options.fullTagEmptyElementFn(name, element);\n            } else {\n                withClosingTag = options.fullTagEmptyElement;\n            }\n        }\n        if (withClosingTag) {\n            xml.push(\">\");\n        } else {\n            xml.push(\"/>\");\n            return xml.join(\"\");\n        }\n    }\n    xml.push(writeElementsCompact(element, options, depth + 1, false));\n    currentElement = element;\n    currentElementName = name;\n    if (name) {\n        xml.push((indent ? writeIndentation(options, depth, false) : \"\") + \"</\" + elementName + \">\");\n    }\n    return xml.join(\"\");\n}\nfunction writeElementsCompact(element, options, depth, firstLine) {\n    var i, key, nodes, xml = [];\n    for(key in element){\n        if (element.hasOwnProperty(key)) {\n            nodes = isArray(element[key]) ? element[key] : [\n                element[key]\n            ];\n            for(i = 0; i < nodes.length; ++i){\n                switch(key){\n                    case options.declarationKey:\n                        xml.push(writeDeclaration(nodes[i], options, depth));\n                        break;\n                    case options.instructionKey:\n                        xml.push((options.indentInstruction ? writeIndentation(options, depth, firstLine) : \"\") + writeInstruction(nodes[i], options, depth));\n                        break;\n                    case options.attributesKey:\n                    case options.parentKey:\n                        break; // skip\n                    case options.textKey:\n                        xml.push((options.indentText ? writeIndentation(options, depth, firstLine) : \"\") + writeText(nodes[i], options));\n                        break;\n                    case options.cdataKey:\n                        xml.push((options.indentCdata ? writeIndentation(options, depth, firstLine) : \"\") + writeCdata(nodes[i], options));\n                        break;\n                    case options.doctypeKey:\n                        xml.push(writeIndentation(options, depth, firstLine) + writeDoctype(nodes[i], options));\n                        break;\n                    case options.commentKey:\n                        xml.push(writeIndentation(options, depth, firstLine) + writeComment(nodes[i], options));\n                        break;\n                    default:\n                        xml.push(writeIndentation(options, depth, firstLine) + writeElementCompact(nodes[i], key, options, depth, hasContentCompact(nodes[i], options)));\n                }\n                firstLine = firstLine && !xml.length;\n            }\n        }\n    }\n    return xml.join(\"\");\n}\nmodule.exports = function(js, options) {\n    options = validateOptions(options);\n    var xml = [];\n    currentElement = js;\n    currentElementName = \"_root_\";\n    if (options.compact) {\n        xml.push(writeElementsCompact(js, options, 0, true));\n    } else {\n        if (js[options.declarationKey]) {\n            xml.push(writeDeclaration(js[options.declarationKey], options, 0));\n        }\n        if (js[options.elementsKey] && js[options.elementsKey].length) {\n            xml.push(writeElements(js[options.elementsKey], options, 0, !xml.length));\n        }\n    }\n    return xml.join(\"\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xml-js/lib/js2xml.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xml-js/lib/json2xml.js":
/*!*********************************************!*\
  !*** ./node_modules/xml-js/lib/json2xml.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar js2xml = __webpack_require__(/*! ./js2xml.js */ \"(rsc)/./node_modules/xml-js/lib/js2xml.js\");\nmodule.exports = function(json, options) {\n    if (json instanceof Buffer) {\n        json = json.toString();\n    }\n    var js = null;\n    if (typeof json === \"string\") {\n        try {\n            js = JSON.parse(json);\n        } catch (e) {\n            throw new Error(\"The JSON structure is invalid\");\n        }\n    } else {\n        js = json;\n    }\n    return js2xml(js, options);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sLWpzL2xpYi9qc29uMnhtbC5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsSUFBSUEsU0FBU0MsbUJBQU9BLENBQUM7QUFFckJDLE9BQU9DLE9BQU8sR0FBRyxTQUFVQyxJQUFJLEVBQUVDLE9BQU87SUFDdEMsSUFBSUQsZ0JBQWdCRSxRQUFRO1FBQzFCRixPQUFPQSxLQUFLRyxRQUFRO0lBQ3RCO0lBQ0EsSUFBSUMsS0FBSztJQUNULElBQUksT0FBUUosU0FBVSxVQUFVO1FBQzlCLElBQUk7WUFDRkksS0FBS0MsS0FBS0MsS0FBSyxDQUFDTjtRQUNsQixFQUFFLE9BQU9PLEdBQUc7WUFDVixNQUFNLElBQUlDLE1BQU07UUFDbEI7SUFDRixPQUFPO1FBQ0xKLEtBQUtKO0lBQ1A7SUFDQSxPQUFPSixPQUFPUSxJQUFJSDtBQUNwQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Rwc2ctbmV4dC8uL25vZGVfbW9kdWxlcy94bWwtanMvbGliL2pzb24yeG1sLmpzPzhhNjAiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGpzMnhtbCA9IHJlcXVpcmUoJy4vanMyeG1sLmpzJyk7XHJcblxyXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIChqc29uLCBvcHRpb25zKSB7XHJcbiAgaWYgKGpzb24gaW5zdGFuY2VvZiBCdWZmZXIpIHtcclxuICAgIGpzb24gPSBqc29uLnRvU3RyaW5nKCk7XHJcbiAgfVxyXG4gIHZhciBqcyA9IG51bGw7XHJcbiAgaWYgKHR5cGVvZiAoanNvbikgPT09ICdzdHJpbmcnKSB7XHJcbiAgICB0cnkge1xyXG4gICAgICBqcyA9IEpTT04ucGFyc2UoanNvbik7XHJcbiAgICB9IGNhdGNoIChlKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignVGhlIEpTT04gc3RydWN0dXJlIGlzIGludmFsaWQnKTtcclxuICAgIH1cclxuICB9IGVsc2Uge1xyXG4gICAganMgPSBqc29uO1xyXG4gIH1cclxuICByZXR1cm4ganMyeG1sKGpzLCBvcHRpb25zKTtcclxufTtcclxuIl0sIm5hbWVzIjpbImpzMnhtbCIsInJlcXVpcmUiLCJtb2R1bGUiLCJleHBvcnRzIiwianNvbiIsIm9wdGlvbnMiLCJCdWZmZXIiLCJ0b1N0cmluZyIsImpzIiwiSlNPTiIsInBhcnNlIiwiZSIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xml-js/lib/json2xml.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xml-js/lib/options-helper.js":
/*!***************************************************!*\
  !*** ./node_modules/xml-js/lib/options-helper.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar isArray = (__webpack_require__(/*! ./array-helper */ \"(rsc)/./node_modules/xml-js/lib/array-helper.js\").isArray);\nmodule.exports = {\n    copyOptions: function(options) {\n        var key, copy = {};\n        for(key in options){\n            if (options.hasOwnProperty(key)) {\n                copy[key] = options[key];\n            }\n        }\n        return copy;\n    },\n    ensureFlagExists: function(item, options) {\n        if (!(item in options) || typeof options[item] !== \"boolean\") {\n            options[item] = false;\n        }\n    },\n    ensureSpacesExists: function(options) {\n        if (!(\"spaces\" in options) || typeof options.spaces !== \"number\" && typeof options.spaces !== \"string\") {\n            options.spaces = 0;\n        }\n    },\n    ensureAlwaysArrayExists: function(options) {\n        if (!(\"alwaysArray\" in options) || typeof options.alwaysArray !== \"boolean\" && !isArray(options.alwaysArray)) {\n            options.alwaysArray = false;\n        }\n    },\n    ensureKeyExists: function(key, options) {\n        if (!(key + \"Key\" in options) || typeof options[key + \"Key\"] !== \"string\") {\n            options[key + \"Key\"] = options.compact ? \"_\" + key : key;\n        }\n    },\n    checkFnExists: function(key, options) {\n        return key + \"Fn\" in options;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sLWpzL2xpYi9vcHRpb25zLWhlbHBlci5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsSUFBSUEsVUFBVUMsc0dBQWlDO0FBRS9DQyxPQUFPQyxPQUFPLEdBQUc7SUFFZkMsYUFBYSxTQUFVQyxPQUFPO1FBQzVCLElBQUlDLEtBQUtDLE9BQU8sQ0FBQztRQUNqQixJQUFLRCxPQUFPRCxRQUFTO1lBQ25CLElBQUlBLFFBQVFHLGNBQWMsQ0FBQ0YsTUFBTTtnQkFDL0JDLElBQUksQ0FBQ0QsSUFBSSxHQUFHRCxPQUFPLENBQUNDLElBQUk7WUFDMUI7UUFDRjtRQUNBLE9BQU9DO0lBQ1Q7SUFFQUUsa0JBQWtCLFNBQVVDLElBQUksRUFBRUwsT0FBTztRQUN2QyxJQUFJLENBQUVLLENBQUFBLFFBQVFMLE9BQU0sS0FBTSxPQUFPQSxPQUFPLENBQUNLLEtBQUssS0FBSyxXQUFXO1lBQzVETCxPQUFPLENBQUNLLEtBQUssR0FBRztRQUNsQjtJQUNGO0lBRUFDLG9CQUFvQixTQUFVTixPQUFPO1FBQ25DLElBQUksQ0FBRSxhQUFZQSxPQUFNLEtBQU8sT0FBT0EsUUFBUU8sTUFBTSxLQUFLLFlBQVksT0FBT1AsUUFBUU8sTUFBTSxLQUFLLFVBQVc7WUFDeEdQLFFBQVFPLE1BQU0sR0FBRztRQUNuQjtJQUNGO0lBRUFDLHlCQUF5QixTQUFVUixPQUFPO1FBQ3hDLElBQUksQ0FBRSxrQkFBaUJBLE9BQU0sS0FBTyxPQUFPQSxRQUFRUyxXQUFXLEtBQUssYUFBYSxDQUFDZCxRQUFRSyxRQUFRUyxXQUFXLEdBQUk7WUFDOUdULFFBQVFTLFdBQVcsR0FBRztRQUN4QjtJQUNGO0lBRUFDLGlCQUFpQixTQUFVVCxHQUFHLEVBQUVELE9BQU87UUFDckMsSUFBSSxDQUFFQyxDQUFBQSxNQUFNLFNBQVNELE9BQU0sS0FBTSxPQUFPQSxPQUFPLENBQUNDLE1BQU0sTUFBTSxLQUFLLFVBQVU7WUFDekVELE9BQU8sQ0FBQ0MsTUFBTSxNQUFNLEdBQUdELFFBQVFXLE9BQU8sR0FBRyxNQUFNVixNQUFNQTtRQUN2RDtJQUNGO0lBRUFXLGVBQWUsU0FBVVgsR0FBRyxFQUFFRCxPQUFPO1FBQ25DLE9BQU9DLE1BQU0sUUFBUUQ7SUFDdkI7QUFFRiIsInNvdXJjZXMiOlsid2VicGFjazovL3Rwc2ctbmV4dC8uL25vZGVfbW9kdWxlcy94bWwtanMvbGliL29wdGlvbnMtaGVscGVyLmpzPzQzNmEiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGlzQXJyYXkgPSByZXF1aXJlKCcuL2FycmF5LWhlbHBlcicpLmlzQXJyYXk7XHJcblxyXG5tb2R1bGUuZXhwb3J0cyA9IHtcclxuXHJcbiAgY29weU9wdGlvbnM6IGZ1bmN0aW9uIChvcHRpb25zKSB7XHJcbiAgICB2YXIga2V5LCBjb3B5ID0ge307XHJcbiAgICBmb3IgKGtleSBpbiBvcHRpb25zKSB7XHJcbiAgICAgIGlmIChvcHRpb25zLmhhc093blByb3BlcnR5KGtleSkpIHtcclxuICAgICAgICBjb3B5W2tleV0gPSBvcHRpb25zW2tleV07XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgIHJldHVybiBjb3B5O1xyXG4gIH0sXHJcblxyXG4gIGVuc3VyZUZsYWdFeGlzdHM6IGZ1bmN0aW9uIChpdGVtLCBvcHRpb25zKSB7XHJcbiAgICBpZiAoIShpdGVtIGluIG9wdGlvbnMpIHx8IHR5cGVvZiBvcHRpb25zW2l0ZW1dICE9PSAnYm9vbGVhbicpIHtcclxuICAgICAgb3B0aW9uc1tpdGVtXSA9IGZhbHNlO1xyXG4gICAgfVxyXG4gIH0sXHJcblxyXG4gIGVuc3VyZVNwYWNlc0V4aXN0czogZnVuY3Rpb24gKG9wdGlvbnMpIHtcclxuICAgIGlmICghKCdzcGFjZXMnIGluIG9wdGlvbnMpIHx8ICh0eXBlb2Ygb3B0aW9ucy5zcGFjZXMgIT09ICdudW1iZXInICYmIHR5cGVvZiBvcHRpb25zLnNwYWNlcyAhPT0gJ3N0cmluZycpKSB7XHJcbiAgICAgIG9wdGlvbnMuc3BhY2VzID0gMDtcclxuICAgIH1cclxuICB9LFxyXG5cclxuICBlbnN1cmVBbHdheXNBcnJheUV4aXN0czogZnVuY3Rpb24gKG9wdGlvbnMpIHtcclxuICAgIGlmICghKCdhbHdheXNBcnJheScgaW4gb3B0aW9ucykgfHwgKHR5cGVvZiBvcHRpb25zLmFsd2F5c0FycmF5ICE9PSAnYm9vbGVhbicgJiYgIWlzQXJyYXkob3B0aW9ucy5hbHdheXNBcnJheSkpKSB7XHJcbiAgICAgIG9wdGlvbnMuYWx3YXlzQXJyYXkgPSBmYWxzZTtcclxuICAgIH1cclxuICB9LFxyXG5cclxuICBlbnN1cmVLZXlFeGlzdHM6IGZ1bmN0aW9uIChrZXksIG9wdGlvbnMpIHtcclxuICAgIGlmICghKGtleSArICdLZXknIGluIG9wdGlvbnMpIHx8IHR5cGVvZiBvcHRpb25zW2tleSArICdLZXknXSAhPT0gJ3N0cmluZycpIHtcclxuICAgICAgb3B0aW9uc1trZXkgKyAnS2V5J10gPSBvcHRpb25zLmNvbXBhY3QgPyAnXycgKyBrZXkgOiBrZXk7XHJcbiAgICB9XHJcbiAgfSxcclxuXHJcbiAgY2hlY2tGbkV4aXN0czogZnVuY3Rpb24gKGtleSwgb3B0aW9ucykge1xyXG4gICAgcmV0dXJuIGtleSArICdGbicgaW4gb3B0aW9ucztcclxuICB9XHJcblxyXG59O1xyXG4iXSwibmFtZXMiOlsiaXNBcnJheSIsInJlcXVpcmUiLCJtb2R1bGUiLCJleHBvcnRzIiwiY29weU9wdGlvbnMiLCJvcHRpb25zIiwia2V5IiwiY29weSIsImhhc093blByb3BlcnR5IiwiZW5zdXJlRmxhZ0V4aXN0cyIsIml0ZW0iLCJlbnN1cmVTcGFjZXNFeGlzdHMiLCJzcGFjZXMiLCJlbnN1cmVBbHdheXNBcnJheUV4aXN0cyIsImFsd2F5c0FycmF5IiwiZW5zdXJlS2V5RXhpc3RzIiwiY29tcGFjdCIsImNoZWNrRm5FeGlzdHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xml-js/lib/options-helper.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xml-js/lib/xml2js.js":
/*!*******************************************!*\
  !*** ./node_modules/xml-js/lib/xml2js.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar sax = __webpack_require__(/*! sax */ \"(rsc)/./node_modules/sax/lib/sax.js\");\nvar expat /*= require('node-expat');*/  = {\n    on: function() {},\n    parse: function() {}\n};\nvar helper = __webpack_require__(/*! ./options-helper */ \"(rsc)/./node_modules/xml-js/lib/options-helper.js\");\nvar isArray = (__webpack_require__(/*! ./array-helper */ \"(rsc)/./node_modules/xml-js/lib/array-helper.js\").isArray);\nvar options;\nvar pureJsParser = true;\nvar currentElement;\nfunction validateOptions(userOptions) {\n    options = helper.copyOptions(userOptions);\n    helper.ensureFlagExists(\"ignoreDeclaration\", options);\n    helper.ensureFlagExists(\"ignoreInstruction\", options);\n    helper.ensureFlagExists(\"ignoreAttributes\", options);\n    helper.ensureFlagExists(\"ignoreText\", options);\n    helper.ensureFlagExists(\"ignoreComment\", options);\n    helper.ensureFlagExists(\"ignoreCdata\", options);\n    helper.ensureFlagExists(\"ignoreDoctype\", options);\n    helper.ensureFlagExists(\"compact\", options);\n    helper.ensureFlagExists(\"alwaysChildren\", options);\n    helper.ensureFlagExists(\"addParent\", options);\n    helper.ensureFlagExists(\"trim\", options);\n    helper.ensureFlagExists(\"nativeType\", options);\n    helper.ensureFlagExists(\"nativeTypeAttributes\", options);\n    helper.ensureFlagExists(\"sanitize\", options);\n    helper.ensureFlagExists(\"instructionHasAttributes\", options);\n    helper.ensureFlagExists(\"captureSpacesBetweenElements\", options);\n    helper.ensureAlwaysArrayExists(options);\n    helper.ensureKeyExists(\"declaration\", options);\n    helper.ensureKeyExists(\"instruction\", options);\n    helper.ensureKeyExists(\"attributes\", options);\n    helper.ensureKeyExists(\"text\", options);\n    helper.ensureKeyExists(\"comment\", options);\n    helper.ensureKeyExists(\"cdata\", options);\n    helper.ensureKeyExists(\"doctype\", options);\n    helper.ensureKeyExists(\"type\", options);\n    helper.ensureKeyExists(\"name\", options);\n    helper.ensureKeyExists(\"elements\", options);\n    helper.ensureKeyExists(\"parent\", options);\n    helper.checkFnExists(\"doctype\", options);\n    helper.checkFnExists(\"instruction\", options);\n    helper.checkFnExists(\"cdata\", options);\n    helper.checkFnExists(\"comment\", options);\n    helper.checkFnExists(\"text\", options);\n    helper.checkFnExists(\"instructionName\", options);\n    helper.checkFnExists(\"elementName\", options);\n    helper.checkFnExists(\"attributeName\", options);\n    helper.checkFnExists(\"attributeValue\", options);\n    helper.checkFnExists(\"attributes\", options);\n    return options;\n}\nfunction nativeType(value) {\n    var nValue = Number(value);\n    if (!isNaN(nValue)) {\n        return nValue;\n    }\n    var bValue = value.toLowerCase();\n    if (bValue === \"true\") {\n        return true;\n    } else if (bValue === \"false\") {\n        return false;\n    }\n    return value;\n}\nfunction addField(type, value) {\n    var key;\n    if (options.compact) {\n        if (!currentElement[options[type + \"Key\"]] && (isArray(options.alwaysArray) ? options.alwaysArray.indexOf(options[type + \"Key\"]) !== -1 : options.alwaysArray)) {\n            currentElement[options[type + \"Key\"]] = [];\n        }\n        if (currentElement[options[type + \"Key\"]] && !isArray(currentElement[options[type + \"Key\"]])) {\n            currentElement[options[type + \"Key\"]] = [\n                currentElement[options[type + \"Key\"]]\n            ];\n        }\n        if (type + \"Fn\" in options && typeof value === \"string\") {\n            value = options[type + \"Fn\"](value, currentElement);\n        }\n        if (type === \"instruction\" && (\"instructionFn\" in options || \"instructionNameFn\" in options)) {\n            for(key in value){\n                if (value.hasOwnProperty(key)) {\n                    if (\"instructionFn\" in options) {\n                        value[key] = options.instructionFn(value[key], key, currentElement);\n                    } else {\n                        var temp = value[key];\n                        delete value[key];\n                        value[options.instructionNameFn(key, temp, currentElement)] = temp;\n                    }\n                }\n            }\n        }\n        if (isArray(currentElement[options[type + \"Key\"]])) {\n            currentElement[options[type + \"Key\"]].push(value);\n        } else {\n            currentElement[options[type + \"Key\"]] = value;\n        }\n    } else {\n        if (!currentElement[options.elementsKey]) {\n            currentElement[options.elementsKey] = [];\n        }\n        var element = {};\n        element[options.typeKey] = type;\n        if (type === \"instruction\") {\n            for(key in value){\n                if (value.hasOwnProperty(key)) {\n                    break;\n                }\n            }\n            element[options.nameKey] = \"instructionNameFn\" in options ? options.instructionNameFn(key, value, currentElement) : key;\n            if (options.instructionHasAttributes) {\n                element[options.attributesKey] = value[key][options.attributesKey];\n                if (\"instructionFn\" in options) {\n                    element[options.attributesKey] = options.instructionFn(element[options.attributesKey], key, currentElement);\n                }\n            } else {\n                if (\"instructionFn\" in options) {\n                    value[key] = options.instructionFn(value[key], key, currentElement);\n                }\n                element[options.instructionKey] = value[key];\n            }\n        } else {\n            if (type + \"Fn\" in options) {\n                value = options[type + \"Fn\"](value, currentElement);\n            }\n            element[options[type + \"Key\"]] = value;\n        }\n        if (options.addParent) {\n            element[options.parentKey] = currentElement;\n        }\n        currentElement[options.elementsKey].push(element);\n    }\n}\nfunction manipulateAttributes(attributes) {\n    if (\"attributesFn\" in options && attributes) {\n        attributes = options.attributesFn(attributes, currentElement);\n    }\n    if ((options.trim || \"attributeValueFn\" in options || \"attributeNameFn\" in options || options.nativeTypeAttributes) && attributes) {\n        var key;\n        for(key in attributes){\n            if (attributes.hasOwnProperty(key)) {\n                if (options.trim) attributes[key] = attributes[key].trim();\n                if (options.nativeTypeAttributes) {\n                    attributes[key] = nativeType(attributes[key]);\n                }\n                if (\"attributeValueFn\" in options) attributes[key] = options.attributeValueFn(attributes[key], key, currentElement);\n                if (\"attributeNameFn\" in options) {\n                    var temp = attributes[key];\n                    delete attributes[key];\n                    attributes[options.attributeNameFn(key, attributes[key], currentElement)] = temp;\n                }\n            }\n        }\n    }\n    return attributes;\n}\nfunction onInstruction(instruction) {\n    var attributes = {};\n    if (instruction.body && (instruction.name.toLowerCase() === \"xml\" || options.instructionHasAttributes)) {\n        var attrsRegExp = /([\\w:-]+)\\s*=\\s*(?:\"([^\"]*)\"|'([^']*)'|(\\w+))\\s*/g;\n        var match;\n        while((match = attrsRegExp.exec(instruction.body)) !== null){\n            attributes[match[1]] = match[2] || match[3] || match[4];\n        }\n        attributes = manipulateAttributes(attributes);\n    }\n    if (instruction.name.toLowerCase() === \"xml\") {\n        if (options.ignoreDeclaration) {\n            return;\n        }\n        currentElement[options.declarationKey] = {};\n        if (Object.keys(attributes).length) {\n            currentElement[options.declarationKey][options.attributesKey] = attributes;\n        }\n        if (options.addParent) {\n            currentElement[options.declarationKey][options.parentKey] = currentElement;\n        }\n    } else {\n        if (options.ignoreInstruction) {\n            return;\n        }\n        if (options.trim) {\n            instruction.body = instruction.body.trim();\n        }\n        var value = {};\n        if (options.instructionHasAttributes && Object.keys(attributes).length) {\n            value[instruction.name] = {};\n            value[instruction.name][options.attributesKey] = attributes;\n        } else {\n            value[instruction.name] = instruction.body;\n        }\n        addField(\"instruction\", value);\n    }\n}\nfunction onStartElement(name, attributes) {\n    var element;\n    if (typeof name === \"object\") {\n        attributes = name.attributes;\n        name = name.name;\n    }\n    attributes = manipulateAttributes(attributes);\n    if (\"elementNameFn\" in options) {\n        name = options.elementNameFn(name, currentElement);\n    }\n    if (options.compact) {\n        element = {};\n        if (!options.ignoreAttributes && attributes && Object.keys(attributes).length) {\n            element[options.attributesKey] = {};\n            var key;\n            for(key in attributes){\n                if (attributes.hasOwnProperty(key)) {\n                    element[options.attributesKey][key] = attributes[key];\n                }\n            }\n        }\n        if (!(name in currentElement) && (isArray(options.alwaysArray) ? options.alwaysArray.indexOf(name) !== -1 : options.alwaysArray)) {\n            currentElement[name] = [];\n        }\n        if (currentElement[name] && !isArray(currentElement[name])) {\n            currentElement[name] = [\n                currentElement[name]\n            ];\n        }\n        if (isArray(currentElement[name])) {\n            currentElement[name].push(element);\n        } else {\n            currentElement[name] = element;\n        }\n    } else {\n        if (!currentElement[options.elementsKey]) {\n            currentElement[options.elementsKey] = [];\n        }\n        element = {};\n        element[options.typeKey] = \"element\";\n        element[options.nameKey] = name;\n        if (!options.ignoreAttributes && attributes && Object.keys(attributes).length) {\n            element[options.attributesKey] = attributes;\n        }\n        if (options.alwaysChildren) {\n            element[options.elementsKey] = [];\n        }\n        currentElement[options.elementsKey].push(element);\n    }\n    element[options.parentKey] = currentElement; // will be deleted in onEndElement() if !options.addParent\n    currentElement = element;\n}\nfunction onText(text) {\n    if (options.ignoreText) {\n        return;\n    }\n    if (!text.trim() && !options.captureSpacesBetweenElements) {\n        return;\n    }\n    if (options.trim) {\n        text = text.trim();\n    }\n    if (options.nativeType) {\n        text = nativeType(text);\n    }\n    if (options.sanitize) {\n        text = text.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    addField(\"text\", text);\n}\nfunction onComment(comment) {\n    if (options.ignoreComment) {\n        return;\n    }\n    if (options.trim) {\n        comment = comment.trim();\n    }\n    addField(\"comment\", comment);\n}\nfunction onEndElement(name) {\n    var parentElement = currentElement[options.parentKey];\n    if (!options.addParent) {\n        delete currentElement[options.parentKey];\n    }\n    currentElement = parentElement;\n}\nfunction onCdata(cdata) {\n    if (options.ignoreCdata) {\n        return;\n    }\n    if (options.trim) {\n        cdata = cdata.trim();\n    }\n    addField(\"cdata\", cdata);\n}\nfunction onDoctype(doctype) {\n    if (options.ignoreDoctype) {\n        return;\n    }\n    doctype = doctype.replace(/^ /, \"\");\n    if (options.trim) {\n        doctype = doctype.trim();\n    }\n    addField(\"doctype\", doctype);\n}\nfunction onError(error) {\n    error.note = error; //console.error(error);\n}\nmodule.exports = function(xml, userOptions) {\n    var parser = pureJsParser ? sax.parser(true, {}) : parser = new expat.Parser(\"UTF-8\");\n    var result = {};\n    currentElement = result;\n    options = validateOptions(userOptions);\n    if (pureJsParser) {\n        parser.opt = {\n            strictEntities: true\n        };\n        parser.onopentag = onStartElement;\n        parser.ontext = onText;\n        parser.oncomment = onComment;\n        parser.onclosetag = onEndElement;\n        parser.onerror = onError;\n        parser.oncdata = onCdata;\n        parser.ondoctype = onDoctype;\n        parser.onprocessinginstruction = onInstruction;\n    } else {\n        parser.on(\"startElement\", onStartElement);\n        parser.on(\"text\", onText);\n        parser.on(\"comment\", onComment);\n        parser.on(\"endElement\", onEndElement);\n        parser.on(\"error\", onError);\n    //parser.on('startCdata', onStartCdata);\n    //parser.on('endCdata', onEndCdata);\n    //parser.on('entityDecl', onEntityDecl);\n    }\n    if (pureJsParser) {\n        parser.write(xml).close();\n    } else {\n        if (!parser.parse(xml)) {\n            throw new Error(\"XML parsing error: \" + parser.getError());\n        }\n    }\n    if (result[options.elementsKey]) {\n        var temp = result[options.elementsKey];\n        delete result[options.elementsKey];\n        result[options.elementsKey] = temp;\n        delete result.text;\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xml-js/lib/xml2js.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xml-js/lib/xml2json.js":
/*!*********************************************!*\
  !*** ./node_modules/xml-js/lib/xml2json.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar helper = __webpack_require__(/*! ./options-helper */ \"(rsc)/./node_modules/xml-js/lib/options-helper.js\");\nvar xml2js = __webpack_require__(/*! ./xml2js */ \"(rsc)/./node_modules/xml-js/lib/xml2js.js\");\nfunction validateOptions(userOptions) {\n    var options = helper.copyOptions(userOptions);\n    helper.ensureSpacesExists(options);\n    return options;\n}\nmodule.exports = function(xml, userOptions) {\n    var options, js, json, parentKey;\n    options = validateOptions(userOptions);\n    js = xml2js(xml, options);\n    parentKey = \"compact\" in options && options.compact ? \"_parent\" : \"parent\";\n    // parentKey = ptions.compact ? '_parent' : 'parent'; // consider this\n    if (\"addParent\" in options && options.addParent) {\n        json = JSON.stringify(js, function(k, v) {\n            return k === parentKey ? \"_\" : v;\n        }, options.spaces);\n    } else {\n        json = JSON.stringify(js, null, options.spaces);\n    }\n    return json.replace(/\\u2028/g, \"\\\\u2028\").replace(/\\u2029/g, \"\\\\u2029\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xml-js/lib/xml2json.js\n");

/***/ })

};
;