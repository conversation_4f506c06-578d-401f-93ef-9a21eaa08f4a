"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_shared_ConvertkitForm_CKForm_js";
exports.ids = ["components_shared_ConvertkitForm_CKForm_js"];
exports.modules = {

/***/ "./components/shared/ConvertkitForm/CKForm.js":
/*!****************************************************!*\
  !*** ./components/shared/ConvertkitForm/CKForm.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CKForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction CKForm({ title, desc, formString }) {\n    if (!formString) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormWrapper, {\n        children: [\n            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"form-title\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\ConvertkitForm\\\\CKForm.js\",\n                lineNumber: 9,\n                columnNumber: 17\n            }, this),\n            desc && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"form-desc\",\n                children: desc\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\ConvertkitForm\\\\CKForm.js\",\n                lineNumber: 10,\n                columnNumber: 16\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                dangerouslySetInnerHTML: {\n                    __html: `${formString}`\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\ConvertkitForm\\\\CKForm.js\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\rep\\\\TPSG\\\\tpsg-next\\\\components\\\\shared\\\\ConvertkitForm\\\\CKForm.js\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\nconst FormWrapper = styled_components__WEBPACK_IMPORTED_MODULE_1___default().div.withConfig({\n    displayName: \"CKForm__FormWrapper\",\n    componentId: \"sc-4ad002d4-0\"\n})`\r\n  position: relative;\r\n  width: 100%;\r\n  .form-title {\r\n    font-size: 42px;\r\n    margin-top: 0;\r\n    margin-bottom: 16px;\r\n  }\r\n  .form-desc {\r\n    margin-top: 24px;\r\n    margin-bottom: 24px;\r\n    font-weight: 400;\r\n    color: #161616;\r\n    font-size: 22px;\r\n  }\r\n  .formkit-input {\r\n    margin-bottom: 16px;\r\n    background-color: #F0F0F0 !important;\r\n    color: #161616 !important;\r\n    border-radius: 0 !important;\r\n    border: none !important;\r\n    width: 100% !important;\r\n    height: 52px;\r\n    font-size: 18px !important;\r\n    padding-left: 20px !important;\r\n    &::placeholder {\r\n      color: #888888 !important;\r\n    }\r\n  }\r\n  .formkit-submit {\r\n    margin-bottom: 16px;\r\n    color: white !important;\r\n    border: none !important;\r\n    border-radius: 0 !important;\r\n    background-color: #080808 !important;\r\n    width: 100%;\r\n    height: 52px;\r\n    font-size: 18px !important;\r\n    cursor: pointer;\r\n    &:hover {\r\n      background-color: var(--brand-color) !important;\r\n    }\r\n  }\r\n  div {\r\n    padding: 0 !important;\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/shared/ConvertkitForm/CKForm.js\n");

/***/ })

};
;