"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[915],{8436:function(n,t,e){e.d(t,{Z:function(){return DefaultPostCard}});var i=e(2729),r=e(5893),a=e(9521),o=e(7421),l=e(5675),c=e.n(l),s=e(1664),d=e.n(s),p=e(4218),f=e(4871);e(279),e(1304);var m=e(9588),u=e(4440),h=e(3071),x=e(5158);function _templateObject(){let n=(0,i._)(['\n  position: relative;\n  display: flex;\n  flex-direction: row-reverse;\n  margin-bottom: 32px;\n  padding-bottom: 32px;\n  border-bottom: 1px solid #dcdcdc;\n\n  .dc-text-container {\n    width: 66%;\n    margin-top: 0;\n    .dc-author {\n      font-family: "Lora", serif;\n      font-style: italic;\n      font-size: 14px;\n      color: #888888;\n      margin-top: 0;\n      margin-bottom: 0;\n    }\n\n    .dc-date {\n      font-size: 14px;\n      color: #888888;\n    }\n\n    .dc-title {\n      margin-top: 8px;\n      margin-bottom: 0;\n      margin-right: 16px;\n      font-size: 22px;\n      line-height: 120%;\n      color: #161616;\n      display: -webkit-box;\n      -webkit-line-clamp: 3;\n      -webkit-box-orient: vertical;\n      overflow: hidden;\n      text-overflow: ellipsis;\n    }\n  }\n\n  .dc-image-container {\n    position: relative;\n    flex-shrink: 0;\n    width: 28vw;\n    //height: 28vw;\n    aspect-ratio: 1/1;\n    //&:after {\n    //  content: "";\n    //  display: block;\n    //  padding-bottom: 100%;\n    //}\n  }\n\n  @media '," {\n    flex-direction: row;\n    border-bottom: 1px solid #ffffff;\n    cursor: pointer;\n    .dc-text-container {\n      //flex-grow: 1;\n      .dc-title {\n        font-size: 32px;\n        margin-top: 12px;\n      }\n      .dc-author {\n        font-size: 16px;\n      }\n    }\n    .dc-image-container {\n      margin-right: 32px;\n      width: 30%;\n      height: 100%;\n      aspect-ratio: 1/1;\n    }\n    .dc-topics {\n      margin-top: 16px;\n      .card-label {\n        margin-right: 8px;\n      }\n    }\n\n  }\n"]);return _templateObject=function(){return n},n}function DefaultPostCard(n){var t,e;let i;let{post:a,options:o,showAnimatedIcon:l=!1}=n,s=(0,f.qt)(a);if(!s)return(0,r.jsx)(r.Fragment,{});a.author&&(i=a.author.fullName?a.author.fullName:a.author),a.date?(0,p.S$)(a.date):(0,p.S$)(a.published_at);let v=(0,h.k)(a.image);return(0,r.jsx)(d(),{href:s,children:(0,r.jsxs)(g,{children:[(0,r.jsxs)("div",{className:"dc-image-container",children:[v&&(0,r.jsx)(c(),{src:v,fill:!0,style:{objectFit:"cover"},sizes:"240px",alt:(null===(t=a.image)||void 0===t?void 0:t.alternativeText)||""}),l&&(0,r.jsx)(m.YM,{type:a.type,colors:(0,u.Q)(a.type)})]}),(0,r.jsxs)("div",{className:"dc-text-container",children:[(0,r.jsx)("p",{className:"dc-author",children:i}),(0,r.jsx)("p",{className:"dc-title primary-hover",children:a.title}),o.showLead&&(0,r.jsx)("p",{className:"dc-lead",children:(0,x.Gq)((0,x.Kd)((null===(e=a.lead)||void 0===e?void 0:e.content)||""))})]})]})})}let g=a.ZP.div.withConfig({componentId:"sc-971df927-0"})(_templateObject(),o.U.tablet)},7915:function(n,t,e){e.d(t,{Zs:function(){return i.Z},F3:function(){return FirstArticleCard},Zo:function(){return SquareVerticalCard},h3:function(){return SquareVerticalFeatured}});var i=e(8436),r=e(2729),a=e(5893),o=e(9521),l=e(4218),c=e(1304),s=e(7421),d=e(1664),p=e.n(d),f=e(4871);function _templateObject(){let n=(0,r._)(['\n  position: relative;\n  overflow: auto;\n  width: 100%;\n\n  &:hover {\n    .image-glow {\n      filter: blur(20px);\n    }\n    cursor: pointer;\n  }\n  \n  .absolute {\n    position: absolute;\n  }\n\n  .sv-card-date {\n    font-family: "Novela", "Lora", sans-serif;\n    font-size: 14px;\n    color: #888888;\n    margin-bottom: 8px;\n    font-weight: 400;\n    margin-top: 0;\n  }\n\n  .sv-card-image {\n    position: relative;\n    aspect-ratio: 1/1;\n    width: 100%;\n    cursor: pointer;\n  }\n\n  .sv-card-author {\n    font-size: 14px;\n    font-family: "Lora", serif;\n    font-style: italic;\n    color: #888888;\n    margin-top: 18px;\n    margin-bottom: 0;\n  }\n\n  .sv-card-title {\n    font-size: 18px;\n    font-weight: 500;\n    line-height: 115%;\n    color: ',";\n  }\n\n  @media "," {\n    .sv-card-title {\n      margin-top: 16px;\n      margin-bottom: auto;\n      font-weight: 500;\n      line-height: 30px;\n      font-size: 27px;\n    }\n\n    .sv-card-date {\n      font-size: 18px;\n    }\n  }\n\n  .image-glow {\n    position: absolute;\n    width: calc(100% - 48px);\n    aspect-ratio: 1/1;\n    filter: blur(10px);\n  }\n"]);return _templateObject=function(){return n},n}function SquareVerticalCard(n){var t;let{post:e,options:i}=n,r=(null==i?void 0:i.showDate)?(0,l.S$)(e.date):null,o=(null==i?void 0:i.showAuthor)?null===(t=e.author)||void 0===t?void 0:t.fullName:null,s=(0,f.qt)(e);return(0,a.jsxs)(m,{className:"sv-card",children:[r&&(0,a.jsx)("p",{className:"sv-card-date",children:(0,l.S$)(e.date)}),(0,a.jsx)(p(),{href:s,children:(0,a.jsx)("div",{className:"sv-card-image",children:(0,a.jsx)(c.Z,{imageData:e.image})})}),o&&(0,a.jsx)("p",{className:"sv-card-author",children:o}),(0,a.jsx)(p(),{href:s,children:(0,a.jsx)("h3",{className:"sv-card-title primary-hover",children:e.title})})]})}let m=o.ZP.div.withConfig({componentId:"sc-9bfcf64e-0"})(_templateObject(),n=>n.featured?"#f4f4f4":"#161616",s.U.tablet);var u=e(9588);function first_article_card_templateObject(){let n=(0,r._)(['\n  width: 100%;\n  grid-area: "FirstArticle";\n  //overflow: auto;\n  //grid-column: 1/3;\n\n  .fa-card-date {\n    font-family: "Novela", "Lora", sans-serif;\n    font-size: 14px;\n    color: #888888;\n    margin-bottom: 8px;\n    margin-top: 0;\n    font-weight: 400;\n  }\n\n  .fa-card-image {\n    position: relative;\n    width: 100%;\n    aspect-ratio: 16/10;\n  }\n\n  .fa-card-title {\n    width: 95%;\n    font-size: 30px;\n    font-weight: 600;\n    margin-top: 18px;\n    line-height: 110%;\n    color: #262626;\n    cursor: pointer;\n  }\n\n  .fa-card-author {\n    font-size: 18px;\n    font-family: "Lora", serif;\n    font-style: italic;\n    color: #888888;\n    margin-top: 24px;\n    margin-bottom: 0;\n  }\n\n  @media '," {\n    //grid-column: 2/4;\n\n    .fa-card-image {\n    }\n\n    .fa-card-title {\n      font-size: 48px;\n      font-weight: 500;\n      line-height: 105%;\n      margin-top: 32px;\n    }\n    .fa-card-date {\n      font-size: 18px;\n    }\n  }\n  @media "," {\n    .fa-card-image {\n    }\n  }\n"]);return first_article_card_templateObject=function(){return n},n}function FirstArticleCard(n){var t;let{post:e,options:i}=n,r=(null==i?void 0:i.showDate)?(0,l.S$)(e.date):null,o=(null==i?void 0:i.showAuthor)?null===(t=e.author)||void 0===t?void 0:t.fullName:null,s=(0,f.qt)(e);return(0,a.jsx)(h,{className:"fa-card",children:(0,a.jsxs)(p(),{href:s,children:[r&&(0,a.jsx)("p",{className:"fa-card-date",children:(0,l.S$)(e.date)}),(0,a.jsxs)("div",{className:"fa-card-image",children:[(0,a.jsx)(c.Z,{imageData:e.image,priority:!0}),(0,a.jsx)(u.YM,{type:e.type,colors:null==i?void 0:i.dotColors})]}),o&&(0,a.jsx)("p",{className:"fa-card-author",children:o}),(0,a.jsx)("h3",{className:"fa-card-title primary-hover",children:e.title})]})})}let h=o.ZP.div.withConfig({componentId:"sc-b1214ea6-0"})(first_article_card_templateObject(),s.U.tablet,s.U.desktop);function slide_card_templateObject(){let n=(0,r._)(["\n  position: relative;\n  display: flex;\n  flex: 0 0 1;\n  justify-content: start;\n  flex-direction: column;\n  min-width: calc(80%);\n  width: calc(100% - 48px);\n  margin-right: 16px;\n  background-color: ",";\n  text-align: center;\n  height: 364px;\n\n  @media "," {\n    min-width: 295px;\n  }\n\n  @media "," {\n    width: 25%;\n    margin-right: 32px\n  }\n  \n  .circle-image {\n    position: relative;\n    flex: 0 1 auto;\n    margin: -25px auto 0 auto;\n    width: 50px;\n    height: 50px;\n    border-radius: 50px;\n    border-style: solid;\n    border-width: 1px;\n    border-color: ",";\n    background-color: ",';\n    z-index: 2;\n    //overflow: hidden;\n    img {\n      border-radius: 50px;\n    }\n    \n    &:before {\n      content: "";\n      position: absolute;\n      top: -7px;\n      left: -7px;\n      width: 50px;\n      height: 50px;\n      border-radius: 50px;\n      border: 6px solid ',';\n    }\n  }\n\n  .card-author {\n    flex: 0 1 auto;\n    font-family: "Lora", serif;\n    font-style: italic;\n    font-size: 16px;\n    margin: 27px 24px 0 24px;\n    color: #888;\n  }\n\n  h3 {\n    flex: 0 1 auto;\n    margin: 12px 24px 0 24px;\n    font-weight: 500;\n    font-size: 21px;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 3;\n    overflow: hidden;\n    color: ',";\n  }\n  \n  .thumbnail {\n    position: relative;\n    flex: 1 1 auto;\n    margin-top: 24px;\n    width: 100%;\n  }\n\n\n\n\n"]);return slide_card_templateObject=function(){return n},n}o.ZP.div.withConfig({componentId:"sc-6e3eb749-0"})(slide_card_templateObject(),n=>n.isDark?"#242424":"#f4f4f4",s.U.tablet,s.U.desktop,n=>n.isDark?"#888888":"white",n=>n.isDark?"#242424":"white",n=>n.isDark?"#080808":"white",n=>n.isDark?"#dcdcdc":"#161616");var x=e(1261);function SquareVerticalFeatured_templateObject(){let n=(0,r._)(["\n  position: relative;\n  width: 100%;\n  cursor: pointer;\n"]);return SquareVerticalFeatured_templateObject=function(){return n},n}function _templateObject1(){let n=(0,r._)(["\n  background-color: #161616;\n  padding: 24px;\n  box-shadow: 0 7px 14px 3px rgba(0, 0, 0, 0.29);\n  \n  .svf-image {\n    position: relative;\n    width: 100%;\n    aspect-ratio: 1/1;\n  }\n  .svf-blurred-image {\n    position: relative;\n  }\n  .svf-card-title {\n    font-size: 18px;\n    font-weight: 500;\n    line-height: 115%;\n    color: #f4f4f4;\n  }\n  @media "," {\n    .svf-card-title {\n      \n    }\n  }\n"]);return _templateObject1=function(){return n},n}function SquareVerticalFeatured(n){var t;let{item:e}=n;return(null===(t=e.cta)||void 0===t?void 0:t.url)?(0,a.jsx)(g,{className:"svf-card",children:(0,a.jsx)(x.Z,{link:e.cta.url,children:(0,a.jsxs)(v,{children:[(0,a.jsx)("div",{className:"svf-hidden-image",children:(0,a.jsx)("div",{className:"svf-blurred-image",children:(0,a.jsx)(c.Z,{imageData:e.image})})}),(0,a.jsx)("div",{className:"svf-image",children:(0,a.jsx)(c.Z,{imageData:e.image})}),(0,a.jsx)("h3",{className:"svf-card-title",children:e.title})]})})}):(0,a.jsx)(a.Fragment,{})}let g=o.ZP.div.withConfig({componentId:"sc-24c25690-0"})(SquareVerticalFeatured_templateObject()),v=o.ZP.div.withConfig({componentId:"sc-24c25690-1"})(_templateObject1(),s.U.desktop)},279:function(n,t,e){var i=e(2729),r=e(5893),a=e(9521),o=e(5158),l=e(1664),c=e.n(l);function _templateObject(){let n=(0,i._)(['\n  position: relative;\n  font-family: Switzer, "Helvetica Neue", Helvetica, "Arial", sans-serif;\n  padding: 5px 11px 5px 11px;\n  font-size: 11px;\n  letter-spacing: 0.2px;\n  border-radius: 100px;\n  text-transform: uppercase;\n  white-space: nowrap;\n  margin-bottom: 8px;\n\n  border: 1px solid #464646;\n  background-color: transparent;\n  color: #464646;\n\n  &:hover {\n    background-color: #242424;\n    color: #f4f4f4;\n  }\n\n  cursor: pointer;\n  ']);return _templateObject=function(){return n},n}t.Z=n=>{let{text:t}=n,e="/categories/"+(0,o.lV)(t);return(0,r.jsx)(c(),{href:e,legacyBehavior:!0,children:(0,r.jsx)(s,{className:"card-label",children:t})})};let s=a.ZP.span.withConfig({componentId:"sc-e62d7975-0"})(_templateObject())}}]);