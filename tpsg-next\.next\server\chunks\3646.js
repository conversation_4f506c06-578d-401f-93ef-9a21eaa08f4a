"use strict";exports.id=3646,exports.ids=[3646],exports.modules={3646:(e,o,n)=>{n.a(e,async(e,r)=>{try{n.d(o,{Z:()=>AuthorBox});var t=n(997),i=n(7518),a=n.n(i),l=n(3135),s=n(1871),d=n(2558),u=n(7467);n(1077),n(1664);var c=e([l,s]);[l,s]=c.then?(await c)():c;let linkComponent=({href:e,children:o,...n})=>t.jsx("a",{href:e,...n,children:o}),underlineComponent=({children:e})=>t.jsx("u",{children:e});function AuthorBox({author:e,blog:o}){if(!e)return null;e.about=e.about?.replace(/\\/g,"")||"";let n=o?`/blog/${o.blogger?.slug}`:`/recherche?author=${e.fullName}`,r=`${e.firstName||""} ${e.lastName||""}`.trim();return(0,t.jsxs)(p,{className:"author-box",children:[t.jsx(h,{children:t.jsx("a",{href:n,children:t.jsx(d.Z,{imageData:e.picture})})}),(0,t.jsxs)(x,{children:[t.jsx("p",{className:"abox-about__name",children:t.jsx("a",{href:n,children:r})}),t.jsx(l.default,{rehypePlugins:[s.default],components:{a:linkComponent,u:underlineComponent},children:e.about})]})]})}let p=a().div.withConfig({componentId:"sc-6ce06466-0"})`
  position: relative;
  display: flex;
  flex-direction: column;
  border-top: 1px solid rgba(0, 0, 0, 0.4);
  padding-top: 24px;

  @media ${u.U.tablet} {
    flex-direction: row;
    padding: 32px 0 0 0;
  }
`,h=a().div.withConfig({componentId:"sc-6ce06466-1"})`
  position: relative;
  flex-shrink: 0;
  margin: 0 32px 12px 0;
  height: 60px;
  width: 60px;
  border-radius: 60px;
  overflow: hidden;
  background-color: white;
`,x=a().div.withConfig({componentId:"sc-6ce06466-2"})`
  .abox-about__name {
    font-size: 26px;
    font-weight: 600;
  }
  p {
    font-size: 22px;
    line-height: 28px;
    font-weight: 400;
    margin: 12px 0 0 0;
  }

  a {
    color: var(--brand-color);
    text-decoration: none;
  }

  a:hover {
    color: var(--brand-color);
    text-decoration: underline;
  }

  /* Spécificité renforcée pour les balises u */
  u {
    text-decoration: underline !important;
  }

  /* Styles pour les balises u à l'intérieur des liens - TOUTES les combinaisons */
  a u,
  a em u,
  a strong u,
  a span u,
  a i u,
  a b u {
    text-decoration: none !important;
  }
  a:hover u,
  a:hover em u,
  a:hover strong u,
  a:hover span u,
  a:hover i u,
  a:hover b u {
    text-decoration: underline !important;
  }
`;r()}catch(e){r(e)}})}};