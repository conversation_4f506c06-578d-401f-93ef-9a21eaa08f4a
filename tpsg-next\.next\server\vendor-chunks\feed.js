"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/feed";
exports.ids = ["vendor-chunks/feed"];
exports.modules = {

/***/ "(rsc)/./node_modules/feed/lib/atom1.js":
/*!****************************************!*\
  !*** ./node_modules/feed/lib/atom1.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar convert = __webpack_require__(/*! xml-js */ \"(rsc)/./node_modules/xml-js/lib/index.js\");\nvar config_1 = __webpack_require__(/*! ./config */ \"(rsc)/./node_modules/feed/lib/config/index.js\");\nvar utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/feed/lib/utils.js\");\nexports[\"default\"] = function(ins) {\n    var options = ins.options;\n    var base = {\n        _declaration: {\n            _attributes: {\n                version: \"1.0\",\n                encoding: \"utf-8\"\n            }\n        },\n        feed: {\n            _attributes: {\n                xmlns: \"http://www.w3.org/2005/Atom\"\n            },\n            id: options.id,\n            title: options.title,\n            updated: options.updated ? options.updated.toISOString() : new Date().toISOString(),\n            generator: utils_1.sanitize(options.generator || config_1.generator)\n        }\n    };\n    if (options.author) {\n        base.feed.author = formatAuthor(options.author);\n    }\n    base.feed.link = [];\n    if (options.link) {\n        base.feed.link.push({\n            _attributes: {\n                rel: \"alternate\",\n                href: utils_1.sanitize(options.link)\n            }\n        });\n    }\n    var atomLink = utils_1.sanitize(options.feed || options.feedLinks && options.feedLinks.atom);\n    if (atomLink) {\n        base.feed.link.push({\n            _attributes: {\n                rel: \"self\",\n                href: utils_1.sanitize(atomLink)\n            }\n        });\n    }\n    if (options.hub) {\n        base.feed.link.push({\n            _attributes: {\n                rel: \"hub\",\n                href: utils_1.sanitize(options.hub)\n            }\n        });\n    }\n    if (options.description) {\n        base.feed.subtitle = options.description;\n    }\n    if (options.image) {\n        base.feed.logo = options.image;\n    }\n    if (options.favicon) {\n        base.feed.icon = options.favicon;\n    }\n    if (options.copyright) {\n        base.feed.rights = options.copyright;\n    }\n    base.feed.category = [];\n    ins.categories.map(function(category) {\n        base.feed.category.push({\n            _attributes: {\n                term: category\n            }\n        });\n    });\n    base.feed.contributor = [];\n    ins.contributors.map(function(contributor) {\n        base.feed.contributor.push(formatAuthor(contributor));\n    });\n    base.feed.entry = [];\n    ins.items.map(function(item) {\n        var entry = {\n            title: {\n                _attributes: {\n                    type: \"html\"\n                },\n                _cdata: item.title\n            },\n            id: utils_1.sanitize(item.id || item.link),\n            link: [\n                {\n                    _attributes: {\n                        href: utils_1.sanitize(item.link)\n                    }\n                }\n            ],\n            updated: item.date.toISOString()\n        };\n        if (item.description) {\n            entry.summary = {\n                _attributes: {\n                    type: \"html\"\n                },\n                _cdata: item.description\n            };\n        }\n        if (item.content) {\n            entry.content = {\n                _attributes: {\n                    type: \"html\"\n                },\n                _cdata: item.content\n            };\n        }\n        if (Array.isArray(item.author)) {\n            entry.author = [];\n            item.author.map(function(author) {\n                entry.author.push(formatAuthor(author));\n            });\n        }\n        if (Array.isArray(item.category)) {\n            entry.category = [];\n            item.category.map(function(category) {\n                entry.category.push(formatCategory(category));\n            });\n        }\n        if (item.contributor && Array.isArray(item.contributor)) {\n            entry.contributor = [];\n            item.contributor.map(function(contributor) {\n                entry.contributor.push(formatAuthor(contributor));\n            });\n        }\n        if (item.published) {\n            entry.published = item.published.toISOString();\n        }\n        if (item.copyright) {\n            entry.rights = item.copyright;\n        }\n        base.feed.entry.push(entry);\n    });\n    return convert.js2xml(base, {\n        compact: true,\n        ignoreComment: true,\n        spaces: 4\n    });\n};\nvar formatAuthor = function(author) {\n    var name = author.name, email = author.email, link = author.link;\n    var out = {\n        name: name\n    };\n    if (email) {\n        out.email = email;\n    }\n    if (link) {\n        out.uri = utils_1.sanitize(link);\n    }\n    return out;\n};\nvar formatCategory = function(category) {\n    var name = category.name, scheme = category.scheme, term = category.term;\n    return {\n        _attributes: {\n            label: name,\n            scheme: scheme,\n            term: term\n        }\n    };\n}; //# sourceMappingURL=atom1.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/feed/lib/atom1.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/feed/lib/config/index.js":
/*!***********************************************!*\
  !*** ./node_modules/feed/lib/config/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.generator = void 0;\nexports.generator = \"https://github.com/jpmonette/feed\"; //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmVlZC9saWIvY29uZmlnL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2JBLDhDQUE2QztJQUFFRyxPQUFPO0FBQUssQ0FBQyxFQUFDO0FBQzdERCxpQkFBaUIsR0FBRyxLQUFLO0FBQ3pCQSxpQkFBaUIsR0FBRyxxQ0FDcEIsaUNBQWlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHBzZy1uZXh0Ly4vbm9kZV9tb2R1bGVzL2ZlZWQvbGliL2NvbmZpZy9pbmRleC5qcz9hNzhiIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5nZW5lcmF0b3IgPSB2b2lkIDA7XG5leHBvcnRzLmdlbmVyYXRvciA9IFwiaHR0cHM6Ly9naXRodWIuY29tL2pwbW9uZXR0ZS9mZWVkXCI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJnZW5lcmF0b3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/feed/lib/config/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/feed/lib/feed.js":
/*!***************************************!*\
  !*** ./node_modules/feed/lib/feed.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Feed = void 0;\nvar atom1_1 = __webpack_require__(/*! ./atom1 */ \"(rsc)/./node_modules/feed/lib/atom1.js\");\nvar json_1 = __webpack_require__(/*! ./json */ \"(rsc)/./node_modules/feed/lib/json.js\");\nvar rss2_1 = __webpack_require__(/*! ./rss2 */ \"(rsc)/./node_modules/feed/lib/rss2.js\");\nvar Feed = function() {\n    function Feed(options) {\n        var _this = this;\n        this.items = [];\n        this.categories = [];\n        this.contributors = [];\n        this.extensions = [];\n        this.addItem = function(item) {\n            return _this.items.push(item);\n        };\n        this.addCategory = function(category) {\n            return _this.categories.push(category);\n        };\n        this.addContributor = function(contributor) {\n            return _this.contributors.push(contributor);\n        };\n        this.addExtension = function(extension) {\n            return _this.extensions.push(extension);\n        };\n        this.atom1 = function() {\n            return atom1_1.default(_this);\n        };\n        this.rss2 = function() {\n            return rss2_1.default(_this);\n        };\n        this.json1 = function() {\n            return json_1.default(_this);\n        };\n        this.options = options;\n    }\n    return Feed;\n}();\nexports.Feed = Feed; //# sourceMappingURL=feed.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/feed/lib/feed.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/feed/lib/json.js":
/*!***************************************!*\
  !*** ./node_modules/feed/lib/json.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = function(ins) {\n    var options = ins.options, items = ins.items, extensions = ins.extensions;\n    var feed = {\n        version: \"https://jsonfeed.org/version/1\",\n        title: options.title\n    };\n    if (options.link) {\n        feed.home_page_url = options.link;\n    }\n    if (options.feedLinks && options.feedLinks.json) {\n        feed.feed_url = options.feedLinks.json;\n    }\n    if (options.description) {\n        feed.description = options.description;\n    }\n    if (options.image) {\n        feed.icon = options.image;\n    }\n    if (options.author) {\n        feed.author = {};\n        if (options.author.name) {\n            feed.author.name = options.author.name;\n        }\n        if (options.author.link) {\n            feed.author.url = options.author.link;\n        }\n    }\n    extensions.map(function(e) {\n        feed[e.name] = e.objects;\n    });\n    feed.items = items.map(function(item) {\n        var feedItem = {\n            id: item.id,\n            content_html: item.content\n        };\n        if (item.link) {\n            feedItem.url = item.link;\n        }\n        if (item.title) {\n            feedItem.title = item.title;\n        }\n        if (item.description) {\n            feedItem.summary = item.description;\n        }\n        if (item.image) {\n            feedItem.image = item.image;\n        }\n        if (item.date) {\n            feedItem.date_modified = item.date.toISOString();\n        }\n        if (item.published) {\n            feedItem.date_published = item.published.toISOString();\n        }\n        if (item.author) {\n            var author = item.author;\n            if (author instanceof Array) {\n                author = author[0];\n            }\n            feedItem.author = {};\n            if (author.name) {\n                feedItem.author.name = author.name;\n            }\n            if (author.link) {\n                feedItem.author.url = author.link;\n            }\n        }\n        if (Array.isArray(item.category)) {\n            feedItem.tags = [];\n            item.category.map(function(category) {\n                if (category.name) {\n                    feedItem.tags.push(category.name);\n                }\n            });\n        }\n        if (item.extensions) {\n            item.extensions.map(function(e) {\n                feedItem[e.name] = e.objects;\n            });\n        }\n        return feedItem;\n    });\n    return JSON.stringify(feed, null, 4);\n}; //# sourceMappingURL=json.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/feed/lib/json.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/feed/lib/rss2.js":
/*!***************************************!*\
  !*** ./node_modules/feed/lib/rss2.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __assign = (void 0) && (void 0).__assign || function() {\n    __assign = Object.assign || function(t) {\n        for(var s, i = 1, n = arguments.length; i < n; i++){\n            s = arguments[i];\n            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar convert = __webpack_require__(/*! xml-js */ \"(rsc)/./node_modules/xml-js/lib/index.js\");\nvar config_1 = __webpack_require__(/*! ./config */ \"(rsc)/./node_modules/feed/lib/config/index.js\");\nvar utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/feed/lib/utils.js\");\nexports[\"default\"] = function(ins) {\n    var options = ins.options;\n    var isAtom = false;\n    var isContent = false;\n    var base = {\n        _declaration: {\n            _attributes: {\n                version: \"1.0\",\n                encoding: \"utf-8\"\n            }\n        },\n        rss: {\n            _attributes: {\n                version: \"2.0\"\n            },\n            channel: {\n                title: {\n                    _text: options.title\n                },\n                link: {\n                    _text: utils_1.sanitize(options.link)\n                },\n                description: {\n                    _text: options.description\n                },\n                lastBuildDate: {\n                    _text: options.updated ? options.updated.toUTCString() : new Date().toUTCString()\n                },\n                docs: {\n                    _text: options.docs ? options.docs : \"https://validator.w3.org/feed/docs/rss2.html\"\n                },\n                generator: {\n                    _text: options.generator || config_1.generator\n                }\n            }\n        }\n    };\n    if (options.language) {\n        base.rss.channel.language = {\n            _text: options.language\n        };\n    }\n    if (options.ttl) {\n        base.rss.channel.ttl = {\n            _text: options.ttl\n        };\n    }\n    if (options.image) {\n        base.rss.channel.image = {\n            title: {\n                _text: options.title\n            },\n            url: {\n                _text: options.image\n            },\n            link: {\n                _text: utils_1.sanitize(options.link)\n            }\n        };\n    }\n    if (options.copyright) {\n        base.rss.channel.copyright = {\n            _text: options.copyright\n        };\n    }\n    ins.categories.map(function(category) {\n        if (!base.rss.channel.category) {\n            base.rss.channel.category = [];\n        }\n        base.rss.channel.category.push({\n            _text: category\n        });\n    });\n    var atomLink = options.feed || options.feedLinks && options.feedLinks.rss;\n    if (atomLink) {\n        isAtom = true;\n        base.rss.channel[\"atom:link\"] = [\n            {\n                _attributes: {\n                    href: utils_1.sanitize(atomLink),\n                    rel: \"self\",\n                    type: \"application/rss+xml\"\n                }\n            }\n        ];\n    }\n    if (options.hub) {\n        isAtom = true;\n        if (!base.rss.channel[\"atom:link\"]) {\n            base.rss.channel[\"atom:link\"] = [];\n        }\n        base.rss.channel[\"atom:link\"] = {\n            _attributes: {\n                href: utils_1.sanitize(options.hub),\n                rel: \"hub\"\n            }\n        };\n    }\n    base.rss.channel.item = [];\n    ins.items.map(function(entry) {\n        var item = {};\n        if (entry.title) {\n            item.title = {\n                _cdata: entry.title\n            };\n        }\n        if (entry.link) {\n            item.link = {\n                _text: utils_1.sanitize(entry.link)\n            };\n        }\n        if (entry.guid) {\n            item.guid = {\n                _text: entry.guid\n            };\n        } else if (entry.id) {\n            item.guid = {\n                _text: entry.id\n            };\n        } else if (entry.link) {\n            item.guid = {\n                _text: utils_1.sanitize(entry.link)\n            };\n        }\n        if (entry.date) {\n            item.pubDate = {\n                _text: entry.date.toUTCString()\n            };\n        }\n        if (entry.published) {\n            item.pubDate = {\n                _text: entry.published.toUTCString()\n            };\n        }\n        if (entry.description) {\n            item.description = {\n                _cdata: entry.description\n            };\n        }\n        if (entry.content) {\n            isContent = true;\n            item[\"content:encoded\"] = {\n                _cdata: entry.content\n            };\n        }\n        if (Array.isArray(entry.author)) {\n            item.author = [];\n            entry.author.map(function(author) {\n                if (author.email && author.name) {\n                    item.author.push({\n                        _text: author.email + \" (\" + author.name + \")\"\n                    });\n                }\n            });\n        }\n        if (Array.isArray(entry.category)) {\n            item.category = [];\n            entry.category.map(function(category) {\n                item.category.push(formatCategory(category));\n            });\n        }\n        if (entry.enclosure) {\n            item.enclosure = formatEnclosure(entry.enclosure);\n        }\n        if (entry.image) {\n            item.enclosure = formatEnclosure(entry.image, \"image\");\n        }\n        if (entry.audio) {\n            item.enclosure = formatEnclosure(entry.audio, \"audio\");\n        }\n        if (entry.video) {\n            item.enclosure = formatEnclosure(entry.video, \"video\");\n        }\n        base.rss.channel.item.push(item);\n    });\n    if (isContent) {\n        base.rss._attributes[\"xmlns:dc\"] = \"http://purl.org/dc/elements/1.1/\";\n        base.rss._attributes[\"xmlns:content\"] = \"http://purl.org/rss/1.0/modules/content/\";\n    }\n    if (isAtom) {\n        base.rss._attributes[\"xmlns:atom\"] = \"http://www.w3.org/2005/Atom\";\n    }\n    return convert.js2xml(base, {\n        compact: true,\n        ignoreComment: true,\n        spaces: 4\n    });\n};\nvar formatEnclosure = function(enclosure, mimeCategory) {\n    if (mimeCategory === void 0) {\n        mimeCategory = \"image\";\n    }\n    if (typeof enclosure === \"string\") {\n        var type_1 = new URL(enclosure).pathname.split(\".\").slice(-1)[0];\n        return {\n            _attributes: {\n                url: enclosure,\n                length: 0,\n                type: mimeCategory + \"/\" + type_1\n            }\n        };\n    }\n    var type = new URL(enclosure.url).pathname.split(\".\").slice(-1)[0];\n    return {\n        _attributes: __assign({\n            length: 0,\n            type: mimeCategory + \"/\" + type\n        }, enclosure)\n    };\n};\nvar formatCategory = function(category) {\n    var name = category.name, domain = category.domain;\n    return {\n        _text: name,\n        _attributes: {\n            domain: domain\n        }\n    };\n}; //# sourceMappingURL=rss2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/feed/lib/rss2.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/feed/lib/utils.js":
/*!****************************************!*\
  !*** ./node_modules/feed/lib/utils.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.sanitize = void 0;\nfunction sanitize(url) {\n    if (typeof url === \"undefined\") {\n        return;\n    }\n    return url.replace(/&/g, \"&amp;\");\n}\nexports.sanitize = sanitize; //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmVlZC9saWIvdXRpbHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYkEsOENBQTZDO0lBQUVHLE9BQU87QUFBSyxDQUFDLEVBQUM7QUFDN0RELGdCQUFnQixHQUFHLEtBQUs7QUFDeEIsU0FBU0UsU0FBU0MsR0FBRztJQUNqQixJQUFJLE9BQVFBLFFBQVMsYUFBYTtRQUM5QjtJQUNKO0lBQ0EsT0FBT0EsSUFBSUMsT0FBTyxDQUFDLE1BQU07QUFDN0I7QUFDQUosZ0JBQWdCLEdBQUdFLFVBQ25CLGlDQUFpQyIsInNvdXJjZXMiOlsid2VicGFjazovL3Rwc2ctbmV4dC8uL25vZGVfbW9kdWxlcy9mZWVkL2xpYi91dGlscy5qcz8zNmZlIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5zYW5pdGl6ZSA9IHZvaWQgMDtcbmZ1bmN0aW9uIHNhbml0aXplKHVybCkge1xuICAgIGlmICh0eXBlb2YgKHVybCkgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIHJldHVybjtcbiAgICB9XG4gICAgcmV0dXJuIHVybC5yZXBsYWNlKC8mL2csICcmYW1wOycpO1xufVxuZXhwb3J0cy5zYW5pdGl6ZSA9IHNhbml0aXplO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbHMuanMubWFwIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwic2FuaXRpemUiLCJ1cmwiLCJyZXBsYWNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/feed/lib/utils.js\n");

/***/ })

};
;