{"kind": "FETCH", "data": {"headers": {"connection": "keep-alive", "content-length": "61609", "content-type": "application/json", "date": "Wed, 28 May 2025 09:53:58 GMT", "keep-alive": "timeout=5", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Origin", "x-frame-options": "SAMEORIGIN", "x-powered-by": "Strapi <strapi.io>", "x-response-time": "658ms"}, "body": "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", "status": 200, "url": "http://127.0.0.1:1337/graphql"}, "revalidate": 3600, "tags": []}