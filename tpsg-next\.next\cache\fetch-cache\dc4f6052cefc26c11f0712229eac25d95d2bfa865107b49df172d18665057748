{"kind": "FETCH", "data": {"headers": {"connection": "keep-alive", "content-length": "43753", "content-type": "application/json", "date": "Wed, 28 May 2025 11:34:02 GMT", "keep-alive": "timeout=5", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Origin", "x-frame-options": "SAMEORIGIN", "x-powered-by": "Strapi <strapi.io>", "x-response-time": "854ms"}, "body": "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", "status": 200, "url": "http://127.0.0.1:1337/graphql"}, "revalidate": 3600, "tags": []}