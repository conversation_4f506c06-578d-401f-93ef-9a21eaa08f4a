"use strict";(()=>{var e={};e.id=5405,e.ids=[5405,2888],e.modules={1725:(e,t,i)=>{i.a(e,async(e,o)=>{try{i.r(t),i.d(t,{config:()=>g,default:()=>p,getServerSideProps:()=>x,getStaticPaths:()=>h,getStaticProps:()=>m,reportWebVitals:()=>f,routeModule:()=>k,unstable_getServerProps:()=>w,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>u});var r=i(7093),a=i(5244),s=i(1323),n=i(779),l=i(4033),d=i(2346),c=e([d]);d=(c.then?(await c)():c)[0];let p=(0,s.l)(d,"default"),m=(0,s.l)(d,"getStaticProps"),h=(0,s.l)(d,"getStaticPaths"),x=(0,s.l)(d,"getServerSideProps"),g=(0,s.l)(d,"config"),f=(0,s.l)(d,"reportWebVitals"),u=(0,s.l)(d,"unstable_getStaticProps"),v=(0,s.l)(d,"unstable_getStaticPaths"),b=(0,s.l)(d,"unstable_getStaticParams"),w=(0,s.l)(d,"unstable_getServerProps"),j=(0,s.l)(d,"unstable_getServerSideProps"),k=new r.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/index",pathname:"/",bundlePath:"",filename:""},components:{App:l.default,Document:n.default},userland:d});o()}catch(e){o(e)}})},9182:(e,t,i)=>{i.d(t,{ZJ:()=>SectionArticles,lm:()=>SectionBloggers,bh:()=>SectionDouble,$U:()=>SectionMission,dx:()=>SectionMostRead,Qz:()=>SectionPodcasts,Fn:()=>SectionTopics});var o=i(997),r=i(7518),a=i.n(r),s=i(7467),n=i(5675),l=i.n(n),d=i(4130);i(8672);var c=i(635),p=i(2832),m=i(1077),h=i(1664),x=i.n(h),g=i(6382),f=i(73),u=i(7199);function SectionArticles({data:e}){let{posts:t}=e,{lead:i}=(0,c.fw)(t[0].modules),r=t[0].image&&(0,d.k)(t[0].image);return t.length?(0,o.jsxs)(b,{children:[(0,o.jsxs)(w,{children:[o.jsx(x(),{href:(0,m.qt)(t[0]),children:o.jsx("div",{className:"fp-cover",children:r&&o.jsx(l(),{src:r,fill:!0,priority:!0,style:v.fpImage,sizes:"80vw",alt:r?.alternativeText||""})})}),o.jsx("p",{className:"fp-author",children:t[0].author.fullName}),o.jsx(x(),{href:(0,m.qt)(t[0]),children:o.jsx("h3",{className:"fp-title primary-hover",children:t[0].title})}),i&&o.jsx("p",{className:"fp-lead",children:(0,u.Gq)((0,u.Kd)(i.content))})]}),(0,o.jsxs)(j,{children:[t.slice(1,10).map((e,t)=>o.jsx(p.Zs,{post:e,options:{showLead:!1,showDate:!1,showTopics:!0}},t)),o.jsx(f.Ty,{text:"Tous nos articles",link:"/recherche"})]})]}):null}let v={fpImage:{objectFit:"cover"}},b=a().section.withConfig({componentId:"sc-385c5ea2-0"})`
  padding: 0 var(--border-space);
  position: relative;
  margin-top: 40px;
  margin-bottom: 80px;
  display: flex;
  flex-direction: column;

  @media ${s.U.desktop} {
    flex-direction: row;
    margin-bottom: 164px;
  }
`,w=a().div.withConfig({componentId:"sc-385c5ea2-1"})`
  position: relative;
  top: 0;
  display: block;

  .fp-date {
    position: relative;
    font-family: "Lora", serif;
    font-size: 14px;
    font-weight: 400;
    color: #888888;
    margin-top: 0;
    margin-bottom: 16px;
  }

  .fp-cover {
    position: relative;
    height: 220px;
    background-color: black;
  }

  .fp-author {
    color: #888888;
    font-family: "Novela", serif;
    font-style: italic;
    font-weight: 400;
    font-size: 14px;
    margin-top: 12px;
  }

  .fp-title {
    font-family: "Stelvio", serif;
    line-height: 110%;
    font-weight: 400;
    font-size: 30px;
    margin-top: 12px;
    margin-bottom: 0;
    width: 95%;
    color: #161616;
  }

  .fp-topics {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: start;
    justify-content: start;
    flex-wrap: wrap;
    margin-top: 16px;
    height: 32px;
    width: 100%;
    overflow: hidden;
    gap: 8px;
  }

  .fp-lead {
    font-size: 15px;
    color: #888;
    margin-top: 24px;
  }

  @media ${s.U.desktop} {
    position: sticky;
    width: 50%;
    height: 100vh;
    margin-right: 32px;

    .fp-date {
      font-size: 18px;
    }

    .fp-cover {
      height: 430px;
    }

    .fp-author {
      margin-top: 24px;
      font-size: 18px;
    }

    .fp-title {
      font-size: 46px;
      margin-top: 24px;
    }

    .fp-lead {
      max-width: 90%;
      font-weight: 400;
      margin-top: 24px;
      font-size: 17px;
      color: #888;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      -webkit-box-orient: vertical;
    }
  }
`,j=a().div.withConfig({componentId:"sc-385c5ea2-2"})`
  display: inline;
  @media ${s.U.tablet} {
    //margin-left: 16px;
    //width: 50%;
  }
  @media ${s.U.desktop} {
    width: 50%;
  }
`;var k=i(1240);function RenderTopic({topic:e}){let{name:t,posts:i}=e;return(0,o.jsxs)("div",{className:"topic-wrapper",children:[o.jsx(x(),{href:`/categories/${(0,u.lV)(t)}`,children:o.jsx("div",{className:"topic-head",children:o.jsx("h2",{className:"topic-title",children:t})})}),i.slice().reverse().map((e,t)=>o.jsx(k.Z,{post:e,options:{showTopics:!0,showDate:!1,showLead:!1}},t))]})}function SectionTopics({data:e}){return o.jsx(S,{children:e.map((e,t)=>o.jsx(RenderTopic,{topic:e},t))})}let S=a().section.withConfig({componentId:"sc-a932684-0"})`
  position: relative;
  grid-column: 1/13;
  grid-row: 1;

  .topic-head {
    position: relative;
    border-top: 1px solid #161616;
    border-bottom: 1px solid #161616;
    padding-top: 10px;
    padding-bottom: 2px;
    margin-bottom: 60px;
    margin-top: 90px;
    
    &:hover {
      * {
        color: var(--c-brand-light);
      }
    }
  }

  .topic-title {
    margin: 0;
    font-size: 22px;
    font-weight: 500;

    &::after {
      margin: 0;
      position: absolute;
      font-weight: 400;
      top: 10px;
      right: 0;
      content: '→';
      color: black;
      font-size: 22px;
    }
  }

  @media ${s.U.desktop} {
    grid-column: 1/7;

    .topic-head {
      width: 100%;
      border-top: 1px solid #161616;
      border-bottom: 1px solid #161616;
      padding-top: 14px;
      padding-bottom: 4px;
      margin-bottom: 60px;
      margin-top: 90px;
    }

    .topic-title {
      margin: 0;
      font-size: 32px;
      font-weight: 500;
      letter-spacing: 2%;
      color: #242424;

      &::after {
        margin: 0;
        position: absolute;
        font-weight: 400;
        top: 14px;
        right: 0;
        content: '→';
        color: black;
        font-size: 32px;
      }
    }

  }
`;var y=i(6453);function SectionHeader({title:e,subTitle:t,supTitle:i,light:r,margin:a}){return(0,o.jsxs)(C,{margin:a,className:"section-header",light:r,children:[i&&o.jsx("p",{children:i}),o.jsx(y.NZ,{light:r,children:e}),t&&o.jsx("p",{children:t})]})}let C=a().div.withConfig({componentId:"sc-6dd2ed14-0"})`
  position: relative;
  margin-left: ${e=>e.margin?"var(--border-space)":0};
  margin-bottom: var(--fluid-space-m);

  p {
    font-size: 16px;
    font-weight: 400;
    font-family: Switzer, "Helvetica Neue", Helvetica, sans-serif;
    color: ${e=>e.light?"rgba(250,247,243,0.48)":"rgba(22,22,22,0.48)"};
    margin-bottom: 8px;
  }
  
  @media ${s.U.tablet} {
    p {
      font-size: 18px;
    }
  }
  
  @media ${s.U.desktop} {
    p {
      font-size: 20px;
    }
  }
`;var z=i(9667),$=i(1163);function SliderCard({post:e,options:t}){if((0,$.useRouter)(),e.route)return o.jsx(P,{theme:t?.theme,children:(0,o.jsxs)(_,{className:"sc-inner",theme:t?.theme,invert:t?.invert,children:[o.jsx("div",{className:"sc-image",children:o.jsx(l(),{src:e.image,style:N,fill:!0,alt:"",sizes:"(max-width: 768px) 80vw, (max-width: 1220px) 33vw, 20vw"})}),o.jsx(x(),{href:e.route,children:o.jsx("div",{className:"clickable-area"})}),(0,o.jsxs)("div",{className:"sc-text",children:[o.jsx("h2",{className:"sc-title",children:e.title}),o.jsx(x(),{href:e.authorLink||"",children:(0,o.jsxs)("div",{className:"sc-footer",children:[o.jsx("div",{className:"sc-author_image",children:e.authorImage&&o.jsx(l(),{src:e.authorImage,fill:!0,alt:"",style:N,sizes:"40px"})}),(0,o.jsxs)("div",{className:"sc-more",children:[o.jsx("p",{className:"sc-author",children:e.author}),o.jsx("p",{className:"sc-date",children:e.date})]})]})})]})]})})}let N={objectFit:"cover"},P=a().li.withConfig({componentId:"sc-aa6de393-0"})`,
  position: relative;
  list-style: none;
  min-width: 292px;
  padding-left: 16px;
  overflow-y: visible;

  &:last-child {
    margin-right: 24px;
  }
  
  &:first-child {
    min-width: calc(292px - 16px + var(--border-space));
    padding-left: var(--border-space);
  }

  @media ${s.U.desktop} {
    min-width: calc((100% - 24px * 2) / 3);
    padding: 0 !important;
    margin-right: 24px;

    &:last-child {
      margin-right: 0;
    }

    &:first-child {
      min-width: calc((100% - 24px * 2) / 3);
      padding-left: 40px;
    }
  }

  @media (min-width: 1220px) {
    min-width: calc((100% - 26px * 3) / 4);

    &:first-child {
      min-width:  calc((100% - 26px * 3) / 4);
      padding-left: 40px;
    }
  }

`,_=a().div.withConfig({componentId:"sc-aa6de393-1"})`
  position: relative;
  overflow-y: visible;
  width: 100%;
  border: 1px solid ${e=>"dark"===e.theme?"rgba(250,247,243,0.2)":"rgba(22,22,22,0.2)"}; // TODO: var here

  .clickable-area {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 1;

    &:hover {
      cursor: pointer;
      border: 1px solid var(--c-brand-lighter);
      -webkit-box-shadow: 0 0 0 1px var(--c-brand-ligher);
      box-shadow: 0 0 0 1px var(--c-brand-lighter);
    }
    
  }

  .sc-image {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
    background-color: taransparent;
  }

  .sc-text {
    padding: clamp(1.5rem, 0.27rem + 1.92vw, 2rem);
  }

  .sc-title {
    color: ${e=>"dark"===e.theme?"var(--c-soft-cream)":"#161616"};
    margin: 0;
    font-weight: ${e=>"dark"===e.theme?400:500};
    height: clamp(4.5rem, 2.65rem + 2.88vw, 5.25rem);
    font-family: Switzer, sans-serif;
    font-size: clamp(1.125rem, 0.82rem + 0.48vw, 1.25rem);
    line-height: clamp(1.5rem, 0.88rem + 0.96vw, 1.75rem);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .sc-footer {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-top: clamp(3.5rem, 1.03rem + 3.85vw, 4.5rem);

    z-index: 2;

    &:hover {
      .sc-author {
        color: var(--c-brand-light);
      }
    }
  }

  .sc-author_image {
    position: relative;
    overflow: hidden;
    border-radius: 90px;
    width: clamp(2rem, 0.77rem + 1.92vw, 2.5rem);
    height: clamp(2rem, 0.77rem + 1.92vw, 2.5rem);
    margin-right: 12px;
    background-color: ${e=>e.invert?"var(--c-soft-cream)":"#161616"};

    img {
      filter: ${e=>e.invert?"invert()":"none"};
    }
  }

  .sc-more {
    flex-wrap: wrap;
    display: flex;
    flex-direction: column;
    margin-top: -2px;
    gap: 4px;

    .sc-author,
    .sc-date {
      margin: 0;
      font-size: clamp(0.875rem, 0.56rem + 0.48vw, 1rem);
      font-family: Switzer, sans-serif;
    }

    .sc-author {
      color: ${e=>"dark"===e.theme?"#FAF7F3":"#161616"};
      font-weight: 400;
    }

    .sc-date {
      margin-top: -2px;
      color: #989AA4;
    }
  }

  &:active {
    border: 1px solid var(--c-brand-lighter);
  }

  //transition: all 250ms ease-out;

  @media ${s.U.desktop} {
    margin: 2px 2px;
  }
`;var q=i(6689);function Carousel({options:e,children:t}){let i=(0,q.useRef)(null),[r,a]=(0,q.useState)(0),[s,n]=(0,q.useState)([!1,!0]);function move(e,t){var o;let s;a(r+t),o=r+t,s=Math.round(8/Math.trunc(i.current.clientWidth/280)),0===o?n([!1,!0]):o>=s-1?n([!0,!1]):n([!0,!0]),e.preventDefault(),i.current.scrollBy({left:i.current.clientWidth*t,top:0,behavior:"smooth"})}return(0,o.jsxs)(I,{children:[o.jsx(U,{ref:i,onScroll:e=>{let t;(t=i.current.scrollLeft)+50>=i.current.scrollLeftMax?n([!0,!1]):t<=50?n([!1,!0]):n([!0,!0])},children:t}),(0,o.jsxs)(T,{children:[o.jsx("div",{children:e?.seeMoreText&&o.jsx(f.Ty,{text:e.seeMoreText,link:e.seeMoreUrl,theme:"light"})}),(0,o.jsxs)(F,{children:[o.jsx(f.Z_,{disabled:!s[0],theme:e?.theme||"dark",reverse:!0,onClickFunction:e=>move(e,-1)}),o.jsx(f.Z_,{disabled:!s[1],theme:e?.theme||"dark",reverse:!1,onClickFunction:e=>move(e,1)})]})]})]})}let I=a().div.withConfig({componentId:"sc-61385040-0"})`
  position: relative;
  width: 100%;
  overflow-y: visible;
  @media ${s.U.desktop} {
    padding: 0 var(--border-space);
  }
`,U=a().div.withConfig({componentId:"sc-61385040-1"})`
  display: flex;
  flex-direction: row;
  overflow-x: scroll;
  scroll-snap-type: x mandatory;
  overflow-y: visible;
  margin-bottom: 24px;

  > * {
    scroll-snap-align: start;
  }
  
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;
`,T=a().div.withConfig({componentId:"sc-61385040-2"})`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 12px;
  padding: 0 var(--border-space);

  @media ${s.U.desktop} {
    padding: 0;
  }
`,F=a().div.withConfig({componentId:"sc-61385040-3"})`
  display: flex;
  justify-content: right;
  align-items: center;
  gap: 12px;
`;function SectionBloggers({data:e}){let{posts:t}=e;return(0,o.jsxs)(M,{children:[o.jsx(SectionHeader,{supTitle:"Derniers articles de nos",title:"Blogueurs"}),o.jsx(Carousel,{options:{theme:"light",seeMoreUrl:"/recherche?type=Article&page=1",seeMoreText:"Voir tout"},children:t.sort(z.df).map((e,t)=>(function(e,t){if(!e.blog)return null;let i={title:e.title,author:e.blog.blogger.fullName,authorLink:"/blog/"+e.blog.blogger.slug,image:(0,d.k)(e.image),authorImage:(0,d.k)(e.blog.blogger.picture),date:(0,g.S$)(e.published_at),route:(0,m.qt)(e)};return o.jsx(SliderCard,{post:i,options:{theme:"light"}},t)})(e,t))})]})}let M=a().section.withConfig({componentId:"sc-90781d20-0"})`
  padding: 56px 0;
  @media ${s.U.tablet} {
    padding: 128px 0;
  }
  @media ${s.U.desktop} {
    padding: 164px 0;
  }
  .section-header {
    margin-left: var(--border-space);
  }
`;function SectionPodcasts({data:e}){let{posts:t}=e;return t=function(e){let t,i={},o=[],r=0;for(let t of e)i[t.author]?new Date(i[t.author].published_at)<new Date(t.published_at)&&(i[t.author]=t,i[t.author].index=r):i[t.author]=t,r++;for(let[t,r]of Object.entries(i))o.push(i[t]),delete e[r.index];return o=o.sort(z.df),t=e.sort(z.df).splice(0,4),o.concat(t)}(t=t.map(e=>(function(e){let{podcast:t}=(0,c.fw)(e.modules);return{title:e.title,author:t.podcast.name,authorLink:"/podcasts/"+t.podcast.slug,image:(0,d.k)(e.image),authorImage:(0,d.k)(t.podcast.logoSmall),date:(0,g.S$)(e.published_at),published_at:e.published_at,route:(0,m.qt)(e)}})(e))),(0,o.jsxs)(D,{children:[o.jsx(SectionHeader,{title:"Podcasts",supTitle:"Derniers \xe9pisodes",margin:!0,light:!0}),o.jsx(Carousel,{options:{seeMoreUrl:"/recherche?type=Podcast&page=1",seeMoreText:"Voir tout"},children:t.map((e,t)=>e.author?o.jsx(SliderCard,{post:e,options:{theme:"dark",invert:!0}},t):null)})]})}let D=a().section.withConfig({componentId:"sc-85dd2016-0"})`
  position: relative;
  background-color: var(--blue-dark);
  padding: 56px 0 0 0;
  @media ${s.U.tablet} {
    padding: 128px 0 0 0 ;
  }
  @media ${s.U.desktop} {
    padding: 164px 0 0 0;
  }
`;var L=i(9785),Z=i(361);function SectionMission({data:e}){let{partOne:t,partTwo:i}=e,r=(0,q.useRef)(),[a,s]=(0,L.useInView)();return(0,o.jsxs)(A,{ref:a,inView:s,children:[o.jsx(H,{inView:s}),(0,o.jsxs)(R,{ref:r,children:[o.jsx(SectionHeader,{supTitle:"Notre",title:"Mission",light:!0}),o.jsx("p",{className:"mission-desc",children:(0,u.Gq)((0,u.Kd)(t||""))}),o.jsx("div",{children:o.jsx(Z.Z,{theme:"light",text:"En savoir plus",link:"/a-propos"})})]})]})}let A=a().div.withConfig({componentId:"sc-93ceda0f-0"})`
  position: relative;
  top: 0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 24px;
  padding: 0 var(--border-space);
  overflow: hidden;
  height: 100vh;
  max-height: 900px;

  @media ${s.U.tablet} {
    align-items: center;
  }
`,R=a().div.withConfig({componentId:"sc-93ceda0f-1"})`
  grid-column: 1/3;
  margin-top: 48px;
  margin-bottom: 56px;
  width: 100%;
  z-index: 1;

  .mission-desc {
    color: var(--soft-white);
    margin-top: 20px;
    font-size: 20px;
    font-family: "Switzer", serif;
    margin-bottom: 48px;
    z-index: 2;
  }

  @media ${s.U.tablet} {
    font-size: 22px;
    .mission-desc {
      margin-top: 22px;
      font-size: 22px;
    }
  }
  @media ${s.U.desktop} {
    grid-column: 1/2;
    margin-top: 126px;
    margin-bottom: 164px;
    .mission-desc {
      margin-top: 20px;
      font-size: 20px;
    }
  }
`,H=a().div.withConfig({componentId:"sc-93ceda0f-2"})`

  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #F3673B;
  z-index: 0;

  @media ${s.U.desktop} {
    left: -50%;
    width: 200%;
    height: auto;
    top: -200%;
    transform: ${e=>e.inView?"scale(1) translateY(25%)":"scale(0.2) translateY(0)"};
    aspect-ratio: 1/1;
    border-radius: 99999px;
    transform-origin: center;
    transition: all 1.5s ease-out;
  }

`;var V=i(8102);function SectionMostRead({data:e,newsletter:t}){let{name:i,posts:r}=e;return(0,o.jsxs)(G,{children:[o.jsx(V.Z,{title:"",formString:t}),o.jsx("h2",{children:i}),r.map((e,t)=>o.jsx(x(),{href:(0,m.qt)(e),children:(0,o.jsxs)(E,{children:[o.jsx("p",{className:"author",children:e.author.fullName}),o.jsx("p",{className:"title",children:e.title})]})},t))]})}let G=a().div.withConfig({componentId:"sc-ac6d5080-0"})`
  position: relative;
  margin-top: 60px;
  margin-bottom: 60px;
  grid-column: 1/12;
  height: auto;
  @media ${s.U.desktop} {
    grid-column: 9/13;
  }

  .ck-form-wrapper {
    position: relative;
    width: 100%;
    min-height: 400px;
    background-color: indianred;
  }
`,E=a().div.withConfig({componentId:"sc-ac6d5080-1"})`
  display: block;
  font-size: 22px;
  background-color: #161616;
  padding: 24px 24px 24px 24px;
  margin-bottom: 1px;

  .title {
    line-height: 115%;
    margin-top: 16px;
    margin-bottom: 0;
    color: #dcdcdc;
  }

  .author {
    margin: 0;
    font-family: "Lora", serif;
    font-style: italic;
    font-size: 15px;
    color: #888;
  }
`;var O=i(2558);function LargeCard({post:e,theme:t}){let i=(0,u.Kd)((0,u.Gq)(e.lead));return o.jsx(x(),{href:e.link,children:(0,o.jsxs)(B,{theme:t,children:[o.jsx("div",{className:"lc-image",children:o.jsx(O.Z,{imageData:e.image,preserveAspectRatio:!1,sizes:"(max-width: 768px) 100vw, (max-width: 1220px) 33vw, 25vw"})}),o.jsx("div",{className:"lc-text",children:(0,o.jsxs)("div",{className:"lc-content",children:[o.jsx("h3",{className:"lc-title",children:e.title}),o.jsx("p",{className:"lc-lead",children:i})]})})]})})}let B=a().div.withConfig({componentId:"sc-8db41f6-0"})`
  position: relative;
  width: 100%;
  border: 1px solid ${e=>"dark"===e.theme?"rgba(250,247,243,0.2)":"rgba(22,22,22,0.2)"}; // TODO: var here
  
  .lc-image {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
    overflow: hidden;
    background-color: #F9F1E6;
    background-image: url(/images/tpsg-logo.svg);
    background-repeat: no-repeat;
    background-position: center;
  }
  
  .lc-text {
    display: flex;
    height: 360px;
    flex-direction: column;
    justify-content: space-between;
    padding: clamp(1.5rem, 0.112rem + 2.98vw, 2.5rem);
  }
  
  .lc-title {
    position: relative;
    color: ${e=>"dark"===e.theme?"#FFFFFF":"#161616"};
    font-family: Stelvio, sans-serif;
    font-weight: 400;
    font-size: clamp(1.75rem, 1.4029850746268657rem + 0.7462686567164178vw, 2rem);
    line-height: clamp(2rem, 1.4794776119402986rem + 1.1194029850746268vw, 2.375rem);
    margin-bottom: -4px;
    margin-top: 6px;
  }
  
  .lc-lead {
    margin-top: 12px;
    font-family: Switzer, sans-serif;
    font-size: clamp(0.875rem, 0.53rem + 0.75vw, 1.125rem);
    line-height: clamp(1.375rem, 0.8544776119402986rem + 1.1194029850746268vw, 1.75rem);
    color: #989AA4;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    font-weight: 400;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  
  // .lc-date {
  //   font-family: Switzer, sans-serif;
  //   font-size: 14px;
  //   line-height: 20px;
  //   color: #989AA4;
  //   margin-bottom: 0;
  //   text-transform: uppercase;
  // }
  
  @media ${s.U.desktop} {
    &:hover {
      border-color: var(--brand-color);
      -webkit-box-shadow: 0 0 0 1px var(--c-brand-ligher);
      box-shadow: 0 0 0 1px var(--c-brand-lighter);
      cursor: pointer;
    }
  }
  
`;function SectionDouble({webinars:e,formations:t}){if(e.posts.length<1||t.posts.length<1)return o.jsx(o.Fragment,{});function preparedPost(e){let{lead:t}=(0,c.fw)(e.modules);return{title:e.title,link:(0,m.qt)(e),author:e.author.fullName,image:e.image,date:(0,g.S$)(e.published_at),lead:(0,u.Gq)((0,u.Kd)(t?.content||""))}}return(0,o.jsxs)(K,{children:[(0,o.jsxs)("div",{className:"section-item",children:[o.jsx(Q,{}),o.jsx(SectionHeader,{title:"Webinaire",supTitle:"\xc0 voir ou \xe0 revoir",light:!0}),o.jsx(W,{children:o.jsx(LargeCard,{post:preparedPost(e.posts[0]),theme:"dark"})}),o.jsx(Z.Z,{text:"Voir tout",theme:"light",link:"/webinaires"})]}),(0,o.jsxs)("div",{className:"section-item",children:[o.jsx(Q,{}),o.jsx(SectionHeader,{title:"Formation",supTitle:"Pour aller plus loin",light:!0}),o.jsx(W,{children:o.jsx(LargeCard,{post:preparedPost(t.posts[0]),theme:"dark"})}),o.jsx(Z.Z,{text:"Voir tout",theme:"light",link:"/formations"}),o.jsx(J,{})]})]})}let K=a().div.withConfig({componentId:"sc-f307deb0-0"})`
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 0 var(--border-space);
  background-color: var(--blue-dark);
  .animated-text-button {
    margin-top: 24px;
  }

  @media ${s.U.tablet} {
    gap: 16px;
    flex-direction: row;
    .section-item {
      width: 50%;
    }
  }

  @media ${s.U.desktop} {
    gap: 24px;
  }
`,W=a().div.withConfig({componentId:"sc-f307deb0-1"})`
  @media ${s.U.desktop} {
    margin: 0;
  }
`,J=a().div.withConfig({componentId:"sc-f307deb0-2"})`
  height: 80px;
`,Q=a().div.withConfig({componentId:"sc-f307deb0-3"})`
  height: 72px;
  @media ${s.U.desktop} {
    height: 96px;
  }
`},8102:(e,t,i)=>{i.d(t,{Z:()=>s});var o=i(5152),r=i.n(o);let a=r()(()=>i.e(7992).then(i.bind(i,7992)),{loadableGenerated:{modules:["..\\components\\shared\\ConvertkitForm\\DynamicForm.js -> ./CKForm"]},ssr:!1}),s=a},2346:(e,t,i)=>{i.a(e,async(e,o)=>{try{i.r(t),i.d(t,{default:()=>Home,getStaticProps:()=>getStaticProps});var r=i(997),a=i(9182),s=i(7672),n=i(9114),l=i(1385),d=i(8657),c=i(6641),p=i(7518),m=i.n(p),h=i(7467),x=e([d]);function Home({home:e}){return(0,r.jsxs)(g,{className:"page",children:[r.jsx(c.NextSeo,{title:"ToutPourSaGloire.com",description:"ToutPourSaGloire.com c'est des milliers de ressources pour vous aider \xe0 mener une vie qui glorifie Dieu."}),e.articles&&r.jsx(a.ZJ,{data:e.articles}),e.featured.length>0&&r.jsx(d.g4,{content:e.featured[0]}),e.mission&&r.jsx(a.$U,{data:e.mission}),e.podcasts&&r.jsx(a.Qz,{data:e.podcasts}),e.webinars&&e.formations&&r.jsx(a.bh,{webinars:e.webinars,formations:e.formations}),e.bloggers&&r.jsx(a.lm,{data:e.bloggers}),(0,r.jsxs)(f,{className:"site-padding",children:[e.topics&&r.jsx(a.Fn,{data:e.topics}),e.mostRead&&r.jsx(a.dx,{data:e.mostRead,newsletter:e.newsletter})]})]})}d=(x.then?(await x)():x)[0];let g=m().div.withConfig({componentId:"sc-dddf389e-0"})`
  width: 100vw;
  //padding-right: 15px;
  background-color: var(--soft-white);

  .double-section {
    background-color: var(--blue-dark);
    display: flex;
    flex-direction: column;

    @media ${h.U.desktop} {
      flex-direction: row;
    }
  }
`,f=m().div.withConfig({componentId:"sc-dddf389e-1"})`
  display: grid;
  grid-gap: 16px;
  grid-template-columns: repeat(12, 1fr);
`;async function getStaticProps(){let{data:e}=await l.Z.query({query:u});return{props:{home:e.home},revalidate:10}}let u=n.gql`
  ${s.iT}
  query GetHome {
	home{
	  newsletter
	  articles{
		name
		posts(sort: "published_at:DESC"){
		  ...CorePostSet
		  modules {
			...on ComponentModuleLead {
			  __typename
			  content
			}
		  }
		}
	  }
	  podcasts{
		name
		posts{
		  ...CorePostSet
		  modules {
			__typename
			...on ComponentModulePodcast{
			  podcast {
				logoSmall {
				  formats
				  url
				  provider
				}
				name
				slug
			  }
			}
		  }
		}
	  }
	  shop {
		text
		name
		image {
		  formats
		}
	  }
	  quote {
		author
		text
	  }
	  bloggers {
		name
		posts {
		  ...CorePostSet
		  blog{
			slug
			blogger {
			  firstName
			  lastName
			  fullName
			  slug
			  picture {
				url
				width
				height
				provider
			  }
			}
		  }
		}
	  }
	  mission{
		partOne
		partTwo
	  }
	  topics {
		name
		posts{
		  ...CorePostSet
		}
	  }
	  formations {
		name
		posts {
		  ...CorePostSet
		  modules {
			...on ComponentModuleLead {
			  __typename
			  content
			}
		  }
		},
	  }
	  webinars {
		name
		posts {
		  ...CorePostSet
		  modules {
			...on ComponentModuleLead {
			  __typename
			  content
			}
		  }
		},

	  }
	  mostRead {
		name
		posts {
		  ...CorePostSet
		}
	  }
	  featured {
		type
		title
		description
		cta {
		  name
		  url
		  outline
		}
		cta2 {
		  name
		  url
		  outline
		}
		color {
		  foreground
		  background
		}
		image {
		  width
		  height
		  url
		  provider
		  formats
		}
		postRef {
		  id
		  author {
			fullName
		  }
		}
	  }
	}
  }
`;o()}catch(e){o(e)}})},9114:e=>{e.exports=require("@apollo/client")},9526:e=>{e.exports=require("dangerously-set-html-content")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},6641:e=>{e.exports=require("next-seo")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},9785:e=>{e.exports=require("react-intersection-observer")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},3135:e=>{e.exports=import("react-markdown")},1871:e=>{e.exports=import("rehype-raw")},6809:e=>{e.exports=import("remark-gfm")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),i=t.X(0,[3181,5016,6859,8450,9755,5152,4033,779,7113,6453,1077,7620,8672,2832,8657],()=>__webpack_exec__(1725));module.exports=i})();