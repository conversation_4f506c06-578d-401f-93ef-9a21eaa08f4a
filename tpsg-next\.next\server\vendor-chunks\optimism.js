"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/optimism";
exports.ids = ["vendor-chunks/optimism"];
exports.modules = {

/***/ "(rsc)/./node_modules/optimism/lib/bundle.cjs.js":
/*!*************************************************!*\
  !*** ./node_modules/optimism/lib/bundle.cjs.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar trie = __webpack_require__(/*! @wry/trie */ \"(rsc)/./node_modules/@wry/trie/lib/trie.js\");\nvar context = __webpack_require__(/*! @wry/context */ \"(rsc)/./node_modules/optimism/node_modules/@wry/context/lib/context.js\");\nfunction defaultDispose() {}\nvar Cache = /** @class */ function() {\n    function Cache(max, dispose) {\n        if (max === void 0) {\n            max = Infinity;\n        }\n        if (dispose === void 0) {\n            dispose = defaultDispose;\n        }\n        this.max = max;\n        this.dispose = dispose;\n        this.map = new Map();\n        this.newest = null;\n        this.oldest = null;\n    }\n    Cache.prototype.has = function(key) {\n        return this.map.has(key);\n    };\n    Cache.prototype.get = function(key) {\n        var node = this.getNode(key);\n        return node && node.value;\n    };\n    Cache.prototype.getNode = function(key) {\n        var node = this.map.get(key);\n        if (node && node !== this.newest) {\n            var older = node.older, newer = node.newer;\n            if (newer) {\n                newer.older = older;\n            }\n            if (older) {\n                older.newer = newer;\n            }\n            node.older = this.newest;\n            node.older.newer = node;\n            node.newer = null;\n            this.newest = node;\n            if (node === this.oldest) {\n                this.oldest = newer;\n            }\n        }\n        return node;\n    };\n    Cache.prototype.set = function(key, value) {\n        var node = this.getNode(key);\n        if (node) {\n            return node.value = value;\n        }\n        node = {\n            key: key,\n            value: value,\n            newer: null,\n            older: this.newest\n        };\n        if (this.newest) {\n            this.newest.newer = node;\n        }\n        this.newest = node;\n        this.oldest = this.oldest || node;\n        this.map.set(key, node);\n        return node.value;\n    };\n    Cache.prototype.clean = function() {\n        while(this.oldest && this.map.size > this.max){\n            this.delete(this.oldest.key);\n        }\n    };\n    Cache.prototype.delete = function(key) {\n        var node = this.map.get(key);\n        if (node) {\n            if (node === this.newest) {\n                this.newest = node.older;\n            }\n            if (node === this.oldest) {\n                this.oldest = node.newer;\n            }\n            if (node.newer) {\n                node.newer.older = node.older;\n            }\n            if (node.older) {\n                node.older.newer = node.newer;\n            }\n            this.map.delete(key);\n            this.dispose(node.value, key);\n            return true;\n        }\n        return false;\n    };\n    return Cache;\n}();\nvar parentEntrySlot = new context.Slot();\nvar _a;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar // This Array.from polyfill is restricted to working with Set<any> for now,\n// but we can improve the polyfill and add other input types, as needed. Note\n// that this fallback implementation will only be used if the host environment\n// does not support a native Array.from function. In most modern JS runtimes,\n// the toArray function exported here will be === Array.from.\ntoArray = (_a = Array.from, _a === void 0 ? function(collection) {\n    var array = [];\n    collection.forEach(function(item) {\n        return array.push(item);\n    });\n    return array;\n} : _a);\nfunction maybeUnsubscribe(entryOrDep) {\n    var unsubscribe = entryOrDep.unsubscribe;\n    if (typeof unsubscribe === \"function\") {\n        entryOrDep.unsubscribe = void 0;\n        unsubscribe();\n    }\n}\nvar emptySetPool = [];\nvar POOL_TARGET_SIZE = 100;\n// Since this package might be used browsers, we should avoid using the\n// Node built-in assert module.\nfunction assert(condition, optionalMessage) {\n    if (!condition) {\n        throw new Error(optionalMessage || \"assertion failure\");\n    }\n}\nfunction valueIs(a, b) {\n    var len = a.length;\n    return(// Unknown values are not equal to each other.\n    len > 0 && // Both values must be ordinary (or both exceptional) to be equal.\n    len === b.length && // The underlying value or exception must be the same.\n    a[len - 1] === b[len - 1]);\n}\nfunction valueGet(value) {\n    switch(value.length){\n        case 0:\n            throw new Error(\"unknown value\");\n        case 1:\n            return value[0];\n        case 2:\n            throw value[1];\n    }\n}\nfunction valueCopy(value) {\n    return value.slice(0);\n}\nvar Entry = /** @class */ function() {\n    function Entry(fn) {\n        this.fn = fn;\n        this.parents = new Set();\n        this.childValues = new Map();\n        // When this Entry has children that are dirty, this property becomes\n        // a Set containing other Entry objects, borrowed from emptySetPool.\n        // When the set becomes empty, it gets recycled back to emptySetPool.\n        this.dirtyChildren = null;\n        this.dirty = true;\n        this.recomputing = false;\n        this.value = [];\n        this.deps = null;\n        ++Entry.count;\n    }\n    Entry.prototype.peek = function() {\n        if (this.value.length === 1 && !mightBeDirty(this)) {\n            rememberParent(this);\n            return this.value[0];\n        }\n    };\n    // This is the most important method of the Entry API, because it\n    // determines whether the cached this.value can be returned immediately,\n    // or must be recomputed. The overall performance of the caching system\n    // depends on the truth of the following observations: (1) this.dirty is\n    // usually false, (2) this.dirtyChildren is usually null/empty, and thus\n    // (3) valueGet(this.value) is usually returned without recomputation.\n    Entry.prototype.recompute = function(args) {\n        assert(!this.recomputing, \"already recomputing\");\n        rememberParent(this);\n        return mightBeDirty(this) ? reallyRecompute(this, args) : valueGet(this.value);\n    };\n    Entry.prototype.setDirty = function() {\n        if (this.dirty) return;\n        this.dirty = true;\n        this.value.length = 0;\n        reportDirty(this);\n        // We can go ahead and unsubscribe here, since any further dirty\n        // notifications we receive will be redundant, and unsubscribing may\n        // free up some resources, e.g. file watchers.\n        maybeUnsubscribe(this);\n    };\n    Entry.prototype.dispose = function() {\n        var _this = this;\n        this.setDirty();\n        // Sever any dependency relationships with our own children, so those\n        // children don't retain this parent Entry in their child.parents sets,\n        // thereby preventing it from being fully garbage collected.\n        forgetChildren(this);\n        // Because this entry has been kicked out of the cache (in index.js),\n        // we've lost the ability to find out if/when this entry becomes dirty,\n        // whether that happens through a subscription, because of a direct call\n        // to entry.setDirty(), or because one of its children becomes dirty.\n        // Because of this loss of future information, we have to assume the\n        // worst (that this entry might have become dirty very soon), so we must\n        // immediately mark this entry's parents as dirty. Normally we could\n        // just call entry.setDirty() rather than calling parent.setDirty() for\n        // each parent, but that would leave this entry in parent.childValues\n        // and parent.dirtyChildren, which would prevent the child from being\n        // truly forgotten.\n        eachParent(this, function(parent, child) {\n            parent.setDirty();\n            forgetChild(parent, _this);\n        });\n    };\n    Entry.prototype.forget = function() {\n        // The code that creates Entry objects in index.ts will replace this method\n        // with one that actually removes the Entry from the cache, which will also\n        // trigger the entry.dispose method.\n        this.dispose();\n    };\n    Entry.prototype.dependOn = function(dep) {\n        dep.add(this);\n        if (!this.deps) {\n            this.deps = emptySetPool.pop() || new Set();\n        }\n        this.deps.add(dep);\n    };\n    Entry.prototype.forgetDeps = function() {\n        var _this = this;\n        if (this.deps) {\n            toArray(this.deps).forEach(function(dep) {\n                return dep.delete(_this);\n            });\n            this.deps.clear();\n            emptySetPool.push(this.deps);\n            this.deps = null;\n        }\n    };\n    Entry.count = 0;\n    return Entry;\n}();\nfunction rememberParent(child) {\n    var parent = parentEntrySlot.getValue();\n    if (parent) {\n        child.parents.add(parent);\n        if (!parent.childValues.has(child)) {\n            parent.childValues.set(child, []);\n        }\n        if (mightBeDirty(child)) {\n            reportDirtyChild(parent, child);\n        } else {\n            reportCleanChild(parent, child);\n        }\n        return parent;\n    }\n}\nfunction reallyRecompute(entry, args) {\n    forgetChildren(entry);\n    // Set entry as the parent entry while calling recomputeNewValue(entry).\n    parentEntrySlot.withValue(entry, recomputeNewValue, [\n        entry,\n        args\n    ]);\n    if (maybeSubscribe(entry, args)) {\n        // If we successfully recomputed entry.value and did not fail to\n        // (re)subscribe, then this Entry is no longer explicitly dirty.\n        setClean(entry);\n    }\n    return valueGet(entry.value);\n}\nfunction recomputeNewValue(entry, args) {\n    entry.recomputing = true;\n    // Set entry.value as unknown.\n    entry.value.length = 0;\n    try {\n        // If entry.fn succeeds, entry.value will become a normal Value.\n        entry.value[0] = entry.fn.apply(null, args);\n    } catch (e) {\n        // If entry.fn throws, entry.value will become exceptional.\n        entry.value[1] = e;\n    }\n    // Either way, this line is always reached.\n    entry.recomputing = false;\n}\nfunction mightBeDirty(entry) {\n    return entry.dirty || !!(entry.dirtyChildren && entry.dirtyChildren.size);\n}\nfunction setClean(entry) {\n    entry.dirty = false;\n    if (mightBeDirty(entry)) {\n        // This Entry may still have dirty children, in which case we can't\n        // let our parents know we're clean just yet.\n        return;\n    }\n    reportClean(entry);\n}\nfunction reportDirty(child) {\n    eachParent(child, reportDirtyChild);\n}\nfunction reportClean(child) {\n    eachParent(child, reportCleanChild);\n}\nfunction eachParent(child, callback) {\n    var parentCount = child.parents.size;\n    if (parentCount) {\n        var parents = toArray(child.parents);\n        for(var i = 0; i < parentCount; ++i){\n            callback(parents[i], child);\n        }\n    }\n}\n// Let a parent Entry know that one of its children may be dirty.\nfunction reportDirtyChild(parent, child) {\n    // Must have called rememberParent(child) before calling\n    // reportDirtyChild(parent, child).\n    assert(parent.childValues.has(child));\n    assert(mightBeDirty(child));\n    var parentWasClean = !mightBeDirty(parent);\n    if (!parent.dirtyChildren) {\n        parent.dirtyChildren = emptySetPool.pop() || new Set;\n    } else if (parent.dirtyChildren.has(child)) {\n        // If we already know this child is dirty, then we must have already\n        // informed our own parents that we are dirty, so we can terminate\n        // the recursion early.\n        return;\n    }\n    parent.dirtyChildren.add(child);\n    // If parent was clean before, it just became (possibly) dirty (according to\n    // mightBeDirty), since we just added child to parent.dirtyChildren.\n    if (parentWasClean) {\n        reportDirty(parent);\n    }\n}\n// Let a parent Entry know that one of its children is no longer dirty.\nfunction reportCleanChild(parent, child) {\n    // Must have called rememberChild(child) before calling\n    // reportCleanChild(parent, child).\n    assert(parent.childValues.has(child));\n    assert(!mightBeDirty(child));\n    var childValue = parent.childValues.get(child);\n    if (childValue.length === 0) {\n        parent.childValues.set(child, valueCopy(child.value));\n    } else if (!valueIs(childValue, child.value)) {\n        parent.setDirty();\n    }\n    removeDirtyChild(parent, child);\n    if (mightBeDirty(parent)) {\n        return;\n    }\n    reportClean(parent);\n}\nfunction removeDirtyChild(parent, child) {\n    var dc = parent.dirtyChildren;\n    if (dc) {\n        dc.delete(child);\n        if (dc.size === 0) {\n            if (emptySetPool.length < POOL_TARGET_SIZE) {\n                emptySetPool.push(dc);\n            }\n            parent.dirtyChildren = null;\n        }\n    }\n}\n// Removes all children from this entry and returns an array of the\n// removed children.\nfunction forgetChildren(parent) {\n    if (parent.childValues.size > 0) {\n        parent.childValues.forEach(function(_value, child) {\n            forgetChild(parent, child);\n        });\n    }\n    // Remove this parent Entry from any sets to which it was added by the\n    // addToSet method.\n    parent.forgetDeps();\n    // After we forget all our children, this.dirtyChildren must be empty\n    // and therefore must have been reset to null.\n    assert(parent.dirtyChildren === null);\n}\nfunction forgetChild(parent, child) {\n    child.parents.delete(parent);\n    parent.childValues.delete(child);\n    removeDirtyChild(parent, child);\n}\nfunction maybeSubscribe(entry, args) {\n    if (typeof entry.subscribe === \"function\") {\n        try {\n            maybeUnsubscribe(entry); // Prevent double subscriptions.\n            entry.unsubscribe = entry.subscribe.apply(null, args);\n        } catch (e) {\n            // If this Entry has a subscribe function and it threw an exception\n            // (or an unsubscribe function it previously returned now throws),\n            // return false to indicate that we were not able to subscribe (or\n            // unsubscribe), and this Entry should remain dirty.\n            entry.setDirty();\n            return false;\n        }\n    }\n    // Returning true indicates either that there was no entry.subscribe\n    // function or that it succeeded.\n    return true;\n}\nvar EntryMethods = {\n    setDirty: true,\n    dispose: true,\n    forget: true\n};\nfunction dep(options) {\n    var depsByKey = new Map();\n    var subscribe = options && options.subscribe;\n    function depend(key) {\n        var parent = parentEntrySlot.getValue();\n        if (parent) {\n            var dep_1 = depsByKey.get(key);\n            if (!dep_1) {\n                depsByKey.set(key, dep_1 = new Set);\n            }\n            parent.dependOn(dep_1);\n            if (typeof subscribe === \"function\") {\n                maybeUnsubscribe(dep_1);\n                dep_1.unsubscribe = subscribe(key);\n            }\n        }\n    }\n    depend.dirty = function dirty(key, entryMethodName) {\n        var dep = depsByKey.get(key);\n        if (dep) {\n            var m_1 = entryMethodName && hasOwnProperty.call(EntryMethods, entryMethodName) ? entryMethodName : \"setDirty\";\n            // We have to use toArray(dep).forEach instead of dep.forEach, because\n            // modifying a Set while iterating over it can cause elements in the Set\n            // to be removed from the Set before they've been iterated over.\n            toArray(dep).forEach(function(entry) {\n                return entry[m_1]();\n            });\n            depsByKey.delete(key);\n            maybeUnsubscribe(dep);\n        }\n    };\n    return depend;\n}\nfunction makeDefaultMakeCacheKeyFunction() {\n    var keyTrie = new trie.Trie(typeof WeakMap === \"function\");\n    return function() {\n        return keyTrie.lookupArray(arguments);\n    };\n}\n// The defaultMakeCacheKey function is remarkably powerful, because it gives\n// a unique object for any shallow-identical list of arguments. If you need\n// to implement a custom makeCacheKey function, you may find it helpful to\n// delegate the final work to defaultMakeCacheKey, which is why we export it\n// here. However, you may want to avoid defaultMakeCacheKey if your runtime\n// does not support WeakMap, or you have the ability to return a string key.\n// In those cases, just write your own custom makeCacheKey functions.\nvar defaultMakeCacheKey = makeDefaultMakeCacheKeyFunction();\nvar caches = new Set();\nfunction wrap(originalFunction, options) {\n    if (options === void 0) {\n        options = Object.create(null);\n    }\n    var cache = new Cache(options.max || Math.pow(2, 16), function(entry) {\n        return entry.dispose();\n    });\n    var keyArgs = options.keyArgs;\n    var makeCacheKey = options.makeCacheKey || makeDefaultMakeCacheKeyFunction();\n    var optimistic = function() {\n        var key = makeCacheKey.apply(null, keyArgs ? keyArgs.apply(null, arguments) : arguments);\n        if (key === void 0) {\n            return originalFunction.apply(null, arguments);\n        }\n        var entry = cache.get(key);\n        if (!entry) {\n            cache.set(key, entry = new Entry(originalFunction));\n            entry.subscribe = options.subscribe;\n            // Give the Entry the ability to trigger cache.delete(key), even though\n            // the Entry itself does not know about key or cache.\n            entry.forget = function() {\n                return cache.delete(key);\n            };\n        }\n        var value = entry.recompute(Array.prototype.slice.call(arguments));\n        // Move this entry to the front of the least-recently used queue,\n        // since we just finished computing its value.\n        cache.set(key, entry);\n        caches.add(cache);\n        // Clean up any excess entries in the cache, but only if there is no\n        // active parent entry, meaning we're not in the middle of a larger\n        // computation that might be flummoxed by the cleaning.\n        if (!parentEntrySlot.hasValue()) {\n            caches.forEach(function(cache) {\n                return cache.clean();\n            });\n            caches.clear();\n        }\n        return value;\n    };\n    Object.defineProperty(optimistic, \"size\", {\n        get: function() {\n            return cache[\"map\"].size;\n        },\n        configurable: false,\n        enumerable: false\n    });\n    function dirtyKey(key) {\n        var entry = cache.get(key);\n        if (entry) {\n            entry.setDirty();\n        }\n    }\n    optimistic.dirtyKey = dirtyKey;\n    optimistic.dirty = function dirty() {\n        dirtyKey(makeCacheKey.apply(null, arguments));\n    };\n    function peekKey(key) {\n        var entry = cache.get(key);\n        if (entry) {\n            return entry.peek();\n        }\n    }\n    optimistic.peekKey = peekKey;\n    optimistic.peek = function peek() {\n        return peekKey(makeCacheKey.apply(null, arguments));\n    };\n    function forgetKey(key) {\n        return cache.delete(key);\n    }\n    optimistic.forgetKey = forgetKey;\n    optimistic.forget = function forget() {\n        return forgetKey(makeCacheKey.apply(null, arguments));\n    };\n    optimistic.makeCacheKey = makeCacheKey;\n    optimistic.getKey = keyArgs ? function getKey() {\n        return makeCacheKey.apply(null, keyArgs.apply(null, arguments));\n    } : makeCacheKey;\n    return Object.freeze(optimistic);\n}\nObject.defineProperty(exports, \"KeyTrie\", ({\n    enumerable: true,\n    get: function() {\n        return trie.Trie;\n    }\n}));\nObject.defineProperty(exports, \"asyncFromGen\", ({\n    enumerable: true,\n    get: function() {\n        return context.asyncFromGen;\n    }\n}));\nObject.defineProperty(exports, \"bindContext\", ({\n    enumerable: true,\n    get: function() {\n        return context.bind;\n    }\n}));\nObject.defineProperty(exports, \"noContext\", ({\n    enumerable: true,\n    get: function() {\n        return context.noContext;\n    }\n}));\nObject.defineProperty(exports, \"setTimeout\", ({\n    enumerable: true,\n    get: function() {\n        return context.setTimeout;\n    }\n}));\nexports.defaultMakeCacheKey = defaultMakeCacheKey;\nexports.dep = dep;\nexports.wrap = wrap; //# sourceMappingURL=bundle.cjs.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/optimism/lib/bundle.cjs.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/optimism/node_modules/@wry/context/lib/context.js":
/*!************************************************************************!*\
  !*** ./node_modules/optimism/node_modules/@wry/context/lib/context.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n// This currentContext variable will only be used if the makeSlotClass\n// function is called, which happens only if this is the first copy of the\n// @wry/context package to be imported.\nvar currentContext = null;\n// This unique internal object is used to denote the absence of a value\n// for a given Slot, and is never exposed to outside code.\nvar MISSING_VALUE = {};\nvar idCounter = 1;\n// Although we can't do anything about the cost of duplicated code from\n// accidentally bundling multiple copies of the @wry/context package, we can\n// avoid creating the Slot class more than once using makeSlotClass.\nvar makeSlotClass = function() {\n    return /** @class */ function() {\n        function Slot() {\n            // If you have a Slot object, you can find out its slot.id, but you cannot\n            // guess the slot.id of a Slot you don't have access to, thanks to the\n            // randomized suffix.\n            this.id = [\n                \"slot\",\n                idCounter++,\n                Date.now(),\n                Math.random().toString(36).slice(2)\n            ].join(\":\");\n        }\n        Slot.prototype.hasValue = function() {\n            for(var context_1 = currentContext; context_1; context_1 = context_1.parent){\n                // We use the Slot object iself as a key to its value, which means the\n                // value cannot be obtained without a reference to the Slot object.\n                if (this.id in context_1.slots) {\n                    var value = context_1.slots[this.id];\n                    if (value === MISSING_VALUE) break;\n                    if (context_1 !== currentContext) {\n                        // Cache the value in currentContext.slots so the next lookup will\n                        // be faster. This caching is safe because the tree of contexts and\n                        // the values of the slots are logically immutable.\n                        currentContext.slots[this.id] = value;\n                    }\n                    return true;\n                }\n            }\n            if (currentContext) {\n                // If a value was not found for this Slot, it's never going to be found\n                // no matter how many times we look it up, so we might as well cache\n                // the absence of the value, too.\n                currentContext.slots[this.id] = MISSING_VALUE;\n            }\n            return false;\n        };\n        Slot.prototype.getValue = function() {\n            if (this.hasValue()) {\n                return currentContext.slots[this.id];\n            }\n        };\n        Slot.prototype.withValue = function(value, callback, // Given the prevalence of arrow functions, specifying arguments is likely\n        // to be much more common than specifying `this`, hence this ordering:\n        args, thisArg) {\n            var _a;\n            var slots = (_a = {\n                __proto__: null\n            }, _a[this.id] = value, _a);\n            var parent = currentContext;\n            currentContext = {\n                parent: parent,\n                slots: slots\n            };\n            try {\n                // Function.prototype.apply allows the arguments array argument to be\n                // omitted or undefined, so args! is fine here.\n                return callback.apply(thisArg, args);\n            } finally{\n                currentContext = parent;\n            }\n        };\n        // Capture the current context and wrap a callback function so that it\n        // reestablishes the captured context when called.\n        Slot.bind = function(callback) {\n            var context = currentContext;\n            return function() {\n                var saved = currentContext;\n                try {\n                    currentContext = context;\n                    return callback.apply(this, arguments);\n                } finally{\n                    currentContext = saved;\n                }\n            };\n        };\n        // Immediately run a callback function without any captured context.\n        Slot.noContext = function(callback, // Given the prevalence of arrow functions, specifying arguments is likely\n        // to be much more common than specifying `this`, hence this ordering:\n        args, thisArg) {\n            if (currentContext) {\n                var saved = currentContext;\n                try {\n                    currentContext = null;\n                    // Function.prototype.apply allows the arguments array argument to be\n                    // omitted or undefined, so args! is fine here.\n                    return callback.apply(thisArg, args);\n                } finally{\n                    currentContext = saved;\n                }\n            } else {\n                return callback.apply(thisArg, args);\n            }\n        };\n        return Slot;\n    }();\n};\n// We store a single global implementation of the Slot class as a permanent\n// non-enumerable symbol property of the Array constructor. This obfuscation\n// does nothing to prevent access to the Slot class, but at least it ensures\n// the implementation (i.e. currentContext) cannot be tampered with, and all\n// copies of the @wry/context package (hopefully just one) will share the\n// same Slot implementation. Since the first copy of the @wry/context package\n// to be imported wins, this technique imposes a very high cost for any\n// future breaking changes to the Slot class.\nvar globalKey = \"@wry/context:Slot\";\nvar host = Array;\nvar Slot = host[globalKey] || function() {\n    var Slot = makeSlotClass();\n    try {\n        Object.defineProperty(host, globalKey, {\n            value: host[globalKey] = Slot,\n            enumerable: false,\n            writable: false,\n            configurable: false\n        });\n    } finally{\n        return Slot;\n    }\n}();\nvar bind = Slot.bind, noContext = Slot.noContext;\nfunction setTimeoutWithContext(callback, delay) {\n    return setTimeout(bind(callback), delay);\n}\n// Turn any generator function into an async function (using yield instead\n// of await), with context automatically preserved across yields.\nfunction asyncFromGen(genFn) {\n    return function() {\n        var gen = genFn.apply(this, arguments);\n        var boundNext = bind(gen.next);\n        var boundThrow = bind(gen.throw);\n        return new Promise(function(resolve, reject) {\n            function invoke(method, argument) {\n                try {\n                    var result = method.call(gen, argument);\n                } catch (error) {\n                    return reject(error);\n                }\n                var next = result.done ? resolve : invokeNext;\n                if (isPromiseLike(result.value)) {\n                    result.value.then(next, result.done ? reject : invokeThrow);\n                } else {\n                    next(result.value);\n                }\n            }\n            var invokeNext = function(value) {\n                return invoke(boundNext, value);\n            };\n            var invokeThrow = function(error) {\n                return invoke(boundThrow, error);\n            };\n            invokeNext();\n        });\n    };\n}\nfunction isPromiseLike(value) {\n    return value && typeof value.then === \"function\";\n}\n// If you use the fibers npm package to implement coroutines in Node.js,\n// you should call this function at least once to ensure context management\n// remains coherent across any yields.\nvar wrappedFibers = [];\nfunction wrapYieldingFiberMethods(Fiber) {\n    // There can be only one implementation of Fiber per process, so this array\n    // should never grow longer than one element.\n    if (wrappedFibers.indexOf(Fiber) < 0) {\n        var wrap = function(obj, method) {\n            var fn = obj[method];\n            obj[method] = function() {\n                return noContext(fn, arguments, this);\n            };\n        };\n        // These methods can yield, according to\n        // https://github.com/laverdet/node-fibers/blob/ddebed9b8ae3883e57f822e2108e6943e5c8d2a8/fibers.js#L97-L100\n        wrap(Fiber, \"yield\");\n        wrap(Fiber.prototype, \"run\");\n        wrap(Fiber.prototype, \"throwInto\");\n        wrappedFibers.push(Fiber);\n    }\n    return Fiber;\n}\nexports.Slot = Slot;\nexports.asyncFromGen = asyncFromGen;\nexports.bind = bind;\nexports.noContext = noContext;\nexports.setTimeout = setTimeoutWithContext;\nexports.wrapYieldingFiberMethods = wrapYieldingFiberMethods; //# sourceMappingURL=context.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/optimism/node_modules/@wry/context/lib/context.js\n");

/***/ })

};
;