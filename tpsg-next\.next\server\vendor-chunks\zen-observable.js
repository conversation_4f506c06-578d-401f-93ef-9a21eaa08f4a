"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zen-observable";
exports.ids = ["vendor-chunks/zen-observable"];
exports.modules = {

/***/ "(rsc)/./node_modules/zen-observable/index.js":
/*!**********************************************!*\
  !*** ./node_modules/zen-observable/index.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nmodule.exports = __webpack_require__(/*! ./lib/Observable.js */ \"(rsc)/./node_modules/zen-observable/lib/Observable.js\").Observable;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvemVuLW9ic2VydmFibGUvaW5kZXguanMiLCJtYXBwaW5ncyI6IjtBQUFBQSxtSUFBMEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90cHNnLW5leHQvLi9ub2RlX21vZHVsZXMvemVuLW9ic2VydmFibGUvaW5kZXguanM/MDlkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vbGliL09ic2VydmFibGUuanMnKS5PYnNlcnZhYmxlO1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJyZXF1aXJlIiwiT2JzZXJ2YWJsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/zen-observable/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/zen-observable/lib/Observable.js":
/*!*******************************************************!*\
  !*** ./node_modules/zen-observable/lib/Observable.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Observable = void 0;\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\n// === Symbol Support ===\nvar hasSymbols = function() {\n    return typeof Symbol === \"function\";\n};\nvar hasSymbol = function(name) {\n    return hasSymbols() && Boolean(Symbol[name]);\n};\nvar getSymbol = function(name) {\n    return hasSymbol(name) ? Symbol[name] : \"@@\" + name;\n};\nif (hasSymbols() && !hasSymbol(\"observable\")) {\n    Symbol.observable = Symbol(\"observable\");\n}\nvar SymbolIterator = getSymbol(\"iterator\");\nvar SymbolObservable = getSymbol(\"observable\");\nvar SymbolSpecies = getSymbol(\"species\"); // === Abstract Operations ===\nfunction getMethod(obj, key) {\n    var value = obj[key];\n    if (value == null) return undefined;\n    if (typeof value !== \"function\") throw new TypeError(value + \" is not a function\");\n    return value;\n}\nfunction getSpecies(obj) {\n    var ctor = obj.constructor;\n    if (ctor !== undefined) {\n        ctor = ctor[SymbolSpecies];\n        if (ctor === null) {\n            ctor = undefined;\n        }\n    }\n    return ctor !== undefined ? ctor : Observable;\n}\nfunction isObservable(x) {\n    return x instanceof Observable; // SPEC: Brand check\n}\nfunction hostReportError(e) {\n    if (hostReportError.log) {\n        hostReportError.log(e);\n    } else {\n        setTimeout(function() {\n            throw e;\n        });\n    }\n}\nfunction enqueue(fn) {\n    Promise.resolve().then(function() {\n        try {\n            fn();\n        } catch (e) {\n            hostReportError(e);\n        }\n    });\n}\nfunction cleanupSubscription(subscription) {\n    var cleanup = subscription._cleanup;\n    if (cleanup === undefined) return;\n    subscription._cleanup = undefined;\n    if (!cleanup) {\n        return;\n    }\n    try {\n        if (typeof cleanup === \"function\") {\n            cleanup();\n        } else {\n            var unsubscribe = getMethod(cleanup, \"unsubscribe\");\n            if (unsubscribe) {\n                unsubscribe.call(cleanup);\n            }\n        }\n    } catch (e) {\n        hostReportError(e);\n    }\n}\nfunction closeSubscription(subscription) {\n    subscription._observer = undefined;\n    subscription._queue = undefined;\n    subscription._state = \"closed\";\n}\nfunction flushSubscription(subscription) {\n    var queue = subscription._queue;\n    if (!queue) {\n        return;\n    }\n    subscription._queue = undefined;\n    subscription._state = \"ready\";\n    for(var i = 0; i < queue.length; ++i){\n        notifySubscription(subscription, queue[i].type, queue[i].value);\n        if (subscription._state === \"closed\") break;\n    }\n}\nfunction notifySubscription(subscription, type, value) {\n    subscription._state = \"running\";\n    var observer = subscription._observer;\n    try {\n        var m = getMethod(observer, type);\n        switch(type){\n            case \"next\":\n                if (m) m.call(observer, value);\n                break;\n            case \"error\":\n                closeSubscription(subscription);\n                if (m) m.call(observer, value);\n                else throw value;\n                break;\n            case \"complete\":\n                closeSubscription(subscription);\n                if (m) m.call(observer);\n                break;\n        }\n    } catch (e) {\n        hostReportError(e);\n    }\n    if (subscription._state === \"closed\") cleanupSubscription(subscription);\n    else if (subscription._state === \"running\") subscription._state = \"ready\";\n}\nfunction onNotify(subscription, type, value) {\n    if (subscription._state === \"closed\") return;\n    if (subscription._state === \"buffering\") {\n        subscription._queue.push({\n            type: type,\n            value: value\n        });\n        return;\n    }\n    if (subscription._state !== \"ready\") {\n        subscription._state = \"buffering\";\n        subscription._queue = [\n            {\n                type: type,\n                value: value\n            }\n        ];\n        enqueue(function() {\n            return flushSubscription(subscription);\n        });\n        return;\n    }\n    notifySubscription(subscription, type, value);\n}\nvar Subscription = /*#__PURE__*/ function() {\n    function Subscription(observer, subscriber) {\n        _classCallCheck(this, Subscription);\n        // ASSERT: observer is an object\n        // ASSERT: subscriber is callable\n        this._cleanup = undefined;\n        this._observer = observer;\n        this._queue = undefined;\n        this._state = \"initializing\";\n        var subscriptionObserver = new SubscriptionObserver(this);\n        try {\n            this._cleanup = subscriber.call(undefined, subscriptionObserver);\n        } catch (e) {\n            subscriptionObserver.error(e);\n        }\n        if (this._state === \"initializing\") this._state = \"ready\";\n    }\n    _createClass(Subscription, [\n        {\n            key: \"unsubscribe\",\n            value: function unsubscribe() {\n                if (this._state !== \"closed\") {\n                    closeSubscription(this);\n                    cleanupSubscription(this);\n                }\n            }\n        },\n        {\n            key: \"closed\",\n            get: function() {\n                return this._state === \"closed\";\n            }\n        }\n    ]);\n    return Subscription;\n}();\nvar SubscriptionObserver = /*#__PURE__*/ function() {\n    function SubscriptionObserver(subscription) {\n        _classCallCheck(this, SubscriptionObserver);\n        this._subscription = subscription;\n    }\n    _createClass(SubscriptionObserver, [\n        {\n            key: \"next\",\n            value: function next(value) {\n                onNotify(this._subscription, \"next\", value);\n            }\n        },\n        {\n            key: \"error\",\n            value: function error(value) {\n                onNotify(this._subscription, \"error\", value);\n            }\n        },\n        {\n            key: \"complete\",\n            value: function complete() {\n                onNotify(this._subscription, \"complete\");\n            }\n        },\n        {\n            key: \"closed\",\n            get: function() {\n                return this._subscription._state === \"closed\";\n            }\n        }\n    ]);\n    return SubscriptionObserver;\n}();\nvar Observable = /*#__PURE__*/ function() {\n    function Observable(subscriber) {\n        _classCallCheck(this, Observable);\n        if (!(this instanceof Observable)) throw new TypeError(\"Observable cannot be called as a function\");\n        if (typeof subscriber !== \"function\") throw new TypeError(\"Observable initializer must be a function\");\n        this._subscriber = subscriber;\n    }\n    _createClass(Observable, [\n        {\n            key: \"subscribe\",\n            value: function subscribe(observer) {\n                if (typeof observer !== \"object\" || observer === null) {\n                    observer = {\n                        next: observer,\n                        error: arguments[1],\n                        complete: arguments[2]\n                    };\n                }\n                return new Subscription(observer, this._subscriber);\n            }\n        },\n        {\n            key: \"forEach\",\n            value: function forEach(fn) {\n                var _this = this;\n                return new Promise(function(resolve, reject) {\n                    if (typeof fn !== \"function\") {\n                        reject(new TypeError(fn + \" is not a function\"));\n                        return;\n                    }\n                    function done() {\n                        subscription.unsubscribe();\n                        resolve();\n                    }\n                    var subscription = _this.subscribe({\n                        next: function(value) {\n                            try {\n                                fn(value, done);\n                            } catch (e) {\n                                reject(e);\n                                subscription.unsubscribe();\n                            }\n                        },\n                        error: reject,\n                        complete: resolve\n                    });\n                });\n            }\n        },\n        {\n            key: \"map\",\n            value: function map(fn) {\n                var _this2 = this;\n                if (typeof fn !== \"function\") throw new TypeError(fn + \" is not a function\");\n                var C = getSpecies(this);\n                return new C(function(observer) {\n                    return _this2.subscribe({\n                        next: function(value) {\n                            try {\n                                value = fn(value);\n                            } catch (e) {\n                                return observer.error(e);\n                            }\n                            observer.next(value);\n                        },\n                        error: function(e) {\n                            observer.error(e);\n                        },\n                        complete: function() {\n                            observer.complete();\n                        }\n                    });\n                });\n            }\n        },\n        {\n            key: \"filter\",\n            value: function filter(fn) {\n                var _this3 = this;\n                if (typeof fn !== \"function\") throw new TypeError(fn + \" is not a function\");\n                var C = getSpecies(this);\n                return new C(function(observer) {\n                    return _this3.subscribe({\n                        next: function(value) {\n                            try {\n                                if (!fn(value)) return;\n                            } catch (e) {\n                                return observer.error(e);\n                            }\n                            observer.next(value);\n                        },\n                        error: function(e) {\n                            observer.error(e);\n                        },\n                        complete: function() {\n                            observer.complete();\n                        }\n                    });\n                });\n            }\n        },\n        {\n            key: \"reduce\",\n            value: function reduce(fn) {\n                var _this4 = this;\n                if (typeof fn !== \"function\") throw new TypeError(fn + \" is not a function\");\n                var C = getSpecies(this);\n                var hasSeed = arguments.length > 1;\n                var hasValue = false;\n                var seed = arguments[1];\n                var acc = seed;\n                return new C(function(observer) {\n                    return _this4.subscribe({\n                        next: function(value) {\n                            var first = !hasValue;\n                            hasValue = true;\n                            if (!first || hasSeed) {\n                                try {\n                                    acc = fn(acc, value);\n                                } catch (e) {\n                                    return observer.error(e);\n                                }\n                            } else {\n                                acc = value;\n                            }\n                        },\n                        error: function(e) {\n                            observer.error(e);\n                        },\n                        complete: function() {\n                            if (!hasValue && !hasSeed) return observer.error(new TypeError(\"Cannot reduce an empty sequence\"));\n                            observer.next(acc);\n                            observer.complete();\n                        }\n                    });\n                });\n            }\n        },\n        {\n            key: \"concat\",\n            value: function concat() {\n                var _this5 = this;\n                for(var _len = arguments.length, sources = new Array(_len), _key = 0; _key < _len; _key++){\n                    sources[_key] = arguments[_key];\n                }\n                var C = getSpecies(this);\n                return new C(function(observer) {\n                    var subscription;\n                    var index = 0;\n                    function startNext(next) {\n                        subscription = next.subscribe({\n                            next: function(v) {\n                                observer.next(v);\n                            },\n                            error: function(e) {\n                                observer.error(e);\n                            },\n                            complete: function() {\n                                if (index === sources.length) {\n                                    subscription = undefined;\n                                    observer.complete();\n                                } else {\n                                    startNext(C.from(sources[index++]));\n                                }\n                            }\n                        });\n                    }\n                    startNext(_this5);\n                    return function() {\n                        if (subscription) {\n                            subscription.unsubscribe();\n                            subscription = undefined;\n                        }\n                    };\n                });\n            }\n        },\n        {\n            key: \"flatMap\",\n            value: function flatMap(fn) {\n                var _this6 = this;\n                if (typeof fn !== \"function\") throw new TypeError(fn + \" is not a function\");\n                var C = getSpecies(this);\n                return new C(function(observer) {\n                    var subscriptions = [];\n                    var outer = _this6.subscribe({\n                        next: function(value) {\n                            if (fn) {\n                                try {\n                                    value = fn(value);\n                                } catch (e) {\n                                    return observer.error(e);\n                                }\n                            }\n                            var inner = C.from(value).subscribe({\n                                next: function(value) {\n                                    observer.next(value);\n                                },\n                                error: function(e) {\n                                    observer.error(e);\n                                },\n                                complete: function() {\n                                    var i = subscriptions.indexOf(inner);\n                                    if (i >= 0) subscriptions.splice(i, 1);\n                                    completeIfDone();\n                                }\n                            });\n                            subscriptions.push(inner);\n                        },\n                        error: function(e) {\n                            observer.error(e);\n                        },\n                        complete: function() {\n                            completeIfDone();\n                        }\n                    });\n                    function completeIfDone() {\n                        if (outer.closed && subscriptions.length === 0) observer.complete();\n                    }\n                    return function() {\n                        subscriptions.forEach(function(s) {\n                            return s.unsubscribe();\n                        });\n                        outer.unsubscribe();\n                    };\n                });\n            }\n        },\n        {\n            key: SymbolObservable,\n            value: function() {\n                return this;\n            }\n        }\n    ], [\n        {\n            key: \"from\",\n            value: function from(x) {\n                var C = typeof this === \"function\" ? this : Observable;\n                if (x == null) throw new TypeError(x + \" is not an object\");\n                var method = getMethod(x, SymbolObservable);\n                if (method) {\n                    var observable = method.call(x);\n                    if (Object(observable) !== observable) throw new TypeError(observable + \" is not an object\");\n                    if (isObservable(observable) && observable.constructor === C) return observable;\n                    return new C(function(observer) {\n                        return observable.subscribe(observer);\n                    });\n                }\n                if (hasSymbol(\"iterator\")) {\n                    method = getMethod(x, SymbolIterator);\n                    if (method) {\n                        return new C(function(observer) {\n                            enqueue(function() {\n                                if (observer.closed) return;\n                                var _iteratorNormalCompletion = true;\n                                var _didIteratorError = false;\n                                var _iteratorError = undefined;\n                                try {\n                                    for(var _iterator = method.call(x)[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){\n                                        var _item = _step.value;\n                                        observer.next(_item);\n                                        if (observer.closed) return;\n                                    }\n                                } catch (err) {\n                                    _didIteratorError = true;\n                                    _iteratorError = err;\n                                } finally{\n                                    try {\n                                        if (!_iteratorNormalCompletion && _iterator.return != null) {\n                                            _iterator.return();\n                                        }\n                                    } finally{\n                                        if (_didIteratorError) {\n                                            throw _iteratorError;\n                                        }\n                                    }\n                                }\n                                observer.complete();\n                            });\n                        });\n                    }\n                }\n                if (Array.isArray(x)) {\n                    return new C(function(observer) {\n                        enqueue(function() {\n                            if (observer.closed) return;\n                            for(var i = 0; i < x.length; ++i){\n                                observer.next(x[i]);\n                                if (observer.closed) return;\n                            }\n                            observer.complete();\n                        });\n                    });\n                }\n                throw new TypeError(x + \" is not observable\");\n            }\n        },\n        {\n            key: \"of\",\n            value: function of() {\n                for(var _len2 = arguments.length, items = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){\n                    items[_key2] = arguments[_key2];\n                }\n                var C = typeof this === \"function\" ? this : Observable;\n                return new C(function(observer) {\n                    enqueue(function() {\n                        if (observer.closed) return;\n                        for(var i = 0; i < items.length; ++i){\n                            observer.next(items[i]);\n                            if (observer.closed) return;\n                        }\n                        observer.complete();\n                    });\n                });\n            }\n        },\n        {\n            key: SymbolSpecies,\n            get: function() {\n                return this;\n            }\n        }\n    ]);\n    return Observable;\n}();\nexports.Observable = Observable;\nif (hasSymbols()) {\n    Object.defineProperty(Observable, Symbol(\"extensions\"), {\n        value: {\n            symbol: SymbolObservable,\n            hostReportError: hostReportError\n        },\n        configurable: true\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/zen-observable/lib/Observable.js\n");

/***/ })

};
;