(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[405],{5557:function(n,e,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return t(4038)}])},1809:function(n,e,t){"use strict";var i=t(5152),o=t.n(i);let r=o()(()=>t.e(523).then(t.bind(t,4523)),{loadableGenerated:{webpack:()=>[4523]},ssr:!1});e.Z=r},4038:function(n,e,t){"use strict";t.r(e),t.d(e,{__N_SSG:function(){return nn},default:function(){return Home}});var i=t(2729),o=t(5893),r=t(9521),a=t(7421),l=t(5675),c=t.n(l),s=t(3071);t(279);var p=t(785),d=t(7915),m=t(4871),f=t(1664),u=t.n(f),h=t(4218),g=t(4256),x=t(5158);function _templateObject(){let n=(0,i._)(["\n  padding: 0 var(--border-space);\n  position: relative;\n  margin-top: 40px;\n  margin-bottom: 80px;\n  display: flex;\n  flex-direction: column;\n\n  @media "," {\n    flex-direction: row;\n    margin-bottom: 164px;\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(['\n  position: relative;\n  top: 0;\n  display: block;\n\n  .fp-date {\n    position: relative;\n    font-family: "Lora", serif;\n    font-size: 14px;\n    font-weight: 400;\n    color: #888888;\n    margin-top: 0;\n    margin-bottom: 16px;\n  }\n\n  .fp-cover {\n    position: relative;\n    height: 220px;\n    background-color: black;\n  }\n\n  .fp-author {\n    color: #888888;\n    font-family: "Novela", serif;\n    font-style: italic;\n    font-weight: 400;\n    font-size: 14px;\n    margin-top: 12px;\n  }\n\n  .fp-title {\n    font-family: "Stelvio", serif;\n    line-height: 110%;\n    font-weight: 400;\n    font-size: 30px;\n    margin-top: 12px;\n    margin-bottom: 0;\n    width: 95%;\n    color: #161616;\n  }\n\n  .fp-topics {\n    position: relative;\n    display: flex;\n    flex-direction: row;\n    align-items: start;\n    justify-content: start;\n    flex-wrap: wrap;\n    margin-top: 16px;\n    height: 32px;\n    width: 100%;\n    overflow: hidden;\n    gap: 8px;\n  }\n\n  .fp-lead {\n    font-size: 15px;\n    color: #888;\n    margin-top: 24px;\n  }\n\n  @media '," {\n    position: sticky;\n    width: 50%;\n    height: 100vh;\n    margin-right: 32px;\n\n    .fp-date {\n      font-size: 18px;\n    }\n\n    .fp-cover {\n      height: 430px;\n    }\n\n    .fp-author {\n      margin-top: 24px;\n      font-size: 18px;\n    }\n\n    .fp-title {\n      font-size: 46px;\n      margin-top: 24px;\n    }\n\n    .fp-lead {\n      max-width: 90%;\n      font-weight: 400;\n      margin-top: 24px;\n      font-size: 17px;\n      color: #888;\n      overflow: hidden;\n      display: -webkit-box;\n      -webkit-line-clamp: 3;\n      line-clamp: 3;\n      -webkit-box-orient: vertical;\n    }\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  display: inline;\n  @media "," {\n    //margin-left: 16px;\n    //width: 50%;\n  }\n  @media "," {\n    width: 50%;\n  }\n"]);return _templateObject2=function(){return n},n}function SectionArticles(n){let{data:e}=n,{posts:t}=e,{lead:i}=(0,p.fw)(t[0].modules),r=t[0].image&&(0,s.k)(t[0].image);return t.length?(0,o.jsxs)(j,{children:[(0,o.jsxs)(v,{children:[(0,o.jsx)(u(),{href:(0,m.qt)(t[0]),children:(0,o.jsx)("div",{className:"fp-cover",children:r&&(0,o.jsx)(c(),{src:r,fill:!0,priority:!0,style:b.fpImage,sizes:"80vw",alt:(null==r?void 0:r.alternativeText)||""})})}),(0,o.jsx)("p",{className:"fp-author",children:t[0].author.fullName}),(0,o.jsx)(u(),{href:(0,m.qt)(t[0]),children:(0,o.jsx)("h3",{className:"fp-title primary-hover",children:t[0].title})}),i&&(0,o.jsx)("p",{className:"fp-lead",children:(0,x.Gq)((0,x.Kd)(i.content))})]}),(0,o.jsxs)(w,{children:[t.slice(1,10).map((n,e)=>(0,o.jsx)(d.Zs,{post:n,options:{showLead:!1,showDate:!1,showTopics:!0}},e)),(0,o.jsx)(g.Ty,{text:"Tous nos articles",link:"/recherche"})]})]}):null}let b={fpImage:{objectFit:"cover"}},j=r.ZP.section.withConfig({componentId:"sc-385c5ea2-0"})(_templateObject(),a.U.desktop),v=r.ZP.div.withConfig({componentId:"sc-385c5ea2-1"})(_templateObject1(),a.U.desktop),w=r.ZP.div.withConfig({componentId:"sc-385c5ea2-2"})(_templateObject2(),a.U.tablet,a.U.desktop);var _=t(8436);function SectionTopics_templateObject(){let n=(0,i._)(["\n  position: relative;\n  grid-column: 1/13;\n  grid-row: 1;\n\n  .topic-head {\n    position: relative;\n    border-top: 1px solid #161616;\n    border-bottom: 1px solid #161616;\n    padding-top: 10px;\n    padding-bottom: 2px;\n    margin-bottom: 60px;\n    margin-top: 90px;\n    \n    &:hover {\n      * {\n        color: var(--c-brand-light);\n      }\n    }\n  }\n\n  .topic-title {\n    margin: 0;\n    font-size: 22px;\n    font-weight: 500;\n\n    &::after {\n      margin: 0;\n      position: absolute;\n      font-weight: 400;\n      top: 10px;\n      right: 0;\n      content: '→';\n      color: black;\n      font-size: 22px;\n    }\n  }\n\n  @media "," {\n    grid-column: 1/7;\n\n    .topic-head {\n      width: 100%;\n      border-top: 1px solid #161616;\n      border-bottom: 1px solid #161616;\n      padding-top: 14px;\n      padding-bottom: 4px;\n      margin-bottom: 60px;\n      margin-top: 90px;\n    }\n\n    .topic-title {\n      margin: 0;\n      font-size: 32px;\n      font-weight: 500;\n      letter-spacing: 2%;\n      color: #242424;\n\n      &::after {\n        margin: 0;\n        position: absolute;\n        font-weight: 400;\n        top: 14px;\n        right: 0;\n        content: '→';\n        color: black;\n        font-size: 32px;\n      }\n    }\n\n  }\n"]);return SectionTopics_templateObject=function(){return n},n}function RenderTopic(n){let{topic:e}=n,{name:t,posts:i}=e;return(0,o.jsxs)("div",{className:"topic-wrapper",children:[(0,o.jsx)(u(),{href:"/categories/".concat((0,x.lV)(t)),children:(0,o.jsx)("div",{className:"topic-head",children:(0,o.jsx)("h2",{className:"topic-title",children:t})})}),i.slice().reverse().map((n,e)=>(0,o.jsx)(_.Z,{post:n,options:{showTopics:!0,showDate:!1,showLead:!1}},e))]})}function SectionTopics(n){let{data:e}=n;return(0,o.jsx)(O,{children:e.map((n,e)=>(0,o.jsx)(RenderTopic,{topic:n},e))})}let O=r.ZP.section.withConfig({componentId:"sc-a932684-0"})(SectionTopics_templateObject(),a.U.desktop);var k=t(6368);function SectionHeader_templateObject(){let n=(0,i._)(["\n  position: relative;\n  margin-left: ",';\n  margin-bottom: var(--fluid-space-m);\n\n  p {\n    font-size: 16px;\n    font-weight: 400;\n    font-family: Switzer, "Helvetica Neue", Helvetica, sans-serif;\n    color: ',";\n    margin-bottom: 8px;\n  }\n  \n  @media "," {\n    p {\n      font-size: 18px;\n    }\n  }\n  \n  @media "," {\n    p {\n      font-size: 20px;\n    }\n  }\n"]);return SectionHeader_templateObject=function(){return n},n}function SectionHeader(n){let{title:e,subTitle:t,supTitle:i,light:r,margin:a}=n;return(0,o.jsxs)(S,{margin:a,className:"section-header",light:r,children:[i&&(0,o.jsx)("p",{children:i}),(0,o.jsx)(k.NZ,{light:r,children:e}),t&&(0,o.jsx)("p",{children:t})]})}let S=r.ZP.div.withConfig({componentId:"sc-6dd2ed14-0"})(SectionHeader_templateObject(),n=>n.margin?"var(--border-space)":0,n=>n.light?"rgba(250,247,243,0.48)":"rgba(22,22,22,0.48)",a.U.tablet,a.U.desktop);var z=t(3365),C=t(1163);function SliderCard_templateObject(){let n=(0,i._)([",\n  position: relative;\n  list-style: none;\n  min-width: 292px;\n  padding-left: 16px;\n  overflow-y: visible;\n\n  &:last-child {\n    margin-right: 24px;\n  }\n  \n  &:first-child {\n    min-width: calc(292px - 16px + var(--border-space));\n    padding-left: var(--border-space);\n  }\n\n  @media "," {\n    min-width: calc((100% - 24px * 2) / 3);\n    padding: 0 !important;\n    margin-right: 24px;\n\n    &:last-child {\n      margin-right: 0;\n    }\n\n    &:first-child {\n      min-width: calc((100% - 24px * 2) / 3);\n      padding-left: 40px;\n    }\n  }\n\n  @media (min-width: 1220px) {\n    min-width: calc((100% - 26px * 3) / 4);\n\n    &:first-child {\n      min-width:  calc((100% - 26px * 3) / 4);\n      padding-left: 40px;\n    }\n  }\n\n"]);return SliderCard_templateObject=function(){return n},n}function SliderCard_templateObject1(){let n=(0,i._)(["\n  position: relative;\n  overflow-y: visible;\n  width: 100%;\n  border: 1px solid ","; // TODO: var here\n\n  .clickable-area {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 100%;\n    width: 100%;\n    z-index: 1;\n\n    &:hover {\n      cursor: pointer;\n      border: 1px solid var(--c-brand-lighter);\n      -webkit-box-shadow: 0 0 0 1px var(--c-brand-ligher);\n      box-shadow: 0 0 0 1px var(--c-brand-lighter);\n    }\n    \n  }\n\n  .sc-image {\n    position: relative;\n    width: 100%;\n    aspect-ratio: 16/9;\n    background-color: taransparent;\n  }\n\n  .sc-text {\n    padding: clamp(1.5rem, 0.27rem + 1.92vw, 2rem);\n  }\n\n  .sc-title {\n    color: ",";\n    margin: 0;\n    font-weight: ",";\n    height: clamp(4.5rem, 2.65rem + 2.88vw, 5.25rem);\n    font-family: Switzer, sans-serif;\n    font-size: clamp(1.125rem, 0.82rem + 0.48vw, 1.25rem);\n    line-height: clamp(1.5rem, 0.88rem + 0.96vw, 1.75rem);\n    display: -webkit-box;\n    -webkit-line-clamp: 3;\n    -webkit-box-orient: vertical;\n    text-overflow: ellipsis;\n    overflow: hidden;\n  }\n\n  .sc-footer {\n    position: relative;\n    display: flex;\n    flex-wrap: wrap;\n    align-items: center;\n    margin-top: clamp(3.5rem, 1.03rem + 3.85vw, 4.5rem);\n\n    z-index: 2;\n\n    &:hover {\n      .sc-author {\n        color: var(--c-brand-light);\n      }\n    }\n  }\n\n  .sc-author_image {\n    position: relative;\n    overflow: hidden;\n    border-radius: 90px;\n    width: clamp(2rem, 0.77rem + 1.92vw, 2.5rem);\n    height: clamp(2rem, 0.77rem + 1.92vw, 2.5rem);\n    margin-right: 12px;\n    background-color: ",";\n\n    img {\n      filter: ",";\n    }\n  }\n\n  .sc-more {\n    flex-wrap: wrap;\n    display: flex;\n    flex-direction: column;\n    margin-top: -2px;\n    gap: 4px;\n\n    .sc-author,\n    .sc-date {\n      margin: 0;\n      font-size: clamp(0.875rem, 0.56rem + 0.48vw, 1rem);\n      font-family: Switzer, sans-serif;\n    }\n\n    .sc-author {\n      color: ",";\n      font-weight: 400;\n    }\n\n    .sc-date {\n      margin-top: -2px;\n      color: #989AA4;\n    }\n  }\n\n  &:active {\n    border: 1px solid var(--c-brand-lighter);\n  }\n\n  //transition: all 250ms ease-out;\n\n  @media "," {\n    margin: 2px 2px;\n  }\n"]);return SliderCard_templateObject1=function(){return n},n}function SliderCard(n){let{post:e,options:t}=n;if((0,C.useRouter)(),e.route)return(0,o.jsx)(P,{theme:null==t?void 0:t.theme,children:(0,o.jsxs)(N,{className:"sc-inner",theme:null==t?void 0:t.theme,invert:null==t?void 0:t.invert,children:[(0,o.jsx)("div",{className:"sc-image",children:(0,o.jsx)(c(),{src:e.image,style:y,fill:!0,alt:"",sizes:"(max-width: 768px) 80vw, (max-width: 1220px) 33vw, 20vw"})}),(0,o.jsx)(u(),{href:e.route,children:(0,o.jsx)("div",{className:"clickable-area"})}),(0,o.jsxs)("div",{className:"sc-text",children:[(0,o.jsx)("h2",{className:"sc-title",children:e.title}),(0,o.jsx)(u(),{href:e.authorLink||"",children:(0,o.jsxs)("div",{className:"sc-footer",children:[(0,o.jsx)("div",{className:"sc-author_image",children:e.authorImage&&(0,o.jsx)(c(),{src:e.authorImage,fill:!0,alt:"",style:y,sizes:"40px"})}),(0,o.jsxs)("div",{className:"sc-more",children:[(0,o.jsx)("p",{className:"sc-author",children:e.author}),(0,o.jsx)("p",{className:"sc-date",children:e.date})]})]})})]})]})})}let y={objectFit:"cover"},P=r.ZP.li.withConfig({componentId:"sc-aa6de393-0"})(SliderCard_templateObject(),a.U.desktop),N=r.ZP.div.withConfig({componentId:"sc-aa6de393-1"})(SliderCard_templateObject1(),n=>"dark"===n.theme?"rgba(250,247,243,0.2)":"rgba(22,22,22,0.2)",n=>"dark"===n.theme?"var(--c-soft-cream)":"#161616",n=>"dark"===n.theme?400:500,n=>n.invert?"var(--c-soft-cream)":"#161616",n=>n.invert?"invert()":"none",n=>"dark"===n.theme?"#FAF7F3":"#161616",a.U.desktop);var Z=t(7294);function Carousel_templateObject(){let n=(0,i._)(["\n  position: relative;\n  width: 100%;\n  overflow-y: visible;\n  @media "," {\n    padding: 0 var(--border-space);\n  }\n"]);return Carousel_templateObject=function(){return n},n}function Carousel_templateObject1(){let n=(0,i._)(["\n  display: flex;\n  flex-direction: row;\n  overflow-x: scroll;\n  scroll-snap-type: x mandatory;\n  overflow-y: visible;\n  margin-bottom: 24px;\n\n  > * {\n    scroll-snap-align: start;\n  }\n  \n  &::-webkit-scrollbar {\n    display: none;\n  }\n  -ms-overflow-style: none;  /* IE and Edge */\n  scrollbar-width: none;\n"]);return Carousel_templateObject1=function(){return n},n}function Carousel_templateObject2(){let n=(0,i._)(["\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  gap: 12px;\n  padding: 0 var(--border-space);\n\n  @media "," {\n    padding: 0;\n  }\n"]);return Carousel_templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  display: flex;\n  justify-content: right;\n  align-items: center;\n  gap: 12px;\n"]);return _templateObject3=function(){return n},n}function Carousel(n){let{options:e,children:t}=n,i=(0,Z.useRef)(null),[r,a]=(0,Z.useState)(0),[l,c]=(0,Z.useState)([!1,!0]);function move(n,e){var t;let o;a(r+e),t=r+e,o=Math.round(8/Math.trunc(i.current.clientWidth/280)),0===t?c([!1,!0]):t>=o-1?c([!0,!1]):c([!0,!0]),n.preventDefault(),i.current.scrollBy({left:i.current.clientWidth*e,top:0,behavior:"smooth"})}return(0,o.jsxs)(I,{children:[(0,o.jsx)(U,{ref:i,onScroll:n=>{let e;(e=i.current.scrollLeft)+50>=i.current.scrollLeftMax?c([!0,!1]):e<=50?c([!1,!0]):c([!0,!0])},children:t}),(0,o.jsxs)(M,{children:[(0,o.jsx)("div",{children:(null==e?void 0:e.seeMoreText)&&(0,o.jsx)(g.Ty,{text:e.seeMoreText,link:e.seeMoreUrl,theme:"light"})}),(0,o.jsxs)(T,{children:[(0,o.jsx)(g.Z_,{disabled:!l[0],theme:(null==e?void 0:e.theme)||"dark",reverse:!0,onClickFunction:n=>move(n,-1)}),(0,o.jsx)(g.Z_,{disabled:!l[1],theme:(null==e?void 0:e.theme)||"dark",reverse:!1,onClickFunction:n=>move(n,1)})]})]})]})}let I=r.ZP.div.withConfig({componentId:"sc-61385040-0"})(Carousel_templateObject(),a.U.desktop),U=r.ZP.div.withConfig({componentId:"sc-61385040-1"})(Carousel_templateObject1()),M=r.ZP.div.withConfig({componentId:"sc-61385040-2"})(Carousel_templateObject2(),a.U.desktop),T=r.ZP.div.withConfig({componentId:"sc-61385040-3"})(_templateObject3());function SectionBloggers_templateObject(){let n=(0,i._)(["\n  padding: 56px 0;\n  @media "," {\n    padding: 128px 0;\n  }\n  @media "," {\n    padding: 164px 0;\n  }\n  .section-header {\n    margin-left: var(--border-space);\n  }\n"]);return SectionBloggers_templateObject=function(){return n},n}function SectionBloggers(n){let{data:e}=n,{posts:t}=e;return(0,o.jsxs)(D,{children:[(0,o.jsx)(SectionHeader,{supTitle:"Derniers articles de nos",title:"Blogueurs"}),(0,o.jsx)(Carousel,{options:{theme:"light",seeMoreUrl:"/recherche?type=Article&page=1",seeMoreText:"Voir tout"},children:t.sort(z.df).map((n,e)=>(function(n,e){if(!n.blog)return null;let t={title:n.title,author:n.blog.blogger.fullName,authorLink:"/blog/"+n.blog.blogger.slug,image:(0,s.k)(n.image),authorImage:(0,s.k)(n.blog.blogger.picture),date:(0,h.S$)(n.published_at),route:(0,m.qt)(n)};return(0,o.jsx)(SliderCard,{post:t,options:{theme:"light"}},e)})(n,e))})]})}let D=r.ZP.section.withConfig({componentId:"sc-90781d20-0"})(SectionBloggers_templateObject(),a.U.tablet,a.U.desktop);function SectionPodcasts_templateObject(){let n=(0,i._)(["\n  position: relative;\n  background-color: var(--blue-dark);\n  padding: 56px 0 0 0;\n  @media "," {\n    padding: 128px 0 0 0 ;\n  }\n  @media "," {\n    padding: 164px 0 0 0;\n  }\n"]);return SectionPodcasts_templateObject=function(){return n},n}function SectionPodcasts(n){let{data:e}=n,{posts:t}=e;return t=function(n){let e,t={},i=[],o=0;for(let e of n)t[e.author]?new Date(t[e.author].published_at)<new Date(e.published_at)&&(t[e.author]=e,t[e.author].index=o):t[e.author]=e,o++;for(let[e,o]of Object.entries(t))i.push(t[e]),delete n[o.index];return i=i.sort(z.df),e=n.sort(z.df).splice(0,4),i.concat(e)}(t=t.map(n=>(function(n){let{podcast:e}=(0,p.fw)(n.modules);return{title:n.title,author:e.podcast.name,authorLink:"/podcasts/"+e.podcast.slug,image:(0,s.k)(n.image),authorImage:(0,s.k)(e.podcast.logoSmall),date:(0,h.S$)(n.published_at),published_at:n.published_at,route:(0,m.qt)(n)}})(n))),(0,o.jsxs)(F,{children:[(0,o.jsx)(SectionHeader,{title:"Podcasts",supTitle:"Derniers \xe9pisodes",margin:!0,light:!0}),(0,o.jsx)(Carousel,{options:{seeMoreUrl:"/recherche?type=Podcast&page=1",seeMoreText:"Voir tout"},children:t.map((n,e)=>n.author?(0,o.jsx)(SliderCard,{post:n,options:{theme:"dark",invert:!0}},e):null)})]})}let F=r.ZP.section.withConfig({componentId:"sc-85dd2016-0"})(SectionPodcasts_templateObject(),a.U.tablet,a.U.desktop);var L=t(131),R=t(4629);function SectionMission_templateObject(){let n=(0,i._)(["\n  position: relative;\n  top: 0;\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  column-gap: 24px;\n  padding: 0 var(--border-space);\n  overflow: hidden;\n  height: 100vh;\n  max-height: 900px;\n\n  @media "," {\n    align-items: center;\n  }\n"]);return SectionMission_templateObject=function(){return n},n}function SectionMission_templateObject1(){let n=(0,i._)(['\n  grid-column: 1/3;\n  margin-top: 48px;\n  margin-bottom: 56px;\n  width: 100%;\n  z-index: 1;\n\n  .mission-desc {\n    color: var(--soft-white);\n    margin-top: 20px;\n    font-size: 20px;\n    font-family: "Switzer", serif;\n    margin-bottom: 48px;\n    z-index: 2;\n  }\n\n  @media '," {\n    font-size: 22px;\n    .mission-desc {\n      margin-top: 22px;\n      font-size: 22px;\n    }\n  }\n  @media "," {\n    grid-column: 1/2;\n    margin-top: 126px;\n    margin-bottom: 164px;\n    .mission-desc {\n      margin-top: 20px;\n      font-size: 20px;\n    }\n  }\n"]);return SectionMission_templateObject1=function(){return n},n}function SectionMission_templateObject2(){let n=(0,i._)(["\n\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  background-color: #F3673B;\n  z-index: 0;\n\n  @media "," {\n    left: -50%;\n    width: 200%;\n    height: auto;\n    top: -200%;\n    transform: ",";\n    aspect-ratio: 1/1;\n    border-radius: 99999px;\n    transform-origin: center;\n    transition: all 1.5s ease-out;\n  }\n\n"]);return SectionMission_templateObject2=function(){return n},n}function SectionMission(n){let{data:e}=n,{partOne:t,partTwo:i}=e,r=(0,Z.useRef)(),[a,l]=(0,L.YD)();return(0,o.jsxs)(B,{ref:a,inView:l,children:[(0,o.jsx)(q,{inView:l}),(0,o.jsxs)(H,{ref:r,children:[(0,o.jsx)(SectionHeader,{supTitle:"Notre",title:"Mission",light:!0}),(0,o.jsx)("p",{className:"mission-desc",children:(0,x.Gq)((0,x.Kd)(t||""))}),(0,o.jsx)("div",{children:(0,o.jsx)(R.Z,{theme:"light",text:"En savoir plus",link:"/a-propos"})})]})]})}let B=r.ZP.div.withConfig({componentId:"sc-93ceda0f-0"})(SectionMission_templateObject(),a.U.tablet),H=r.ZP.div.withConfig({componentId:"sc-93ceda0f-1"})(SectionMission_templateObject1(),a.U.tablet,a.U.desktop),q=r.ZP.div.withConfig({componentId:"sc-93ceda0f-2"})(SectionMission_templateObject2(),a.U.desktop,n=>n.inView?"scale(1) translateY(25%)":"scale(0.2) translateY(0)");var A=t(1809);function SectionMostRead_templateObject(){let n=(0,i._)(["\n  position: relative;\n  margin-top: 60px;\n  margin-bottom: 60px;\n  grid-column: 1/12;\n  height: auto;\n  @media "," {\n    grid-column: 9/13;\n  }\n\n  .ck-form-wrapper {\n    position: relative;\n    width: 100%;\n    min-height: 400px;\n    background-color: indianred;\n  }\n"]);return SectionMostRead_templateObject=function(){return n},n}function SectionMostRead_templateObject1(){let n=(0,i._)(['\n  display: block;\n  font-size: 22px;\n  background-color: #161616;\n  padding: 24px 24px 24px 24px;\n  margin-bottom: 1px;\n\n  .title {\n    line-height: 115%;\n    margin-top: 16px;\n    margin-bottom: 0;\n    color: #dcdcdc;\n  }\n\n  .author {\n    margin: 0;\n    font-family: "Lora", serif;\n    font-style: italic;\n    font-size: 15px;\n    color: #888;\n  }\n']);return SectionMostRead_templateObject1=function(){return n},n}function SectionMostRead(n){let{data:e,newsletter:t}=n,{name:i,posts:r}=e;return(0,o.jsxs)(E,{children:[(0,o.jsx)(A.Z,{title:"",formString:t}),(0,o.jsx)("h2",{children:i}),r.map((n,e)=>(0,o.jsx)(u(),{href:(0,m.qt)(n),children:(0,o.jsxs)(G,{children:[(0,o.jsx)("p",{className:"author",children:n.author.fullName}),(0,o.jsx)("p",{className:"title",children:n.title})]})},e))]})}let E=r.ZP.div.withConfig({componentId:"sc-ac6d5080-0"})(SectionMostRead_templateObject(),a.U.desktop),G=r.ZP.div.withConfig({componentId:"sc-ac6d5080-1"})(SectionMostRead_templateObject1());var V=t(1304);function LargeCard_templateObject(){let n=(0,i._)(["\n  position: relative;\n  width: 100%;\n  border: 1px solid ","; // TODO: var here\n  \n  .lc-image {\n    position: relative;\n    width: 100%;\n    aspect-ratio: 16/9;\n    overflow: hidden;\n    background-color: #F9F1E6;\n    background-image: url(/images/tpsg-logo.svg);\n    background-repeat: no-repeat;\n    background-position: center;\n  }\n  \n  .lc-text {\n    display: flex;\n    height: 360px;\n    flex-direction: column;\n    justify-content: space-between;\n    padding: clamp(1.5rem, 0.112rem + 2.98vw, 2.5rem);\n  }\n  \n  .lc-title {\n    position: relative;\n    color: ",";\n    font-family: Stelvio, sans-serif;\n    font-weight: 400;\n    font-size: clamp(1.75rem, 1.4029850746268657rem + 0.7462686567164178vw, 2rem);\n    line-height: clamp(2rem, 1.4794776119402986rem + 1.1194029850746268vw, 2.375rem);\n    margin-bottom: -4px;\n    margin-top: 6px;\n  }\n  \n  .lc-lead {\n    margin-top: 12px;\n    font-family: Switzer, sans-serif;\n    font-size: clamp(0.875rem, 0.53rem + 0.75vw, 1.125rem);\n    line-height: clamp(1.375rem, 0.8544776119402986rem + 1.1194029850746268vw, 1.75rem);\n    color: #989AA4;\n    display: -webkit-box;\n    -webkit-line-clamp: 4;\n    font-weight: 400;\n    -webkit-box-orient: vertical;\n    text-overflow: ellipsis;\n    overflow: hidden;\n  }\n  \n  // .lc-date {\n  //   font-family: Switzer, sans-serif;\n  //   font-size: 14px;\n  //   line-height: 20px;\n  //   color: #989AA4;\n  //   margin-bottom: 0;\n  //   text-transform: uppercase;\n  // }\n  \n  @media "," {\n    &:hover {\n      border-color: var(--brand-color);\n      -webkit-box-shadow: 0 0 0 1px var(--c-brand-ligher);\n      box-shadow: 0 0 0 1px var(--c-brand-lighter);\n      cursor: pointer;\n    }\n  }\n  \n"]);return LargeCard_templateObject=function(){return n},n}function LargeCard(n){let{post:e,theme:t}=n,i=(0,x.Kd)((0,x.Gq)(e.lead));return(0,o.jsx)(u(),{href:e.link,children:(0,o.jsxs)(K,{theme:t,children:[(0,o.jsx)("div",{className:"lc-image",children:(0,o.jsx)(V.Z,{imageData:e.image,preserveAspectRatio:!1,sizes:"(max-width: 768px) 100vw, (max-width: 1220px) 33vw, 25vw"})}),(0,o.jsx)("div",{className:"lc-text",children:(0,o.jsxs)("div",{className:"lc-content",children:[(0,o.jsx)("h3",{className:"lc-title",children:e.title}),(0,o.jsx)("p",{className:"lc-lead",children:i})]})})]})})}let K=r.ZP.div.withConfig({componentId:"sc-8db41f6-0"})(LargeCard_templateObject(),n=>"dark"===n.theme?"rgba(250,247,243,0.2)":"rgba(22,22,22,0.2)",n=>"dark"===n.theme?"#FFFFFF":"#161616",a.U.desktop);function SectionDouble_templateObject(){let n=(0,i._)(["\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  padding: 0 var(--border-space);\n  background-color: var(--blue-dark);\n  .animated-text-button {\n    margin-top: 24px;\n  }\n\n  @media "," {\n    gap: 16px;\n    flex-direction: row;\n    .section-item {\n      width: 50%;\n    }\n  }\n\n  @media "," {\n    gap: 24px;\n  }\n"]);return SectionDouble_templateObject=function(){return n},n}function SectionDouble_templateObject1(){let n=(0,i._)(["\n  @media "," {\n    margin: 0;\n  }\n"]);return SectionDouble_templateObject1=function(){return n},n}function SectionDouble_templateObject2(){let n=(0,i._)(["\n  height: 80px;\n"]);return SectionDouble_templateObject2=function(){return n},n}function SectionDouble_templateObject3(){let n=(0,i._)(["\n  height: 72px;\n  @media "," {\n    height: 96px;\n  }\n"]);return SectionDouble_templateObject3=function(){return n},n}function SectionDouble(n){let{webinars:e,formations:t}=n;if(e.posts.length<1||t.posts.length<1)return(0,o.jsx)(o.Fragment,{});function preparedPost(n){let{lead:e}=(0,p.fw)(n.modules);return{title:n.title,link:(0,m.qt)(n),author:n.author.fullName,image:n.image,date:(0,h.S$)(n.published_at),lead:(0,x.Gq)((0,x.Kd)((null==e?void 0:e.content)||""))}}return(0,o.jsxs)(W,{children:[(0,o.jsxs)("div",{className:"section-item",children:[(0,o.jsx)($,{}),(0,o.jsx)(SectionHeader,{title:"Webinaire",supTitle:"\xc0 voir ou \xe0 revoir",light:!0}),(0,o.jsx)(X,{children:(0,o.jsx)(LargeCard,{post:preparedPost(e.posts[0]),theme:"dark"})}),(0,o.jsx)(R.Z,{text:"Voir tout",theme:"light",link:"/webinaires"})]}),(0,o.jsxs)("div",{className:"section-item",children:[(0,o.jsx)($,{}),(0,o.jsx)(SectionHeader,{title:"Formation",supTitle:"Pour aller plus loin",light:!0}),(0,o.jsx)(X,{children:(0,o.jsx)(LargeCard,{post:preparedPost(t.posts[0]),theme:"dark"})}),(0,o.jsx)(R.Z,{text:"Voir tout",theme:"light",link:"/formations"}),(0,o.jsx)(Y,{})]})]})}let W=r.ZP.div.withConfig({componentId:"sc-f307deb0-0"})(SectionDouble_templateObject(),a.U.tablet,a.U.desktop),X=r.ZP.div.withConfig({componentId:"sc-f307deb0-1"})(SectionDouble_templateObject1(),a.U.desktop),Y=r.ZP.div.withConfig({componentId:"sc-f307deb0-2"})(SectionDouble_templateObject2()),$=r.ZP.div.withConfig({componentId:"sc-f307deb0-3"})(SectionDouble_templateObject3(),a.U.desktop);var Q=t(211),J=t(2962);function pages_templateObject(){let n=(0,i._)(["\n  width: 100vw;\n  //padding-right: 15px;\n  background-color: var(--soft-white);\n\n  .double-section {\n    background-color: var(--blue-dark);\n    display: flex;\n    flex-direction: column;\n\n    @media "," {\n      flex-direction: row;\n    }\n  }\n"]);return pages_templateObject=function(){return n},n}function pages_templateObject1(){let n=(0,i._)(["\n  display: grid;\n  grid-gap: 16px;\n  grid-template-columns: repeat(12, 1fr);\n"]);return pages_templateObject1=function(){return n},n}var nn=!0;function Home(n){let{home:e}=n;return(0,o.jsxs)(ne,{className:"page",children:[(0,o.jsx)(J.PB,{title:"ToutPourSaGloire.com",description:"ToutPourSaGloire.com c'est des milliers de ressources pour vous aider \xe0 mener une vie qui glorifie Dieu."}),e.articles&&(0,o.jsx)(SectionArticles,{data:e.articles}),e.featured.length>0&&(0,o.jsx)(Q.g4,{content:e.featured[0]}),e.mission&&(0,o.jsx)(SectionMission,{data:e.mission}),e.podcasts&&(0,o.jsx)(SectionPodcasts,{data:e.podcasts}),e.webinars&&e.formations&&(0,o.jsx)(SectionDouble,{webinars:e.webinars,formations:e.formations}),e.bloggers&&(0,o.jsx)(SectionBloggers,{data:e.bloggers}),(0,o.jsxs)(nt,{className:"site-padding",children:[e.topics&&(0,o.jsx)(SectionTopics,{data:e.topics}),e.mostRead&&(0,o.jsx)(SectionMostRead,{data:e.mostRead,newsletter:e.newsletter})]})]})}let ne=r.ZP.div.withConfig({componentId:"sc-dddf389e-0"})(pages_templateObject(),a.U.desktop),nt=r.ZP.div.withConfig({componentId:"sc-dddf389e-1"})(pages_templateObject1())},6368:function(n,e,t){"use strict";t.d(e,{DZ:function(){return u},GN:function(){return p},My:function(){return f},NZ:function(){return s},V1:function(){return l},X0:function(){return m},bP:function(){return c},hQ:function(){return h},kz:function(){return d}});var i=t(2729),o=t(9521),r=t(7421);function _templateObject(){let n=(0,i._)(["\n  position: relative;\n  font-weight: 500;\n  color: #161616;\n  font-size: 48px;\n  margin-top:  calc(var(--spacing-l) - 48px * ",");\n  margin-bottom: calc(var(--spacing-l) - 48px * ",");\n  \n  @media "," {\n    font-size: 72px;\n    margin-top:  calc(var(--spacing-l) - 72px * ",");\n    margin-bottom: calc(var(--spacing-l) - 72px * ",");\n  }\n  @media "," {\n    font-size: 104px;\n    margin-top:  calc(var(--spacing-l) - 104px * ",");\n    margin-bottom: calc(var(--spacing-l) - 104px * ",");\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  box-sizing: content-box;\n  color: #ececec;\n  margin-top: 0;\n  margin-bottom: 0;\n  font-size: clamp(24px, 1.125rem + 1vw, 32px);\n  font-weight: 400;\n  max-width: 600px;\n  line-height: 105%;\n  &:before {\n    display: block;\n    margin-bottom: 8px;\n    content: '","';\n    color: ",";\n    font-size: 18px;\n  }\n  @media "," {\n    font-size: 32px;\n    margin-right: 24px;\n  }\n  @media "," {\n    font-weight: 400;\n    margin-right: 48px;\n    font-size: clamp(32px, 3.5vw, 48px);\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(['\n  \n  --title-size: clamp(3.5rem, 0.03rem + 7.4vw, 6rem);\n  \n  font-family: Stelvio, "Helvetica Neue", Helvetica, sans-serif;\n  font-size: var(--title-size);\n  color: ',";\n  font-weight: 400;\n  margin-top: 0;\n  margin-bottom: calc( var(--title-size) * -",");\n  \n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  font-size: 24px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 48px;\n    margin-bottom: 16px;\n    margin-top: 16px;\n  }\n"]);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,i._)(["\n  font-size: 20px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media "," {\n    font-size: 30px;\n  }\n  span {\n    position: relative;\n    margin-left: 8px;\n    display: inline-block;\n    margin-bottom: -2px;\n    height: 22px;\n    width: 22px;\n  }\n"]);return _templateObject4=function(){return n},n}function _templateObject5(){let n=(0,i._)(["\n  font-size: 16px;\n  color: #888888;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  padding-right: 30px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media screen and (max-width:320px){ // Little screen only\n    padding: 0;\n  }\n  @media "," {\n    font-size:20px;\n  }\n"]);return _templateObject5=function(){return n},n}function _templateObject6(){let n=(0,i._)(['\n  font-size: 14px;\n  font-style: italic;\n  font-family: "Lora", Charter, Times, "Times New Roman", serif;\n  color: #7a7a7a;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media '," {\n    font-size: 17px;\n  }\n"]);return _templateObject6=function(){return n},n}function _templateObject7(){let n=(0,i._)(["\n  font-size: 38px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 64px;\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n  @media "," {\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n"]);return _templateObject7=function(){return n},n}function _templateObject8(){let n=(0,i._)(["\n  color: #161616;\n  letter-spacing: 0.04em;\n  margin-top: 48px;\n  text-transform: uppercase;\n  @media "," {\n    margin-top: 0;\n    margin-bottom: 0;\n    font-size: 22px;\n    font-weight: 500;\n    &:hover {\n      color: var(--brand-color);\n    }\n  }\n"]);return _templateObject8=function(){return n},n}function _templateObject9(){let n=(0,i._)(["\n  font-weight: 400;\n  font-size: 26px;\n  margin: 0;\n  color: #161616;\n  @media "," {\n    font-size: 32px;\n  }\n"]);return _templateObject9=function(){return n},n}let a={topSpace:.096,minBottomSpace:.2788,maxBottomSpace:.4711},l=o.ZP.h1.withConfig({componentId:"sc-a3af5335-0"})(_templateObject(),a.topSpace,a.minBottomSpace,r.U.tablet,a.topSpace,a.minBottomSpace,r.U.desktop,a.topSpace,a.minBottomSpace),c=o.ZP.h2.withConfig({componentId:"sc-a3af5335-1"})(_templateObject1(),n=>n.label,n=>n.color,r.U.tablet,r.U.desktop),s=o.ZP.h2.withConfig({componentId:"sc-a3af5335-2"})(_templateObject2(),n=>n.light?"var(--c-soft-cream)":"var(--soft-dark)",a.maxBottomSpace),p=o.ZP.h2.withConfig({componentId:"sc-a3af5335-3"})(_templateObject3(),r.U.tablet),d=o.ZP.h4.withConfig({componentId:"sc-a3af5335-4"})(_templateObject4(),r.U.tablet),m=o.ZP.p.withConfig({componentId:"sc-a3af5335-5"})(_templateObject5(),r.U.tablet),f=o.ZP.p.withConfig({componentId:"sc-a3af5335-6"})(_templateObject6(),r.U.tablet),u=o.ZP.h1.withConfig({componentId:"sc-a3af5335-7"})(_templateObject7(),r.U.tablet,r.U.desktop),h=o.ZP.p.withConfig({componentId:"sc-a3af5335-8"})(_templateObject8(),r.U.desktop);o.ZP.p.withConfig({componentId:"sc-a3af5335-9"})(_templateObject9(),r.U.tablet)}},function(n){n.O(0,[755,764,962,145,291,915,211,774,888,179],function(){return n(n.s=5557)}),_N_E=n.O()}]);